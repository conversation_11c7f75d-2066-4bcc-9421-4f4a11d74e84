#!/usr/bin/env python3
import re
import os

# Mapping of functions to their specific files
FUNCTION_TO_FILE = {
    'logChange': 'change-log-lite.js',  # Use lightweight version
    'logHistory': 'change-log.js',      # Keep full version for history
    'getJoin': 'fast-join.js',
    'findJoin': 'fast-join.js',
    'relate': 'relate/index.js',
    'AnyObj': 'types.js',
    '_get': 'dash-utils.js',
    '_set': 'dash-utils.js',
    '_flatten': 'dash-utils.js',
    '_pick': 'dash-utils.js',
    '_unset': 'dash-utils.js',
    'sanitize': 'sanitize/index.js',
    'plainTextSanitize': 'sanitize/index.js',
    'scrub': 'sanitize/index.js',
    'dollarString': 'simple.js',
    'fakeId': 'simple.js',
    'maybeObjectId': 'simple.js',
    'idConvert': 'simple.js',
    'errHook': 'simple.js',
    'geoQuery': 'geo/index.js',
    'pointToGeo': 'geo/index.js',
    'addSessionFp': 'ip-utils.js',
    'getClientIp': 'ip-utils.js',
    'ping': 'notifications/index.js',
    'sendPings': 'notifications/index.js',
    'resendPings': 'notifications/index.js',
    'Ping': 'notifications/index.js',
    'PingConfig': 'notifications/index.js',
    'encryptedFields': 'encryption/index.js',
    'fieldDecrypt': 'encryption/index.js',
    'deepEncrypt': 'encryption/index.js',
    'deepDecrypt': 'encryption/index.js',
    'handleDeepEncrypt': 'encryption/index.js',
    'encryptFields': 'encryption/index.js',
    'symmetricEncrypt': 'encryption/index.js',
    'checkExisting': 'common/checks.js',
    'limitData': 'common/checks.js',
    'excludeFields': 'common/checks.js',
    'getStateCode': 'common/states.js',
    'removeVectorStore': 'ai/index.js',
    'addFileToVectorStore': 'ai/index.js',
    'saveChatHistory': 'ai/index.js',
    'scrubUploads': 'file-join.js',
    'fileJoinHook': 'file-join.js',
    'genEd255519KeyPair': 'crypto/index.js',
    'publicKeyToDid': 'crypto/index.js'
}

def calculate_relative_path(from_file, to_file):
    """Calculate the relative path from one file to another"""
    from_dir = os.path.dirname(from_file)
    # Count directory levels to go up
    levels_up = from_dir.count('/') - 1  # -1 because we start from src/
    return '../' * levels_up + 'utils/' + to_file

def update_file_imports(filepath):
    """Update a single file's imports"""
    try:
        with open(filepath, 'r') as f:
            content = f.read()
        
        # Find all utils/index.js imports
        patterns = [
            r"import\s*{([^}]+)}\s*from\s*['\"]([^'\"]*utils/index(?:\.js)?)['\"];?",
            r"import\s*{([^}]+)}\s*from\s*['\"]([^'\"]*utils/index)['\"];?"
        ]
        
        updated = False
        for pattern in patterns:
            matches = list(re.finditer(pattern, content))
            
            for match in reversed(matches):  # Process in reverse to maintain positions
                imports_str = match.group(1)
                old_import_path = match.group(2)
                
                # Parse the imported functions
                imported_functions = [func.strip() for func in imports_str.split(',')]
                
                # Group functions by their target files
                file_groups = {}
                for func in imported_functions:
                    if func in FUNCTION_TO_FILE:
                        target_file = FUNCTION_TO_FILE[func]
                        relative_path = calculate_relative_path(filepath, target_file)
                        
                        if relative_path not in file_groups:
                            file_groups[relative_path] = []
                        file_groups[relative_path].append(func)
                    else:
                        print(f"Warning: Unknown function {func} in {filepath}")
                
                # Generate new import statements
                new_imports = []
                for target_path, functions in file_groups.items():
                    functions_str = ', '.join(functions)
                    new_imports.append(f"import {{{functions_str}}} from '{target_path}';")
                
                # Replace the old import with new imports
                new_import_block = '\n'.join(new_imports)
                content = content[:match.start()] + new_import_block + content[match.end():]
                updated = True
        
        if updated:
            with open(filepath, 'w') as f:
                f.write(content)
            print(f"Updated {filepath}")
            return True
        
        return False
        
    except Exception as e:
        print(f"Error updating {filepath}: {e}")
        return False

# Find all files that need updating
files_to_update = []
for root, dirs, files in os.walk('server/src'):
    for file in files:
        if file.endswith('.ts'):
            filepath = os.path.join(root, file)
            try:
                with open(filepath, 'r') as f:
                    content = f.read()
                    if 'utils/index' in content:
                        files_to_update.append(filepath)
            except:
                pass

print(f"Found {len(files_to_update)} files to update")

# Update all files
updated_count = 0
for filepath in files_to_update:
    if update_file_imports(filepath):
        updated_count += 1

print(f"Successfully updated {updated_count} files")
