import { useAuth } from 'src/stores/auth';
import {storeToRefs} from 'pinia';
import {computed, ref} from 'vue';
import {idGet} from 'src/utils/id-get.js';
import {usePpls} from 'stores/ppls.js';
import {useEnvStore} from 'stores/env';
import {useAtcStore} from 'src/stores/atc-store';


export const loginPerson = (opts?: { watch?:boolean }) => {
    const authStore = useAuth();
    const envStore = useEnvStore();

    const { user, isAuthenticated } = storeToRefs(authStore);
    const login = computed(() => user.value || {})
    const { item:person } = idGet({
        store: usePpls() as any,
        value: computed(() => user.value?.owner),
        params: ref({
            runJoin: { add_files: true }
        }),
        onWatch: opts?.watch ? (item:any) => {
           if(!item) return;
           if(envStore.url_context === 'public'){
                window.location.reload()
            }
        } : undefined,
        useAtcStore
    })
    return {
        pplStore: usePpls(),
        person,
        login,
        isAuthenticated,
    }
}

