<template>
  <div class="flex items-center" v-if="org?._id">
    <default-avatar :imageStyle="{ objectFit: 'contain' }" size-in="50px" dark :model-value="org" :use-atc-store="useAtcStore"></default-avatar>
    <div class="q-pl-md">
      <div class="text-xs tw-six">{{ org?.name }}</div>
      <slot name="title">
        <div class="text-grey-7 text-xxs tw-five font-7-8r">{{title}}</div>
      </slot>
    </div>
  </div>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  const props = defineProps({
    org: { required: true },
    title: { type: String }
  })
</script>

<style lang="scss" scoped>

</style>
