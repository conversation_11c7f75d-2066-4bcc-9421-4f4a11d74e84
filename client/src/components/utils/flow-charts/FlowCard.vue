<template>
  <div class="_fw q-pa-md">

    <div class="text-weight-bold">{{ item?.name }}</div>
    <div>{{ $possiblyPlural('Node', item?.nodes || []) }}</div>
    <div class="flex items-center">
      <q-btn flat dense small icon="mdi-pencil-circle" @click="dialog = true"></q-btn>
    </div>

    <common-dialog setting="full" v-model="dialog">
      <div class="_fw q-pa-md">
        <flow-form :model-value="item"></flow-form>
      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import {useFlowCharts} from 'src/stores/flow-charts';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useFlowCharts();
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {$possiblyPlural} from 'src/utils/global-methods';
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import FlowForm from 'src/components/utils/flow-charts/FlowForm.vue';

  const props = defineProps({
    modelValue: Object
  })

  const dialog = ref(false);

  const { item } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })
</script>

<style lang="scss" scoped>

</style>
