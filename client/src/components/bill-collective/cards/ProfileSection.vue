<template>
  <div class="__ps">
    <div class="_form_grid">
      <div class="_form_label">Your Name</div>
      <div class="q-pa-sm">
        <q-input dense borderless class="_inp mw500" placeholder="Type your name..." v-model="form.name" @update:model-value="autoSave('name')"></q-input>
      </div>
      <div class="_form_label">Your Email</div>
      <div class="q-pa-sm">
        <email-field dense hide-bottom-space borderless class="_inp mw500"  v-model="form.email" @update:model-value="autoSave('email')"></email-field>
      </div>
      <div class="_form_label">Your Phone</div>
      <div class="q-pa-sm">
        <phone-input :input-attrs="{ dense: true, filled: false, borderless: true, class: '_inp mw500' }" v-model="form.phone"
                     @update:model-value="autoSave('phone')"></phone-input>
      </div>


      <div class="_form_label">Household Income</div>
      <div class="q-pa-sm">
        <div class="text-xxs">We use this to find you 501(r) bill assistance and other aid opportunities</div>
        <money-input borderless dense class="mw200 _inp" :model-value="eraser.income || 0" @update:model-value="setEraser('income', $event)"></money-input>
      </div>
      <div class="_form_label">Household Size</div>
      <div class="q-pa-sm">
        <div class="flex items-center">
          <q-btn dense flat icon="mdi-menu-down" @click="incCount(-1)"></q-btn>
          <div class="tw-six font-1r q-pa-sm">{{form.hhCount || 1}}</div>
          <q-btn dense flat icon="mdi-menu-up" @click="incCount(1)"></q-btn>
        </div>
      </div>

      <div class="_form_label">Preferred Contact</div>
      <div class="q-pa-sm">
        <q-radio
            v-for="c in ['email', 'phone']"
            :key="`prf-${c}`"
            size="sm"
            :val="c"
            :label="$capitalizeFirstLetter(c)"
            v-model="eraserForm.prefContact"
            @update:model-value="eraserAutoSave('prefContact')"/>
      </div>

      <template v-if="eraser">
        <div class="_form_label">Relationship to patient</div>
        <div class="q-pa-sm">
          <q-radio
              v-for="t in [['self', 'Self'], ['spouse', 'Spouse/Partner'], ['guardian', 'Parent/Guardian']]"
              :key="`t-${t[0]}`"
              size="sm"
              :model-value="eraser.personRelationship"
              :val="t[0]"
              :label="t[1]"
              @update:model-value="setEraser('personRelationship', $event)"/>
        </div>
      </template>
      <div class="_form_label">Bills/Files</div>
      <div class="__ul">
        <upload-ui
            @update:display="display.push($event)"
            :div-attrs="{ class: '' }"
            :div-style="{ fontSize: 'var(--text-xs)', fontWeight: 600, height: '100%', width: '100%' }"
            title="Drop or select bills 📄"
            text-color="white"
            :upload="upload"
            multiple
            bg="transparent"
            allow-types="image,pdf"
        >
          <div class="__drop_zone">
            <div class="_fa flex items-center">
              <div>
                <div class="row items-center">
                  <q-icon size="20px" name="mdi-upload"></q-icon>
                  <div class="q-mx-sm text-xxs tw-eight">Drop or upload files</div>
                </div>
                <div class="row justify-center items-center">
                  <div class="__lock">
                    <q-icon class="_i_i" name="mdi-lock"></q-icon>
                    Your data is encrypted in transit and at rest.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </upload-ui>
        <div v-for="(item, i) in eraser?.files || []" :key="`item-${i}`" class="__raw">
          <div>
            <div class="__file">
              <file-type-handler
                  :div-attrs="{ style: 'height: 100%; width: 100%' }"
                  :imgStyle="{ height: '100%', width: '100%', objectFit: 'cover' }"
                  :url="item.url"
                  :file="item"
                  v-if="item.info?.type"
              ></file-type-handler>
            </div>
          </div>
          <div class="font-1r tw-five">{{ item.meta?.name }}</div>
          <remove-proxy-btn @remove="removeEraserFile(i)" dense flat icon="mdi-close" color="red"></remove-proxy-btn>
        </div>
        <div v-for="(item, i) in rawFiles" :key="`item-${i}`" class="__raw">
          <div>
            <div class="__file">
              <file-type-handler
                  :div-attrs="{ style: 'height: 100%; width: 100%' }"
                  :imgStyle="{ height: '100%', width: '100%', objectFit: 'cover' }"
                  :url="display[i]"
                  :file="item.file"
                  v-if="item.meta?.type"
              ></file-type-handler>
            </div>
          </div>
          <div class="font-1r tw-five">{{ item.meta?.name }}</div>
          <q-btn @click="removeFile(i)" dense flat icon="mdi-close" color="red"></q-btn>
        </div>
      </div>
    </div>
    <common-dialog v-model="authDialog" setting="smmd">
      <auth-card bordered></auth-card>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import AuthCard from 'components/auth/AuthCard.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import UploadUi from 'components/common/uploads/components/UploadUi.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useBillErasers} from 'stores/bill-erasers';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';

  import {loginPerson} from 'stores/utils/login';
  import {usePpls} from 'stores/ppls';
  import {HForm, HSave} from 'src/utils/hForm';
  import FileTypeHandler from 'components/common/uploads/file-types/fileTypeHandler.vue';
  import {axiosFeathers, restCore} from 'components/common/uploads/services/utils';
  import {useHouseholds} from 'stores/households';
  import {totalIncomes} from 'components/households/utils/income';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';

  const { person } = loginPerson()

  const props = defineProps({
    session: { required: true }
  })

  const authDialog = ref(false);

  const eraserStore = useBillErasers();
  const pplStore = usePpls();
  const hhStore = useHouseholds();

  const { item: eraser } = idGet({
    store: eraserStore,
    value: computed(() => props.session)
  ,
    useAtcStore
  })
  const { item: eraserPerson } = idGet({
    store: pplStore,
    value: computed(() => eraser.value?.person)
  ,
    useAtcStore
  })
  const { item:hh } = idGet({
    store: hhStore,
    value: computed(() => eraserPerson.value?.household)
  ,
    useAtcStore
  })

  const loading = ref(false);
  const rawFiles = ref([])
  const display = ref([]);

  const removeFile = (i) => {
    rawFiles.value.splice(i, 1);
    display.value.splice(i, 1);
  }
  const removeEraserFile = (i) => {
    const f = [...eraser.value.files];
    f.splice(i, 1);
    eraserStore.patch(eraser.value._id, { files: i });
  }
  const toDataUrl = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = () => reject(reader.error);
      reader.readAsDataURL(file);
    });
  }
  const to = ref()
  const toSave = async () => {
    if (to.value) clearTimeout(to.value)
    to.value = setTimeout(async () => {
      loading.value = true;
      const payload = new FormData();
      for (let i = 0; i < rawFiles.value.length; i++) {
        if (rawFiles.value[i].file) payload.append('files', rawFiles.value[i].file);
      }
      const res = await axiosFeathers().patch(`/bill-erasers/${eraser.value._id}`, payload, {
        params: {
          core: restCore(),
          runJoin: { add_files: true }
        }
      })
          .catch(err => console.error(`Error adding files: ${err.message}`))
      console.log('got res', res);
      if (res.data?._id) eraserStore.patchInStore(eraser.value._id, res.data);
      loading.value = false
    }, 1000)
  }
  const upload = async (files) => {
    const fileKeys = ['lastModified', 'lastModifiedDate', 'name', 'size', 'type']
    const getFile = async (file) => {
      const url = await toDataUrl(file);
      const meta = {};
      for (const k of fileKeys) {
        meta[k] = file[k];
      }
      return { url, meta, file }
    }
    if (Array.isArray(files)) {
      const names = rawFiles.value.map(a => a.meta.name || a.meta.size);
      for (let i = 0; i < files.length; i++) {
        const file = await getFile(files[i].raw);
        if (!names.includes(file.meta.name || file.meta.size)) rawFiles.value.push(file);
      }
    } else rawFiles.value.push(getFile(files.raw));
    toSave()
  }

  const { form:eraserForm, save:eraserSave } = HForm({
    store: eraserStore,
    value: eraser
  })
  const { autoSave:eraserAutoSave, setForm:setEraser } = HSave({ form: eraserForm, save:eraserSave, store: eraserStore })


  const formFn = (defs) => {
    const obj = {};
    if (person.value) {
      ['name', 'phone', 'email'].forEach(a => {
        if (person.value[a]) obj[a] = person.value[a]
      })
    }
    return {
      ...obj,
      ...defs
    }
  }

  const { form, save } = HForm({
    store: pplStore,
    value: eraserPerson,
    formFn
  })

  const { autoSave, setForm } = HSave({ form, save, store: pplStore, params: { special_change: '*' }})

  const incCount = (v) => {
    const nv = form.value.hhCount + v;
    if(nv > 0){
      form.value.hhCount = nv;
      autoSave('hhCount')
    }
  }

  watch(hh, (nv) => {
    if(nv?._id){
      if(!form.value.income) form.value.income = totalIncomes(nv).total
      if(!form.value.hhCount) form.value.hhCount = Object.keys(nv.members || {}).length || 1
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__ps {
    width: 100%;
    min-height: 500px;
  }
  .__ul {
    padding: 10px;
    height: 80px;
    width: 100%;
    //background: var(--q-p2);
    transform: none;
    transition: all .3s ease;
    cursor: pointer !important;
    color: var(--q-p12);

    .__drop_zone {
      width: 100%;
      height: 80px;
      border-radius: min(10px, 2vw);
      color: var(--q-p7);
      transition: all .3s ease;
      overflow: hidden;
      background-size: 200%;
      background-position: center;
      background-image: linear-gradient(135deg, var(--q-s2), var(--q-p2), var(--q-a2));

      > div {
        background: rgba(255, 255, 255, .7);
        padding: 10px;
        border-radius: inherit;
        transition: all .3s ease;
      }

      .__lock {
        color: #999;
        font-weight: 500;
        transition: all .3s ease;
        padding: 0 5px;
        font-size: var(--text-xxs) !important;
      }

    }
  }

  .__raw {
    width: 100%;
    display: grid;
    align-items: center;
    grid-template-columns: 110px 1fr auto;
    padding: 20px;
    border-radius: 10px;
    background: white;
    margin: 10px 0;

    > div {
      padding: 0 5px;
    }
  }


  .__file {
    height: 50px;
    width: 40px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 1px 1px 2px #c5c5c5;
  }
</style>
