<template>
  <div>
    <div class="flex items-start __mw">
      <slot name="top" :thread="thread"></slot>

      <default-avatar
          class="q-mt-xs q-mr-xs"
          size-in="30px"
          :model-value="thread?._fastjoin?.person"
          :use-atc-store="useAtcStore"
      ></default-avatar>

      <div class="relative-position">

        <div class="__thread q-pa-sm">

          <div class="font-3-4r text-weight-bold text-ir-grey-8">
            <div class="flex items-center">
              <div>{{ thread?._fastjoin?.person?.name || 'Unknown' }}</div>
              <slot name="after-name" :thread="thread"></slot>
            </div>
          </div>
          <div class="font-7-8r" v-html="thread?.body"></div>
        </div>
        <div class="row items-center">
          <q-chip size="sm" clickable color="transparent" @click="reply = !reply"
                  :icon-right="reply ? 'mdi-close' : undefined">
            <span class="font-3-4r text-weight-bold text-ir-grey-8">Reply</span>
          </q-chip>
          <div class="font-3-4r text-ir-grey-8">{{ $ago(thread?.createdAt) }}</div>
          <vote-button
              size="20px"
              :user-id="login?._id"
              :store="store"
              :model-value="thread"
          ></vote-button>
          <q-space></q-space>
          <q-btn flat dense size="sm" icon="mdi-dots-vertical">
            <q-menu>
              <q-list dense separator>
                <template v-for="(item, i) in menuItems">
                  <q-item :key="`menu-item-${i}`" clickable @click="item.click(thread)" v-if="item.show()">
                    <q-item-section>
                      <q-item-label>{{ item.label }}</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-icon :name="item.icon"></q-icon>
                    </q-item-section>
                  </q-item>
                </template>
              </q-list>
            </q-menu>
          </q-btn>
        </div>
      </div>

    </div>

    <q-slide-transition>
      <div v-if="reply" class="__reply">
        <thread-form
            size="xs"
            :parent="depth === 0 ? { id: thread?._id, idService: 'threads' } : parent"
            @update:model-value="reply = false"
            :depth="depth"
            :parent-tag="parentTag"
            :model-value="editing ? thread : undefined"
        ></thread-form>
      </div>
    </q-slide-transition>

    <q-slide-transition>
      <div v-if="show" :id="`sub-${thread?._id}-thread`" class="q-pl-md">
        <show-threads
            :depth="depth + 1"
            :threads="thread?.threads || []"
            :menu-add-ons="menuAddOns"
        >
          <template v-slot:card-top="scope">
            <slot name="top" v-bind="scope"></slot>
          </template>
          <template v-slot:after-name="scope">
            <slot name="after-name" v-bind="scope"></slot>
          </template>
        </show-threads>
      </div>
    </q-slide-transition>

    <div class="q-pl-xl">
      <q-btn
          :icon-right="`mdi-chevron-${show ? 'up' : 'down'}`"
          v-if="thread?.threads"
          flat dense size="sm"
          class="text-weight-bold"
          color="ir-grey-6"
          :label="`${!show ? 'Show' : 'Hide'} ${$possiblyPlural('Repl', thread?.threads, 'y', 'ies')}`"
          @click="show = !show"
      ></q-btn>
    </div>

    <common-dialog v-model="reportDialog" setting="small">
      <q-card class="_fw q-pa-lg">
        <report-issue @update:modelValue="reportDialog = false" type="content" service="threads" :record="thread?._id"></report-issue>
      </q-card>
    </common-dialog>
  </div>
</template>

<script setup>
  import DefaultAvatar from 'src/components/common/avatars/DefaultAvatar.vue';
  import ThreadForm from 'src/components/threads/forms/ThreadForm.vue';
  import ShowThreads from 'src/components/threads/cards/ShowThreads.vue';
  import VoteButton from 'src/components/common/buttons/VoteButton.vue';
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import ReportIssue from 'src/components/issues/forms/ReportIssue.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useThreads} from 'src/stores/threads';
  import {$ago, $errNotify, $possiblyPlural, $successNotify} from 'src/utils/global-methods';

  import {canUOrCreated} from 'src/utils/ucans/client-auth';

  import {loginPerson} from 'src/stores/utils/login';
  const { login } = loginPerson()

  import {useRouter} from 'vue-router';

  const router = useRouter();

  const store = useThreads();
  const reply = ref(false);
  const show = ref(false);

  const props = defineProps({
    modelValue: { required: true },
    depth: { type: Number, default: 0 },
    menuAddOns: { type: Array }
  });

  const reportDialog = ref(false);

  const { item: thread } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const parent = computed(() => {
    return props.depth === 0 ? { id: thread.value?._id, idService: 'threads' } : thread.value.parent
  })

  const parentTag = computed(() => {
    const owner = thread.value?._fastjoin.owner?._fastjoin?.owner;
    if (owner && props.depth !== 0) return {
      id: owner._id,
      name: owner.name,
      service: 'ppls'
    }
    else return undefined;
  });

  const canEdit = ref({ ok: false });
  const editing = ref(false);

  watch(thread, async (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      canEdit.value = await canUOrCreated(nv.createdBy?.login, { requiredCapabilities: [['threads', 'DELETE']] })
    }
  }, { immediate: true })

  const menuItems = computed(() => {
    return [
      {
        icon: 'mdi-pencil-box',
        label: 'Edit',
        show: () => canEdit.value?.ok,
        click: () => {
          editing.value = true;
          reply.value = true;
        }
      },
      {
        icon: 'mdi-delete',
        label: 'Delete',
        show: () => canEdit.value?.ok,
        click: async () => {
          await store.remove(thread.value._id)
              .catch(err => $errNotify(`Could not delete: ${err.message}`))
          $successNotify('Deleted!')
        }
      },
      {
        icon: 'mdi-flag',
        label: 'Report',
        show: () => login.value?._id,
        click: () => reportDialog.value = true
      },
      {
        icon: 'mdi-account',
        label: 'Login to interact',
        show: () => !login.value?._id,
        click: () => router.push('/login')
      },
      ...props.menuAddOns || []
    ]
  })
</script>

<style lang="scss" scoped>
  .__mw {
    min-width: 150px;
    position: relative;
  }

  .__ta {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(0, -50%);
  }

  .__reply {
    width: 100%;
    max-width: 300px;
  }

  .__thread {
    background-color: #eeeeee;
    border-radius: 10px;
  }
</style>
