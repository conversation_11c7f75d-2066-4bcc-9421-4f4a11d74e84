<template>
  <div class="_fw bg-ir-bg1">
    <q-chip dense square color="transparent"></q-chip>
    <template v-if="canEdit.ok">
      <div class="row justify-center">
        <div class="_cent bg-ir-bg pw1">
          <org-comps :org="org"></org-comps>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
  import OrgComps from 'components/comps/pages/OrgComps.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {loginPerson} from 'stores/utils/login';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {fakeId} from 'src/utils/global-methods';
  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {useEnvStore} from 'stores/env';

  const orgStore = useOrgs();
  const envStore = useEnvStore();

  const { login } = loginPerson();

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => envStore.getOrgId)
  ,
    useAtcStore
  })

  const orgId = computed(() => org.value._id || fakeId)
  const { canEdit } = clientCanU({
    subject: org,
    login,
    caps: computed(() => {
      const arr = [['orgs', 'WRITE']]
      if (orgId.value) {
        arr.push([`orgs:${orgId.value}`, 'WRITE'])
        arr.push([`orgs:${orgId.value}`, 'orgAdmin'])
      }
      return arr;
    }),
    loginPass: [[['updatedBy.login'], '*']],
    cap_subjects: computed(() => [org.value._id])
  })
</script>

<style lang="scss" scoped>

</style>
