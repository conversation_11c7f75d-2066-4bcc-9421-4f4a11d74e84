<template>
  <q-item v-bind="{ ...$attrs}">
    <slot name="avatar">
      <q-item-section avatar>
        <q-icon name="mdi-bank"></q-icon>
      </q-item-section>
    </slot>
    <q-item-section>
      <q-item-label caption>{{account?.bankName || 'Invalid Account'}} ending in {{account?.last4}}</q-item-label>
      <q-item-label>{{name || `${$capitalizeFirstLetter(account?.type||'')} ${$capitalizeFirstLetter(account?.accountType || 'Account')}`}}</q-item-label>
    </q-item-section>
    <slot name="side" :item="account"></slot>
  </q-item>
</template>

<script setup>

  import {useBankAccounts} from 'stores/bank-accounts';
  import {idGet} from 'src/utils/id-get';
  import { computed } from 'vue';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useBankAccounts();
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item:account } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })
  const name = computed(() => account.value?.nickname)
</script>

<style lang="scss" scoped>

</style>
