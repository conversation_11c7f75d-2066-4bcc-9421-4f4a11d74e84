<template>
  <q-item clickable @click="addAccount">
    <q-item-section avatar>
      <q-icon color="primary" name="mdi-plus"></q-icon>
    </q-item-section>
    <q-item-section>
      <q-item-label>Add New</q-item-label>
    </q-item-section>
    <common-dialog setting="right" :model-value="dialogOn" @update:model-value="toggleDialog">
      <div class="_fw q-pa-md">
        <business-account-form
            @update:model-value="emitUp"
            :model-value="modelValue"
            :org="fullOrg"
        ></business-account-form>
      </div>
    </common-dialog>
  </q-item>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import BusinessAccountForm from 'components/accounts/forms/BusinessAccountForm.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useOrgs} from 'stores/orgs';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useBankAccounts} from 'stores/bank-accounts';

  const orgStore = useOrgs();
  const accountStore = useBankAccounts();

  const emit = defineEmits(['update:model-value', 'update:dialog']);
  const props = defineProps({
    org: { required: true },
    modelValue: { required: false },
    dialog: Boolean
  })

  const dialogOn = ref(false);

  const { item: fullOrg } = idGet({
    store:orgStore,
    value: computed(() => props.org)
  ,
    useAtcStore
  })

  const { item:account } = idGet({
    store: accountStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const addAccount = () => {
    dialogOn.value = true
  }
  const toggleDialog = (val) => {
    dialogOn.value = val;
    emit('update:model-value', val);
  }
  watch(() => props.dialog, (nv, ov) => {
    if(nv !== ov) dialogOn.value = nv;
  }, { immediate: true })

  const emitUp = (val) => {
    orgStore.get(fullOrg.value._id);
    emit('update:model-value', val);
  }
</script>

<style lang="scss" scoped>

</style>
