import {computed, ComputedRef, ref, Ref} from 'vue';
import {useBudgets} from 'stores/budgets';
import {useCareAccounts} from 'stores/care-accounts';
import {idGet} from 'src/utils/id-get';
import {useOrgs} from 'stores/orgs';
import {useAtcStore} from 'src/stores/atc-store';

export const budgetTree = (subject:Ref<any>|ComputedRef<any>) => {
    const budgetStore = useBudgets();
    const caStore = useCareAccounts();
    const orgStore = useOrgs();

    const { item: budget } = idGet({
        store:budgetStore as any,
        value: subject,
        params: ref({ runJoin: { budget_owner: true }}),
        useAtcStore
    })
    //TODO: could write joins for parent and ca for less db calls
    const { item: parent } = idGet({
        store:budgetStore as any,
        value: computed(() => budget.value?.parent),
        useAtcStore
    })
    const { item: ca } = idGet({
        store: caStore as any,
        value: computed(() => budget.value?.careAccount || parent.value?.careAccount),
        useAtcStore
    })
    const { item: org } = idGet({
        store: orgStore as any,
        value: computed(() => budget.value?._fastjoin?.owner || budget.value?.owner),
        useAtcStore
    })

    return {
        org,
        ca,
        budget,
        parent,
        budgetStore,
        caStore,
        orgStore
    }
}
