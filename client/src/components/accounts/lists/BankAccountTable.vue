<template>
  <div class="_fw">

    <template v-if="!canEdit.ok">
      <div class="q-pa-lg">You do not have permission to view this banking detail</div>
    </template>
    <template v-else>
      <add-edit-account
          v-if="!noAdd"
          :org="fullOrg"
          :model-value="editing"
          :dialog="!!editing"
          @update:model-value="setEditing"
          @update:dialog="toggleDialog"
      ></add-edit-account>
      <table>
        <tr>
          <th></th>
          <th>Name</th>
          <th>Bank</th>
          <th>Last4</th>
          <th>Status</th>
          <th>
            <span>Default <q-icon name="mdi-information"></q-icon></span>
            <q-tooltip class="font-7-8r tw-six">Will be used for funds sent to your organization</q-tooltip>
          </th>
        </tr>
        <tr v-for="acct in a$.data"
            :key="acct._id" class="cursor-pointer" @dblclick="setEditing(acct)">
          <td>
            <q-btn dense flat icon="mdi-dots-vertical">
              <q-popup-proxy>
                <div class="w300 mw100 bg-white q-pa-md">
                  <q-list separator>
                    <q-item clickable @click="setEditing(acct)">
                      <q-item-section>
                        <q-item-label>Edit</q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-icon name="mdi-pencil"></q-icon>
                      </q-item-section>
                    </q-item>
                    <q-item clickable @click="setDefault(acct)">
                      <q-item-section>
                        <q-item-label>Make Default</q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-icon name="mdi-home"></q-icon>
                      </q-item-section>
                    </q-item>
                    <remove-item name="Account" @remove="removeAccount(acct._id)"></remove-item>
                  </q-list>
                </div>
              </q-popup-proxy>
            </q-btn>
          </td>
          <td>
            {{ acct.nickname || `${$capitalizeFirstLetter(acct?.type || '')} ${$capitalizeFirstLetter(acct?.accountType || 'Account')}` }}
          </td>
          <td>{{ acct.bankName }}</td>
          <td>{{ acct.last4 }}</td>
          <td>
            <q-icon v-bind="{size: '15px', ...statusIcons[acct?.status]}">
              <q-tooltip class="font-7-8r tw-six">{{acctStatuses[acct?.status] || 'Un-Verified'}}</q-tooltip>
            </q-icon>
          </td>
          <td>
            <q-icon v-if="(fullOrg?.bankAccounts || {})[acct._id]?.default" name="mdi-check-circle" color="green"
                    size="15px">
              <q-tooltip class="font-7-8r tw-six">Will be used for funds sent to your organization</q-tooltip>
            </q-icon>
          </td>
        </tr>
      </table>
    </template>


  </div>
</template>

<script setup>
  import AddEditAccount from 'components/accounts/forms/AddEditAccount.vue';
  import RemoveItem from 'components/common/buttons/RemoveItem.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {useBankAccounts} from 'stores/bank-accounts';
  import {HFind} from 'src/utils/hFind';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';
  import {acctStatuses, statusIcons} from 'components/accounts/utils/statuses';
  const { login } = loginPerson()

  const store = useOrgs();
  const accountStore = useBankAccounts();

  const emit = defineEmits(['update:model-value', 'add-edit'])
  const props = defineProps({
    org: { required: true },
    noEdit: Boolean,
    noAdd: Boolean
  })

  const { item: fullOrg } = idGet({
    store,
    value: computed(() => props.org)
  ,
    useAtcStore
  })

  const { canEdit } = clientCanU({
    subject: fullOrg,
    or: true,
    caps: computed(() => [[`orgs:${fullOrg.value?._id}`, ['orgAdmin']], [`orgs:${fullOrg.value?._id}`, ['WRITE']], ['orgs', ['WRITE']]]),
    login,
    cap_subjects: computed(() => [fullOrg.value._id])
  })

  const editing = ref('');

  const { h$: a$ } = HFind({
    store: accountStore,
    limit: computed(() => Object.keys(fullOrg.value?.accounts || {}).length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: Object.keys(fullOrg.value?.accounts || {}) },
        }
      }
    })
  })

  const removeAccount = (id) => {
    accountStore.remove(id)
  }

  const setDefault = (val) => {
    if (!props.noEdit) {
      const accounts = fullOrg.value?.accounts || {};
      if (!accounts[val._id].default) {
        let $set = { [`accounts.${val._id}.default`]: true }
        for (const k in accounts) {
          if (accounts[k].default) {
            $set[`accounts.${k}.default`] = false
          }
        }
        store.patch(fullOrg.value._id, { $set })
      }
    }
  }
  const toggleDialog = (val) => {
    if (!val) editing.value = undefined;
  }
  const setEditing = (val) => {
    if (!val || editing.value?._id === val._id) {
      editing.value = undefined
      emit('add-edit', val)
    } else {
      if (!props.noEdit) {
        editing.value = val;
      }
      const accounts = fullOrg.value?.accounts || {};
      let newDefault = val._id;
      for (const k in accounts) {
        if (accounts[k].default) {
          newDefault = false;
          break;
        }
      }
      if (newDefault) {
        store.patch(fullOrg.value._id, { $set: { [`accounts.${val._id}.default`]: true } })
      }
    }
    emit('update:model-value', val)
  }

</script>

<style lang="scss" scoped>
  table {
    width: 100%;
    border-collapse: collapse;

    th {
      font-weight: 600;
      font-size: .85rem;
      color: #999;
      padding: 5px 10px;
      border-bottom: solid .3px black;
      text-align: left;
    }

    td {
      padding: 5px 10px;
      border-bottom: solid .2px #999;
    }

  }
</style>
