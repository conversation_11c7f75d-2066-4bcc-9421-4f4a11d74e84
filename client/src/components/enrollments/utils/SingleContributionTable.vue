<template>
  <table>
    <tbody>
    <tr>
      <td></td>
      <td>Amount</td>
      <td>Flex
        <q-tooltip class="font-7-8r">Can be used on other plan features</q-tooltip>

      </td>
    </tr>
    <tr>
      <td>Employer Allowance</td>
      <td>
        <span class="alt-font" v-html="allowance"></span>
      </td>
      <td>
        <q-icon size="20px" color="green" name="mdi-check-circle">
          <q-tooltip class="font-7-8r">These funds can be used toward any plan benefit</q-tooltip>
        </q-icon>
      </td>
    </tr>
    <tr>
      <td>Premium Subsidy</td>
      <td>
        <span class="alt-font" v-html="dollarString(subsidy, '$', 0)"></span>
      </td>
      <td>
        <q-icon size="20px" color="red" name="mdi-cancel">
          <q-tooltip class="font-7-8r">These funds can only be used toward premiums</q-tooltip>
        </q-icon>
      </td>
    </tr>
    </tbody>
  </table>
</template>

<script setup>

  import {computed} from 'vue';
  import {dollarString} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {usePpls} from 'stores/ppls';
  import {erSubsidy} from 'components/market/shop/utils/subsidy';
  import {useAtcStore} from 'src/stores/atc-store';

  const planStore = usePlans();
  const pplStore = usePpls();

  const props = defineProps({
    dark: Boolean,
    coverage: { required: true },
    plan: { required: false },
    person: { required: false },
    enrollment: { required: false }
  })

  const { item: fullPlan } = idGet({
    value: computed(() => props.plan),
    store: planStore
  ,
    useAtcStore
  })
  const { item: fullPerson } = idGet({
    value: computed(() => props.person),
    store: pplStore,

    useAtcStore
  })

  const { subsidy, allowance } = erSubsidy({
    plan: fullPlan,
    coverage: computed(() => props.coverage?._id || props.coverage),
    enrollment: computed(() => props.enrollment),
    person: fullPerson
  })

</script>

<style lang="scss" scoped>
  table {
    border-collapse: collapse;
    width: 100%;
    box-sizing: border-box;

    tr {
      //display: block;
      box-shadow: 0 2px 2px var(--ir-light);
      border-radius: 6px;

      &:first-child {
        box-shadow: none;
      }
    }

    td {
      padding: 8px 10px;
      //border-bottom: solid .3px #999;
    }
  }
</style>
