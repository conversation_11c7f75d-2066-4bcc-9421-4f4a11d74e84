<template>
  <div class="_fw q-py-md pw1">
    <div
        :class="`tw-six font-1-1-2r text-secondary text-${$q.screen.lt.md ? 'center' : 'left'} text-sm-center q-py-md`">
      Choose your ideal coverage
    </div>

    <div class="row items-center q-py-sm">
      <div class="col-12 col-md-4 q-pa-sm">
        <div class="__item text-xxs">
          <div class="__img">
            <q-img class="_fw" :src="policy" fit="contain"></q-img>
          </div>
          <div>
            We collect your needs and preferences.
          </div>
        </div>
      </div>
      <div class="col-12 col-md-4 q-pa-sm">
        <div class="__item text-xxs">
          <div class="__img">
            <q-img class="_fw" :src="policies" fit="contain"></q-img>
          </div>
          <div>
            <span>Take the&nbsp;</span>
            <q-spinner class="q-mx-sm" v-if="loading" color="primary"></q-spinner>
            <span v-else class="num-font __num"> 100+ </span>
            <span>available plans in your zip code</span>
          </div>
        </div>
      </div>
      <div class="col-12 col-md-4 q-pa-sm">
        <div class="__item text-xxs">
          <div class="__img">
            <q-img class="_fw" :src="settings" fit="contain"></q-img>
          </div>
          <div>
            Analyze all benefits to output your ideal policy (+ honorable mentions)
          </div>
        </div>
      </div>

    </div>

    <q-separator class="q-my-sm" color="secondary"></q-separator>
    <q-slide-transition>
      <ichra-form
          v-if="fulle?._id && (ichra?.status || 1) < 2"
          :enrollment="fulle"
          :model-value="modelValue"
      ></ichra-form>
    </q-slide-transition>

    <div class="row" v-if="ichra?.status === 2">
      <div class="col-12 col-lg-6 q-pa-xs">
        <div  class="__c">
          <div class="tw-six q-pa-sm text-primary font-1-1-4r row items-center">
            <div>Preferences Set!</div>
            <q-space></q-space>
            <q-btn dense flat no-caps @click="setStatus(1)">
              <span class="q-mr-sm font-1r">Edit</span>
              <q-icon size="18px" color="secondary" name="mdi-pencil"></q-icon>
            </q-btn>
          </div>
          <div class="q-pa-sm font-1r tw-five">Your policy details have moved on to review.<br><br>
            <div>We will reach out to the carrier to approve documents and send them for your signature.</div>
          </div>

        </div>
      </div>
      <div class="col-12 col-lg-6 q-pa-xs">
        <div class="__c">
          <div class="tw-six q-pa-sm text-primary font-1-1-4r flex items-center">
            Where to notify you
          </div>
          <q-list separator>
            <q-item>
              <q-item-section avatar>
                <q-img class="w30 h30" :src="logo" fit="contain"></q-img>
              </q-item-section>
              <q-item-section>
                <q-item-label class="__l q-py-md">In App</q-item-label>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section avatar>
                <q-icon size="26px" color="primary" name="mdi-email"></q-icon>
              </q-item-section>
              <q-item-section>
                <email-field dense label="" icon="" borderless placeholder="Add Email" class="__l" v-model="inputs.email"
                             @update:model-value="updatePerson('email')"></email-field>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section avatar>
                <q-icon size="26px" name="mdi-cellphone" color="primary"></q-icon>
              </q-item-section>
              <q-item-section>
                <phone-input borderless placeholder="Add Phone" :inputAttrs="{ placeholder: 'Phone', class: '__l', borderless: true }" v-model="inputs.phone" @update:model-value="updatePerson('phone')"></phone-input>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
  import logo from 'src/assets/commoncare_icon.svg'
  import IchraForm from 'components/enrollments/ichra/forms/IchraForm.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import policies from 'src/assets/icons/policies.svg';
  import settings from 'src/assets/icons/common_settings.svg';
  import policy from 'src/assets/icons/common_policy.svg'

  import {idGet} from 'src/utils/id-get';
  import {computed, nextTick, ref, watch} from 'vue';
  import {useEnrollments} from 'stores/enrollments';
  import {SessionStorage} from 'symbol-auth-client';
  import {loginPerson} from 'stores/utils/login';
  import {usePpls} from 'stores/ppls';
  const { person } = loginPerson()


  const enrollmentStore = useEnrollments();
  const pplsStore = usePpls();

  const props = defineProps({
    enrollment: { required: true },
    modelValue: { required: false }
  })

  const { item: fulle } = idGet({
    store: enrollmentStore,
    value: computed(() => props.enrollment),
    params: ref({ runJoin: { enrollment_person: true } })
  })

  const coverageId = computed(() => Object.keys(fulle.value.coverages || {}).filter(a => fulle.value.coverages[a].ichra)[0])

  const loading = ref(false);

  const inputs = ref({
    email: '',
    phone: undefined
  })
  const to = ref({ email: undefined, phone: undefined });
  const updatePerson = (path) => {
    clearTimeout(to.value[path]);
    to.value[path] = setTimeout(() => {
      if(inputs.value[path]) pplsStore.patch(person.value._id, { $set: { [path]: inputs.value[path] }})
    }, 1000)
  }

  const market = ref({});

  const init = async (tries = 0) => {
    loading.value = true;
    const m = SessionStorage.getItem('market_data')
    if(m) {
      market.value = m;
      loading.value = false
    }
    else if(tries < 10) setTimeout(() => {
      init(tries+1)
    }, 1000)
    else loading.value = false;
  }

  const ichra = ref({})

  watch(fulle, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      nextTick(() => {
        init();
      })
    }
  }, { immediate: true })

  const setStatus = (val) => {
    enrollmentStore.patch(fulle.value._id, { $set: { [`coverages.${coverageId.value}.status`]: val }})
  }

  watch(person, (nv) => {
    if(nv.email) inputs.value.email = nv.email;
    if(nv.phone) inputs.value.phone = nv.phone;
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__item {
    margin: 3px 0;
    padding: 5px 10px;
    border-radius: 10px;
    background: white;
    //box-shadow: 0 2px 6px -2px #999;
    //background: rgba(255, 255, 255, .7);
    display: flex;
    align-items: center;
    transition: all .4s;

  }

  .__num {
    color: var(--q-primary);
    font-weight: 600;
  }

  .__img {
    width: 40px;
    height: 40px;
    margin: 0 15px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .__c {
    padding: 20px 2vw;
    border-radius: 20px;
    background: white;
    box-shadow: 0 2px 8px -5px #666;
    margin: 10px 2px;
    position: relative;
  }

  .__l {
    font-size: .9rem;
  }

</style>
