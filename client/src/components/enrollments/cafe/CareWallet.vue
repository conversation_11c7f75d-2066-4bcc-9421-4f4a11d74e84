<template>
  <div>
    <div class="row q-py-sm">
      <plan-interval-chip v-model="interval"></plan-interval-chip>
    </div>
    <div class="_fw q-py-sm tw-six font-1r flex items-center">
      <div>Funds Incoming</div>
      <q-icon color="primary" name="mdi-menu-up" size="25px"></q-icon>
    </div>
    <div class="__r">
      <template v-if="incoming.employer">
        <div class="tw-six text-ir-deep">Employer Allowance:</div>
        <div class="alt-font">{{dollarString(incoming.employer * intervals[interval].factor, '$', 2)}} {{intervals[interval].label}}</div>
      </template>
      <div class="tw-six text-ir-deep">Payroll Elections:</div>
      <div class="alt-font">{{dollarString((incoming.elected - incoming.employer) * intervals[interval].factor, '$', 2)}} {{intervals[interval].label}}</div>
      <div class="tw-six text-primary">Total Incoming:</div>
      <div class="alt-font">{{dollarString(incoming.elected, '$', 2)}} {{intervals[interval].label}}</div>

    </div>

    <div class="_fw q-py-sm tw-six font-1r flex items-center">
      <div>Funds Committed</div>
      <q-icon color="secondary" name="mdi-menu-down" size="25px"></q-icon>
    </div>
    <div class="__r">
      <template v-for="(k, i) in Object.keys(committed.byKey)" :key="`c-${i}`">
        <div class="tw-six text-ir-deep">{{cafeKeys[k]?.name}}</div>
        <div class="alt-font">{{dollarString(committed.byKey[k], '$', 2)}}</div>
      </template>
      <div class="tw-six text-secondary">Total Committed:</div>
      <div class="alt-font">{{dollarString(committed.total, '$', 2)}} {{intervals[interval].label}}</div>
    </div>
  </div>
</template>

<script setup>
  import PlanIntervalChip from 'components/plans/utils/PlanIntervalChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {useEnrollments} from 'stores/enrollments';
  import {idGet} from 'src/utils/id-get';
  import {enrollmentContributions} from 'components/enrollments/utils';
  import {usePlans} from 'stores/plans';
  import {usePpls} from 'stores/ppls';
  import { intervals } from 'components/plans/utils/intervals';
  import {dollarString} from 'symbol-syntax-utils';
  import {cafeKeys} from 'src/components/plans/utils';

  const erStore = useEnrollments();
  const planStore = usePlans();
  const pplStore = usePpls();

  const props = defineProps({
    enrollment: { required: true }
  });

  const interval = ref('monthly');

  const { item: er } = idGet({
    store: erStore,
    value: computed(() => props.enrollment)
  ,
    useAtcStore
  })
  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => er.value.plan)
  ,
    useAtcStore
  })
  const { item: person } = idGet({
    store: pplStore,
    value: computed(() => er.value.person)
  ,
    useAtcStore
  })

  const { contributions } = enrollmentContributions({
    enrollment: er,
    plan,
    person
  })

  const incoming = computed(() => {
    return {
      employer: contributions.value.employer?.cafe || 0,
      elected: contributions.value.elected || 0
    }
  })

  const committed = computed(() => {
    const byKey = {};
    let total = 0;
    for(const k in er.value.cafe || {}){
      const amt = er.value.cafe[k].amount
      if(amt){
        total += amt;
        byKey[k] = amt
      }
    }
    return {
      byKey,
      total
    }
  })

</script>

<style lang="scss" scoped>

  .__r {
    width: 100%;
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;

    > div {
      padding: 5px;

    }
  }
</style>
