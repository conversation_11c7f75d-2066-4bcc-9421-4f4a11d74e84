<template>
  <div class="_fw __eg">

    <div class="_fw">

      <div>
        <div class="tw-six font-1r">
        <span :class="`tw-seven text-${years[enrollment?.planYear] || 'grey-6'}`" class="tw-six">
          {{ enrollment?.planYear }}
        </span>
          &nbsp;|&nbsp;
          {{ fullPlan?.name }}
        </div>
        <div :class="`row items-center q-py-sm font-1r tw-six text-${years[enrollment?.planYear] || 'grey-6'}`">
          <div>{{ evt }}</div>
          <span class="text-grey-8">&nbsp;|&nbsp;</span>
          <div class="flex items-center q-px-sm">
            <div class="font-3-4r text-grey-8 tw-four">Plan year start:</div>
            <div class="tw-six q-px-sm">
              {{ formatDate(fullPlan?.planYearStart, 'MMMM D') }}
            </div>
          </div>
        </div>
        <q-separator class="q-my-sm"></q-separator>
      </div>

      <action-row :model-value="enrollment"></action-row>

    </div>

  </div>
</template>

<script setup>
  import ActionRow from 'components/enrollments/cards/ActionRow.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useEnrollments} from 'stores/enrollments';
  import {usePlans} from 'stores/plans';
  import {formatDate} from 'src/utils/date-utils';


  const eStore = useEnrollments();
  const planStore = usePlans();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: enrollment } = idGet({
    value: computed(() => props.modelValue),
    store: eStore
  ,
    useAtcStore
  })
  const { item: fullPlan } = idGet({
    value: computed(() => enrollment.value?.plan),
    store: planStore
  ,
    useAtcStore
  })

  const years = {
    '2024': 'blue-6',
    '2025': 'orange-6',
    '2026': 'purple-6'
  }

  const evt = computed(() => {
    return enrollment.value?.version.split('_')[1] === '0' ? 'Open Enrollment' : 'Special Enrollment'
  })

</script>

<style lang="scss" scoped>
  .__eg {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 50%));
  }
</style>
