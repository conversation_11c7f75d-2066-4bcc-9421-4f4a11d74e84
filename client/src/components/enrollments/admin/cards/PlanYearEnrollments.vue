<template>
  <div class="_fw">
    <div class="flex items-center">
      <template v-for="(item, i) in ['changes', 'enrollment', 'eligible']" :key="item">
        <q-chip square dense color="transparent" clickable @click="setTab(item)" :label="$capitalizeFirstLetter(item)"
                :class="`text-ir-deep ${tab === item ? 'tw-six' : ''}`"></q-chip>
        <div v-if="i < 2" class="text-ir-mid">|</div>
      </template>
    </div>

    <q-tab-panels class="_panel" animated v-model="tab">
      <q-tab-panel class="_panel" name="changes">
        <enrollment-changes :plan="fullPlan" :plan-year="yr"></enrollment-changes>
      </q-tab-panel>

      <q-tab-panel class="_panel" name="enrollment">
        <individual-enrollments :event="event" :plan="plan"></individual-enrollments>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="eligible">
        <eligibility-check :plan="fullPlan"></eligibility-check>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import EnrollmentChanges from 'components/enrollments/admin/pages/EnrollmentChanges.vue';
  import EligibilityCheck from 'components/enrollments/admin/cards/EligibilityCheck.vue';
  import IndividualEnrollments from 'components/enrollments/admin/pages/IndividualEnrollments.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, watch, ref} from 'vue';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {useRoute, useRouter} from 'vue-router';

  const planStore = usePlans();
  const route = useRoute();
  const router = useRouter();

  const props = defineProps({
    plan: { required: true },
    event: { required: true }
  })

  const tab = ref('changes');
  const routeTab = computed(() => route.params.tab);
  watch( routeTab, (nv) => {
    if (nv && nv !== tab.value) tab.value = nv
  }, { immediate: true });

  const setTab = (t) => {
    tab.value = t;
    router.push({ ...route, params: { ...route.params, tab: t } })
  }

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  ,
    useAtcStore
  })

  const yr = computed(() => String(Number(props.event)))




</script>

<style lang="scss" scoped>

</style>
