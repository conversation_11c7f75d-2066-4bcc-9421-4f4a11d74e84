<template>
  <div class="_fw">

    <q-tab-panels class="_panel" animated :model-value="tab">
      <q-tab-panel name="base" class="_panel">
        <div class="row items-center q-py-sm">
          <q-btn dense flat icon="mdi-chevron-left" color="primary" @click="emit('back')"></q-btn>
          <q-chip color="white">
            <span>Enrollment Event:&nbsp;&nbsp;<b>{{planKey?.split('_').join(' - ')}}</b></span>
          </q-chip>
          <q-btn flat size="sm" icon="mdi-pencil-box" @click="tab='edit'"></q-btn>
        </div>
        <plan-enrollment :plan="plan" :plan-key="planKey"></plan-enrollment>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="edit">
        <div class="row items-center">
          <q-btn dense flat icon="mdi-chevron-left" color="primary" @click="tab = 'base'"></q-btn>
          <q-chip class="tw-six" color="white" :label="planKey?.split('_').join(' - ')"></q-chip>
        </div>
        <enrollment-form
            :plan-key="planKey"
            :plan="fullPlan"
        ></enrollment-form>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import EnrollmentForm from 'components/enrollments/forms/EnrollmentForm.vue';
  import PlanEnrollment from 'components/enrollments/admin/cards/PlanEnrollment.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {$errNotify} from 'src/utils/global-methods';

  const planStore = usePlans();

  const emit = defineEmits(['back'])
  const props = defineProps({
    plan: { required: true },
    planKey: { required: true }
  })

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  ,
    useAtcStore
  })

  const enrollments = computed(() => {
    const obj = {};
    for (const k in fullPlan.value?.enrollments || {}) {
      const spl = k.split('_');
      if (!spl[1]) spl.push('0');
      obj[spl[0]] = { ...obj[spl[0]], [spl[1]]: fullPlan.value.enrollments[k] };
    }
    const returnObj = {};
    Object.keys(obj).sort((a, b) => Number(b) - Number(a)).forEach(k => returnObj[k] = obj[k]);
    return returnObj;
  })

  const tab = ref('base')

  const remove = async () => {
    if (fullPlan.value.enrollments[props.planKey].enrolled) {
      $errNotify('Participants have enrolled in this enrollment - you cannot remove it');
    } else {
      await planStore.patch(fullPlan.value._id, { $unset: { [`enrollments.${props.planKey}`]: '' } })
          .catch(err => $errNotify(`Error removing enrollment ${props.planKey}: ${err.message}`));
    }
  }


</script>

<style lang="scss" scoped>

</style>
