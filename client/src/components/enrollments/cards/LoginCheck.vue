<template>
  <div class="_fw">
    <template v-if="!login._id">
      <login-page>
      </login-page>
    </template>
    <template v-else-if="personCheck && person?.login !== login._id">
      <div class="row justify-center">
        <q-img
            src="https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fsecure_area.png?alt=media&token=ea043baa-b4ea-40ec-be30-96eb2ceedd10"
            style="height: 300px; width: 300px; border-radius: 10px" fit="cover"></q-img>
      </div>
      <div class="row justify-center">
        <div class="w800 mw100 text-center q-pa-lg font-1-1-2r">
          <div class="text-center q-py-md _fw tw-six">Hold it! This invite doesn't match your login</div>
          <div class="q-py-sm row items-center justify-center">
            <div>This invite is for</div>
            <default-chip :chip-attrs="{ class:'tw-six' }" :model-value="person" :use-atc-store="useAtcStore"></default-chip>
          </div>
          <div class="q-py-sm row items-center justify-center">
            <div>You are logged in as</div>
            <default-chip :chip-attrs="{ class:'tw-six' }" :model-value="person" :use-atc-store="useAtcStore"></default-chip>

          </div>
          <div class="q-py-sm">Ask your plan admin to check your link.</div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import LoginPage from 'components/auth/pages/LoginPage.vue';


  import {loginPerson} from 'stores/utils/login';
  import {useAtcStore} from 'src/stores/atc-store';
  const { login, person } = loginPerson()

  const props = defineProps({
    personCheck: { required: false }
  })

</script>

<style lang="scss" scoped>

</style>
