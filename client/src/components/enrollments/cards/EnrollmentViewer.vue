<template>
  <div class="row q-pb-sm">
    <q-tabs dense align="left" no-caps v-model="tab" indicator-color="primary">
      <q-tab v-for="(t, i) in Object.keys(tabs)" :key="`t-${i}`" :name="t">
        <span class="tw-six">{{ tabs[t].label }}</span>
      </q-tab>
    </q-tabs>
  </div>

  <div class="_fw">
  <q-tab-panels class="_panel" animated v-model="tab">
    <q-tab-panel class="_panel" v-for="(t, i) in Object.keys(tabs)" :key="`p-${i}`" :name="t">
      <component :is="tabs[t].component" v-bind="tabs[t].attrs"></component>
    </q-tab-panel>
  </q-tab-panels>
  </div>
</template>

<script setup>
  import HouseholdTable from 'components/households/forms/HouseholdTable.vue';
  import CareWallet from 'components/enrollments/cafe/CareWallet.vue';
  import MyCoverages from 'components/enrollments/cards/MyCoverages.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useEnrollments} from 'stores/enrollments';
  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';

  const erStore = useEnrollments();
  const pplStore = usePpls();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: enrollment } = idGet({
    value: computed(() => props.modelValue),
    store: erStore
  ,
    useAtcStore
  })
  const { item:person } = idGet({
    store: pplStore,
    value: computed(() => enrollment.value.person)
  ,
    useAtcStore
  })

  const tab = ref('wallet');
  const tabs = computed(() => {
    return {
      'wallet': {
        label: 'Care Wallet',
        component: CareWallet,
        attrs: {
          enrollment: enrollment.value
        }
      },
      'coverage': {
        label: 'Coverage',
        component: MyCoverages,
        attrs: {
          enrollment: enrollment.value,
          plan: enrollment.value.plan
        }
      },
      'household': {
        label: 'Household',
        component: HouseholdTable,
        attrs: {
          disable: true,
          modelValue: person.value.household
        }
      }
    }
  })


</script>

<style lang="scss" scoped>

</style>
