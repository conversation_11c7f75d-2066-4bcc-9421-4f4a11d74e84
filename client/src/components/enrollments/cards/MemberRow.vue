<template>
  <div class="_fw">
    <div class="tw-six text-grey-8 q-px-sm">{{ $possiblyPlural(append, members, singular, plural) }}</div>
    <avatar-row :limit="20" :model-value="members" :use-atc-store="useAtcStore">
      <template v-slot:avatar="scope">
        <default-avatar
            size-in="35px"
            name-path="firstName"
            :model-value="scope.item"
            :bg-in="getGenderColor(scope.item)"
            :divStyle="{ boxShadow: '0 0 0 1px white'}"
            :use-atc-store="useAtcStore"
        ></default-avatar>
      </template>
    </avatar-row>
  </div>
</template>

<script setup>

  import {getGenderColor} from 'components/households/utils';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import AvatarRow from 'components/common/avatars/AvatarRow.vue';
  import {$possiblyPlural} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';

  const props = defineProps({
    members: { required: true },
    append: { default: '' },
    singular: { default: '' },
    plural: { default: 's' },
  })

</script>

<style lang="scss" scoped>

</style>
