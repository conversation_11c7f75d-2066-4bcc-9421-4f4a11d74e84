<template>
  <div class="_fw relative-position">
    <slot name="top"></slot>
    <q-select
        @focus="checkFocus"
        @input-value="input($event)"
        v-bind="{
          multiple: multiple,
          modelValue: Array.isArray(selected) ? selected && selected[0] ? selected : undefined : selected || undefined,
          useInput: true,
          options:h$.data || [],
          placeholder: selected ? undefined : attrs.placeholder,
          ...attrs
        }"

    >
      <template v-slot:no-option>
        <slot name="no-option">
          <q-item dense clickable @click="adding ? addDialog = true : () => console.log('no adding allowed')">
            <q-item-section avatar>
              <q-icon color="primary" name="mdi-plus"></q-icon>
            </q-item-section>
            <q-item-label header>
              New
            </q-item-label>
            <q-item-section side>
              <q-spinner color="primary" v-if="isPending"></q-spinner>
            </q-item-section>
          </q-item>
        </slot>
      </template>
      <template v-slot:before-options>
        <slot name="before-options">
          <q-item dense clickable @click="adding ? addDialog = true : () => console.log('no adding allowed')">
            <q-item-section avatar>
              <q-icon color="primary" name="mdi-plus"></q-icon>
            </q-item-section>
            <q-item-label header>
              New
            </q-item-label>
            <q-item-section side>
              <q-spinner color="primary" v-if="isPending"></q-spinner>
            </q-item-section>
          </q-item>
        </slot>
      </template>
      <template v-slot:prepend>
        <slot name="prepend"></slot>
      </template>
      <template v-slot:selected-item="scope">
        <slot name="selected-item" v-bind="scope">
          <default-chip :store="store" :chip-attrs="{ removable: true }" @remove="remove(scope.opt)" :model-value="scope.opt" :use-atc-store="useAtcStore"></default-chip>
        </slot>
      </template>
      <template v-slot:option="scope">
        <q-item class="__op q-py-xs" clickable @click="clickItem(scope.opt)" dense>
          <q-item-section avatar>
            <default-avatar :imageStyle="{ objectFit: 'contain' }" :store="store" size-in="30px" :model-value="scope.opt" :use-atc-store="useAtcStore"></default-avatar>
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ scope.opt.name }}</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-icon color="positive" v-if="isSelected(scope.opt, modelValue, { optionValue })"
                    name="mdi-checkbox-marked"></q-icon>
          </q-item-section>
        </q-item>
      </template>
    </q-select>

    <common-dialog v-model="addDialog" setting="small">
      <div class="_fw q-pa-sm">
        <q-chip color="transparent" square label="Add Org" class="tw-six"></q-chip>
        <org-edit-card @close="addDialog=false" editing @update:model-value="handleInput"></org-edit-card>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import {computed, useAttrs, ref, watch} from 'vue';
  // import { getFile } from 'src/utils/fs-utils';
  import DefaultAvatar from 'src/components/common/avatars/DefaultAvatar.vue';
  import {searchList} from 'src/utils/search-list';
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import DefaultChip from 'src/components/common/avatars/DefaultChip.vue';
  import { useOrgs } from 'src/stores/orgs';
  import OrgEditCard from 'src/components/orgs/cards/OrgEditCard.vue';
  import {HQuery} from 'src/utils/hQuery';
  import {useAtcStore} from 'src/stores/atc-store';
  const store = useOrgs();

  const attrs = useAttrs();
  const emit = defineEmits(['update:model-value', 'update:selected', 'focus']);
  const props = defineProps({
    modelValue: [Object, String],
    multiple: Boolean,
    emitValue: Boolean,
    org: { required: false },
    optionValue: { type: String, default: '_id' },
    query: Object,
    skip: { type: Number, default: 2 },
    delay: { type: Number, default: 3000 },
    adding: Boolean,
    log: Boolean,
    limit: Number
  });

  const limit = ref(5);
  const addDialog = ref(false);
  const { search, searchQ } = HQuery({})
  const { h$, clickItem, selected, isSelected, remove, isPending, handleInput, checkFocus, input } = searchList({
    store,
    query: computed(() => props.query),
    limit,
    emit
  })

  watch(() => props.limit,(nv) => {
    if(nv) limit.value = nv;
  }, { immediate: true })
</script>

<style scoped>

  .__op {
    border-bottom: solid .3px rgba(100, 100, 100, .3);
  }

</style>
