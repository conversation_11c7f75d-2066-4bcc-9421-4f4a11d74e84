<template>
  <div class="_fw">
    <div class="tw-six">Related Companies</div>
    <q-separator class="q-my-sm"></q-separator>
    <div class="text-grey-8 tw-six font-3-4r">Control Groups</div>
    <avatar-row v-if="controls?.length" :model-value="controls" :use-atc-store="useAtcStore"></avatar-row>
    <div v-else class="q-pa-sm text-italic">No known control groups</div>
    <div class="text-grey-8 tw-six font-3-4r">Affiliated Service Groups</div>
    <avatar-row v-if="asgs?.length" :model-value="asgs" :use-atc-store="useAtcStore"></avatar-row>
    <div v-else class="q-pa-sm text-italic">No known affiliated service groups</div>

  </div>
</template>

<script setup>
  import AvatarRow from 'components/common/avatars/AvatarRow.vue';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {computed} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useAtcStore} from 'src/stores/atc-store';

  const orgStore = useOrgs();

  const props = defineProps({
    org: { required: true }
  })

  const { item:fullOrg } = idGet({
    store: orgStore,
    value: computed(() => props.org)
  ,
    useAtcStore
  })

  const controlIds = computed(() => {
    const ids = [];
    for(const k in fullOrg.value?.controls || {}){
      for(const id in fullOrg.value.controls[k]?.orgs || {}){
        if(!ids.includes(id)){
          ids.push(id)
        }
      }
    }
    return ids;
  })

  const asgIds = computed(() => {
    const ids = [];
    for(const k in fullOrg.value?.asg || {}){
      for(const id in fullOrg.value.asg[k]?.orgs || {}){
        if(!ids.includes(id)){
          ids.push(id)
        }
      }
    }
    return ids;
  })

  const { h$ } = HFind({
    store: orgStore,
    params: computed(() => {
      return {
        query: {
          $limit: (controlIds.value?.length || 0) + (asgIds.value?.length || 0),
          _id: { $in: [...controlIds.value, ...asgIds.value]}
        }
      }
    })
  })

  const controls = computed(() => {
    return orgStore.findInStore({ query: { _id: { $in: controlIds.value }}}).data
  })
  const asgs = computed(() => {
    return orgStore.findInStore({ query: { _id: { $in: asgIds.value }}}).data
  })


</script>

<style lang="scss" scoped>

</style>
