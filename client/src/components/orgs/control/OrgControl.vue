<template>
  <div class="_fw __pd">
    <div class="row">

      <div class="col-12 col-md-6 q-pa-sm">

        <div class="q-py-sm">
          <related-orgs :org="org"></related-orgs>
        </div>
      </div>

      <div class="col-12 col-md-6 q-pa-sm">
        <mgmt-orgs :model-value="org"></mgmt-orgs>
      </div>

    </div>
  </div>
</template>

<script setup>

import RelatedOrgs from 'components/orgs/control/RelatedOrgs.vue';
import MgmtOrgs from 'components/orgs/control/forms/MgmtOrgs.vue';
  import {useAtcStore} from 'src/stores/atc-store';

import {idGet} from 'src/utils/id-get';
import {computed} from 'vue';
import {useOrgs} from 'stores/orgs';

const store = useOrgs();

const props = defineProps({
  modelValue: { required: true }
})

const { item: org } = idGet({
  value: computed(() => props.modelValue),
  store
,
    useAtcStore
  })
const addOwner = (val) => {
  const payload = val.sort((a, b) => b.percent || 0 - a.percent || 0);
  store.patch(org.value._id, { 'owners': payload })
};

</script>

<style lang="scss" scoped>
.__pd {
  padding: 30px min(3vw, 20px)
}
</style>
