<template>
  <div class="_fw">
    <div class="font-1r tw-six">Management Orgs</div>

    <div class="row no-wrap items-center q-py-md">
      <div>
        <q-checkbox
            v-if="!org.managementOrgs?.length"
            :model-value="!!(addMgmt || org.managementOrgs?.length)"
            @update:model-value="addMgmt = $event"
            :label="`${org?.name} has a separate organization that primarily performs management functions for it`"
        ></q-checkbox>
        <div v-else>The following orgs primarily perform management functions for {{org?.name}}</div>
      </div>
     <div class="col-shrink">
       <q-btn dense flat icon="mdi-information">
         <q-popup-proxy>
           <div class="w300 q-pa-md br10 bg-white">
             This doesn't mean just outsourcing, it means the management company's primary business activity is to perform management functions for {{org?.name}} and it's affiliated companies.
           </div>
         </q-popup-proxy>
       </q-btn>
     </div>

    </div>
    <div v-if="loading" class="_fw q-pa-md">
      <q-spinner color="primary"></q-spinner>
    </div>
    <q-slide-transition v-show="!loading">
      <div class="_fw" v-if="addMgmt || org.managementOrgs?.length">
        <q-slide-transition>
          <div class="_fw q-py-sm q-my-sm __bb" v-if="adding">
            <search-and-add-org
                @update:model-value="addOrg"
                @add="createOrg"
            ></search-and-add-org>
          </div>
        </q-slide-transition>
        <q-list separator>
          <default-item :item-attrs="{ dense: true }" v-for="(item, i) in org?.managementOrgs || []" :key="`org-${i}`" :model-value="item" :store="store" :use-atc-store="useAtcStore">
            <template v-slot:side>
              <remove-button @remove="removeOrg(item)" label="" name="Org"></remove-button>
            </template>
          </default-item>
          <add-item label="Add Management Org" @click="adding = !adding"></add-item>
        </q-list>
      </div>
    </q-slide-transition>

  </div>
</template>

<script setup>
  import AddItem from 'components/common/buttons/AddItem.vue';
  import SearchAndAddOrg from 'components/orgs/forms/SearchAndAddOrg.vue';

  import {computed, ref} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {$errNotify} from 'src/utils/global-methods';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import RemoveButton from 'components/common/buttons/RemoveButton.vue';
  import {useAtcStore} from 'src/stores/atc-store';
  const store = useOrgs();

  const props = defineProps({
    modelValue: Object
  })

  const org = computed(() => props.modelValue);

  const adding = ref(false);
  const loading = ref(false);

  const addOrg = async (val) => {
    loading.value = true;
    adding.value = false;
    await store.patch(org.value._id, { $addToSet: { managementOrgs: val._id }})
    loading.value = false;
  }

  const createOrg = async (val) => {
    loading.value = true;
    const newOrg = await store.create(val)
        .catch(err => $errNotify(`Error adding org: ${err.message}`));
    if(newOrg) addOrg(newOrg);
    loading.value = false;

  }

  const removeOrg = async (val) => {
    loading.value = true;
    await store.patch(org.value._id, { $pull: { managementOrgs: val }})
    loading.value = false;

  }

  const addMgmt = ref(false);


</script>

<style lang="scss" scoped>
  .__bb {
    border-bottom: solid .2px #999;
  }
</style>
