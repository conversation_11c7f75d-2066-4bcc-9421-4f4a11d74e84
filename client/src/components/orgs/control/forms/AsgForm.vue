<template>
  <div class="_fw">
    <div class="tw-six font-1r flex items-center">
      <div>Affiliated Service Groups (ASGs)</div>
      <q-btn dense flat icon="mdi-information">
        <q-popup-proxy breakpoint="50000">
          <div class="w400 mw100 br10 bg-white q-pa-md">
            <asg-info></asg-info>
          </div>
        </q-popup-proxy>
      </q-btn>
    </div>
    <div v-if="!org?.asg">
      <q-checkbox v-model="addingAsg" :label="`${org?.name} is primarily a service organization (professional services or where capital is not a material income producing factor)`"></q-checkbox>
    </div>
    <div v-if="loading" class="q-pa-lg">
      <q-spinner size="50px" color="primary"></q-spinner>
    </div>
    <q-slide-transition>
      <div class="_fw" v-if="(org?.asg || addingAsg) && !loading">
        <div class="row q-py-sm">
          <q-btn flat size="sm" no-caps @click="toggleAdding">
            <span class="q-mr-sm" v-if="adding">Close</span>
            <span class="q-mr-sm" v-else>Add ASG</span>
            <q-icon color="primary" :name="`mdi-${adding ? 'minus' : 'plus'}`"></q-icon>
          </q-btn>
        </div>
        <div v-for="(key, i) in Object.keys(org?.asg || {})" :key="`key-${i}`" class="_fw row cursor-pointer __c" @click="editing = key">
          <asg-card :model-value="org.asg[key]"></asg-card>
        </div>

        <q-slide-transition>
          <div class="_fw q-py-md" v-if="(adding || !!editing) && !loading">
            <asg-entry
                :asgKey="editing"
                :primary-org="primaryOrg"
                :model-value="editing ? org.asg[editing] : undefined"
                @update:model-value="addAsg"
                @remove="removeAsg"
            ></asg-entry>
          </div>
        </q-slide-transition>
      </div>
    </q-slide-transition>

  </div>
</template>

<script setup>
  import AsgCard from 'components/orgs/control/cards/AsgCard.vue';
  import {computed, ref} from 'vue';
  import AsgEntry from 'components/orgs/control/forms/AsgEntry.vue';
  import AsgInfo from 'components/orgs/control/cards/AsgInfo.vue';
  import {useOrgs} from 'stores/orgs';
  import {idGet} from 'src/utils/id-get';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useOrgs();
  const props = defineProps({
    primaryOrg: Object,
  })

  const { item: org } = idGet({
    value: computed(() => props.primaryOrg),
    store,
    refresh: true
  ,
    useAtcStore
  })

  const loading = ref(false);
  const adding = ref(false);
  const addingAsg = ref(false);
  const editing = ref(undefined);

  const toggleAdding = (val) => {
    if(val || val === false) adding.value = val;
    else adding.value = !adding.value;
    if(!val) editing.value = undefined
  }
  const addAsg = async (val, key, oldKey) => {
    loading.value = true;
    const asg = { ...(org.value.asg || {}), [key]: val };
    if(oldKey && oldKey !== key) delete asg[oldKey];
    await store.patch(org.value._id, { asg })
        .catch(err => `Error adding ASG: ${err.message}`);
    toggleAdding(false)
    loading.value = false;
  }

  const removeAsg = async (key, subKey) => {
    loading.value = true;
    const newAsg = Object.assign({}, org.value.asg);
    const spl = key.split('.');
    if(spl.length > 2){
      delete newAsg[key].orgs[subKey];
      const newKey = Object.keys(newAsg[key].orgs).join('.');
      newAsg[newKey] = { ...newAsg[key] };
    }
    delete newAsg[key]
    await store.patch(org.value._id, { $set: { 'asg': newAsg } });
    loading.value = false;
  }

</script>

<style lang="scss" scoped>
  .__c {
    padding: 15px 10px;
    border-radius: 9px;
    box-shadow: 0 0 0 1px rgba(99,99,99,.4);
    margin: 5px 0;
  }
</style>
