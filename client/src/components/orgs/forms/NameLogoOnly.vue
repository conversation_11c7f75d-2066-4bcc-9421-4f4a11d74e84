<template>
  <div class="flex items-center">
    <div class="__logo">
      <div class="__form">
        <image-form v-if="isAuthenticated" @uploading="toggleLoading" @update:model-value="setImage" :model-value="undefined" :divAttrs="{ class: '_fa'}" height="60px" width="60px" :use-atc-store="useAtcStore"></image-form>
        <upload-ui
            allow-types="image/*"
            v-else
            :div-style="{ width: '100%', height: '100%' }"
            @update:display="handleRaw"
            @update:sm="handleSm"
            :max-size="51200"
            :upload="() => undefined"
        ></upload-ui>
      </div>
      <div v-if="!useImg || loading" class="_fa br50 flex flex-center bg-ir-bg2">
        <q-spinner v-if="loading" color="primary" size="30px"></q-spinner>
        <q-icon v-else color="ir-mid" name="mdi-image" size="30px"></q-icon>
      </div>
      <q-img v-else class="_fa" fit="contain" :src="useImg"></q-img>

    </div>
    <div class="q-px-sm">
      <input :placeholder="placeholder" @blur="endName" :style="{ width: chars + 'ch' }" :class="inputClass" :value="form.name" @input="form.name  = $event.target.value" type="text">
      <slot name="hint"></slot>
    </div>
  </div>
</template>

<script setup>
  import ImageForm from 'components/common/uploads/images/ImageForm.vue';
  import UploadUi from 'components/common/uploads/components/UploadUi.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {HForm, HSave} from 'src/utils/hForm';
  import {useAtcStore} from 'src/stores/atc-store';
  import {loginPerson} from 'stores/utils/login';
  import {useOrgs} from 'stores/orgs';

  const { isAuthenticated } = loginPerson();

  const orgStore = useOrgs();

  const emit = defineEmits(['update:id', 'update:image-in', 'update:name-in', 'update:raw'])
  const props = defineProps({
    placeholder: { default: '' },
    id: { type: String },
    inputClass: { default: 'font-1r tw-six' },
    params: Object,
    nameIn: String,
    imageIn: String
  })

  const { item:org } = idGet({
    store: orgStore,
    value: computed(() => props.id)
  ,
    useAtcStore
  })

  const loading = ref(false);
  const loadTo = ref();
  const toggleLoading = (val) => {
    loading.value = val;
    if (loadTo.value) clearTimeout(loadTo.value);
    loadTo.value = setTimeout(() => {
      loading.value = false;
    }, 5000)
  }

  const { form, save } = HForm({
    store: orgStore,
    params: computed(() => props.params || {}),
    notify: false,
    value: org,
    afterFn: (val) => {
      emit('update:id', val._id);
    }
  })

  const useImg = computed(() => {
    const obj = form.value.avatar;
    if(!obj) return ''
    if(typeof obj === 'object') return obj.url;
    return obj
  })

  const handleSm = (url, blob) => {
    form.value.avatar = { url };
    emit('update:image-in', url, blob)
  }
  const handleRaw = (raw) => {
    emit('update:raw', raw)
  }

  const chars = computed(() => Math.max(form.value.name ? form.value.name.length : (props.placeholder || '').length || 5, 5))

  const { autoSave } = HSave({ store: orgStore, form, save, params: { special_change: ['name', 'avatar']}, pause: computed(() => !form.value._id) })

  const setImage = (val) => {
    form.value.avatar = val;
    if(form.value._id){
      orgStore.patchInStore(form.value._id, { avatar: val })
      autoSave('avatar');
    }
  }

  const endName = () => {
    if (!form.value._id) {
      if (form.value.name?.length > 1) {
        save()
        emit('update:name-in', form.value.name)
      }
    } else if (form.value.name !== org.value.name) {
      orgStore.patchInStore(form.value._id, { 'name': form.value.name })
      autoSave('name')
    }
  }

  watch(() => props.nameIn, (nv) => {
    if(nv && !form.value.name) form.value.name = nv;
  }, { immediate: true })
  watch(() => props.imageIn, (nv) => {
    if(nv && !form.value.avatar?.url) form.value.avatar = { nv };
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  input {
    background: transparent;
    outline: none !important;
    border: none !important;
    color: var(--ir-text);

    &:focus {
      outline: none !important;
      color: var(--ir-text);
    }
  }
  .__logo {
    height: 60px;
    width: 60px;
    cursor: pointer;
    transition: all .3s;
    position: relative;

    &:hover {
      transform: translate(0, -3px);
    }
  }
  .__form {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
    opacity: 0;
    z-index: 2;
  }
</style>
