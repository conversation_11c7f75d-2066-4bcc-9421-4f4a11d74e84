<template>
  <div class="_fw __pd">

    <div class="row q-col-gutter-sm">
      <div class="col-12 col-lg-6">
        <div class="_f_chip _f_l">Basic Info</div>
        <org-form :model-value="org"></org-form>
      </div>
      <div class="col-12 col-lg-6">
        <div class="_f_chip _f_l">Entity Details</div>
        <div class="_form_grid">
          <org-supplement :model-value="org"></org-supplement>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
  import OrgForm from 'components/orgs/forms/OrgForm.vue';
  import OrgSupplement from 'components/orgs/forms/OrgSupplement.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useOrgs} from 'stores/orgs';

  const store = useOrgs();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: org } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })
</script>

<style lang="scss" scoped>

  .__pd {
    padding: 10px min(3vw, 20px)
  }
</style>
