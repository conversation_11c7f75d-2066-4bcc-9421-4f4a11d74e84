<template>
  <div class="_fw">
    <care-filters set="cares" v-model="query" :plan="fullPlan"></care-filters>
    <plan-care-card :plan="fullPlan" :query="query"></plan-care-card>
  </div>
</template>

<script setup>
  import PlanCareCard from 'components/care/plan-admin/cards/PlanCareCard.vue';
  import CareFilters from 'components/care/utils/CareFilters.vue';
  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {useAtcStore} from 'src/stores/atc-store';

  const planStore = usePlans();
  const props = defineProps({
    plan: { required: true }
  })

  const query = ref({});

  const { item:fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  ,
    useAtcStore
  })
</script>

<style lang="scss" scoped>

</style>
