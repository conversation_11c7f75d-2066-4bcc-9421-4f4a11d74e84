<template>
  <q-page>
    <div class="row justify-center bg-ir-bg2 pd5 _fh mnh80">
      <div class="_xsent __c pw2 pd5">
        <template v-if="person._id">
          <div class="font-1-1-4r tw-six alt-font text-ir-deep">You have been invited to join:</div>

          <q-list separator class="mw500 q-py-md" v-if="org._id">
            <q-item>
              <q-item-section avatar>
                <default-avatar :model-value="org" :use-atc-store="useAtcStore"></default-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label class="font-1-1-8r tw-six text-ir-deep">{{ org.name }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>

          <div v-if="person.email" class="q-pa-md font-1-1-8r">
            Join using email <span class="text-accent">{{ person.email }}</span> to accept the invite
          </div>
          <div class="_fw q-py-md">
            <auth-card bordered :person-id="person._id"></auth-card>
          </div>

          <div class="q-pt-xl">
            <div class="row justify-center items-center">
              <ai-logo size="30px" class="q-mr-sm"></ai-logo>
              <div class="font-1-1-8r tw-six alt-font text-center">Fully private health plans - for the common person</div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="flex flex-center h400 _fw">
            <div v-if="!loaded">
              <ai-logo size="50px"></ai-logo>
            </div>
            <div v-else class="font-1r text-italic">
              No invite found for this person id
            </div>
          </div>
        </template>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import AuthCard from 'components/auth/AuthCard.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {useOrgs} from 'stores/orgs';
  import {computed, onMounted, ref} from 'vue';

  import {useRoute} from 'vue-router';

  const pplStore = usePpls();
  const orgStore = useOrgs();

  const route = useRoute();

  const { item: person } = idGet({
    store: pplStore,
    routeParamsPath: 'personId'
  ,
    useAtcStore
  })

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => route.params.orgId)
  ,
    useAtcStore
  })
  const loaded = ref(false);

  onMounted(() => {
    setTimeout(() => {
      loaded.value = true;
    }, 2000)
  })

</script>

<style lang="scss" scoped>

  .__c {
    border-radius: min(1vw, 20px);
    background: white;
    box-shadow: 0 2px 12px -4px var(--ir-light);
  }
</style>
