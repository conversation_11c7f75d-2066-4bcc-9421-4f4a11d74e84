<template>
  <div class="_fw">

    <div class="row justify-center">
      <div class="_cent pd3">
        <div class="_fw">
          <div class="q-px-md">
            <plan-year-picker class="tw-six" v-model="year"></plan-year-picker>
            <div class="font-1r text-ir-grey-8 q-px-md tw-six">Plans you are eligible to participate in</div>

          </div>
          <div class="row q-py-md">
            <div class="col-12 col-md-6 q-pa-sm" v-for="(pln, i) in p$.data || []" :key="`pln-${i}`">
              <div class="__c q-pa-md cursor-pointer" @click.stop="selectPlan(pln)">
                <plan-card :model-value="pln"></plan-card>
                <div class="row items-center">
                  <status-chip :model-value="planEnrollment(pln)?.status"></status-chip>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="q-px-xl" v-if="!p$.data?.length">
          <div class="font-1r alt-font text-ir-grey-8">No plans found for {{ org?.name }}</div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
  import PlanCard from 'components/plans/cards/PlanCard.vue';
  import StatusChip from 'components/enrollments/cards/StatusChip.vue';
  import PlanYearPicker from 'components/plans/utils/PlanYearPicker.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {HFind} from 'src/utils/hFind';
  import {computed, nextTick, ref} from 'vue';
  import {usePlans} from 'src/stores/plans';

  import {loginPerson} from 'src/stores/utils/login';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {useEnrollments} from 'stores/enrollments';
  import {useRouter} from 'vue-router';
  import {useEnvStore} from 'stores/env';
  const person = loginPerson()

  const envStore = useEnvStore();
  const planStore = usePlans();
  const enrollmentStore = useEnrollments();
  const router = useRouter();

  const year = ref(new Date().getFullYear().toString());

  const props = defineProps({
    org: { required: false }
  })

  const selectPlan = (p) => {
    if(p._id) {
      envStore.setPlanId(p._id, 'org-plans')
      nextTick(() => {
        router.push({ name: 'plan-view', params: { planId: p._id }})
      })
    }
  }

  const { item: fullOrg } = idGet({
    store: useOrgs(),
    value: computed(() => props.org || envStore.getOrgId)
  ,
    useAtcStore
  })

  const { h$:e$ } = HFind({
    store: enrollmentStore,
    limit: ref(50),
    // pause: computed(() => !person.value?._id),
    params: computed(() => {
      return {
        query: {
          $sort: { version: -1 },
          planYear: year.value,
          person: person.value?._id,
          org: fullOrg.value?._id
        }
      }
    })
  })

  const planEnrollment = (pln) => {
    return enrollmentStore.findInStore({ query: { plan: pln._id } }).data[0]
  }

  const { h$:p$ } = HFind({
    store: planStore,
    limit: ref(25),
    pause: computed(() => !e$.data?.length),
    params: computed(() => {
      return {
        query: {
          // _id: { $in: e$.data?.map(a => a.plan) },
          org: fullOrg.value?._id
        }
      }
    })
  })


</script>

<style lang="scss" scoped>
  .__cg {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 450px));
    grid-template-rows: repeat(auto-fit, auto);
    grid-gap: 10px;
  }

  .__c {
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .15);
    background: white;
  }
</style>
