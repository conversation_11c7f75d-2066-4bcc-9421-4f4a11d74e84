<template>
  <div class="_fw q-pa-md">
    <div class="row justify-center q-pt-lg">
      <default-avatar :model-value="org" :imageStyle="{ objectFit: 'contain' }" :use-atc-store="useAtcStore"></default-avatar>
    </div>
    <div class="text-center q-py-md text-weight-bold font-1-1-4r">{{ org?.name }}</div>
    <div class="row justify-end">
      <q-btn flat size="sm" dense icon="mdi-open-in-new" @click="openGroup"></q-btn>
    </div>
  </div>
</template>

<script setup>
  import DefaultAvatar from 'src/components/common/avatars/DefaultAvatar.vue';
  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';

  import {useRouter} from 'vue-router';
  import {useAtcStore} from 'src/stores/atc-store';

  const router = useRouter();

  import {useOrgs} from 'src/stores/orgs';

  const store = useOrgs();

  const props = defineProps({
    modelValue: { required: true }
  })

  const dialog = ref(false);
  const openGroup = () => {
    router.push({ name: 'my-groups', params: { id: org.value._id } })
  }

  const { item: org } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })

</script>

<style lang="scss" scoped>

</style>
