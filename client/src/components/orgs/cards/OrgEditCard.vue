<template>
  <div class="q-pa-lg _fw font-1r">

    <!--    NAME-->
    <editable-item @click="touch('name')" :editing="isEdit('name')">
      <template v-slot:left>
        <div class="row justify-center">
          <default-avatar :imageStyle="{ objectFit: 'contain' }" class="q-mr-sm" size-in="30px" :model-value="form" :use-atc-store="useAtcStore">
            <q-tooltip>Name</q-tooltip>
          </default-avatar>
        </div>
      </template>
      <template v-slot:editing>
        <q-input
            dense
            filled
            @dblclick="touch('name')"
            @focus="edit = 'name'"
            @blur="maybeSave()"
            @keyup.enter="maybeSave()"
            :model-value="form.name"
            @update:model-value="update($event, 'name')"
            label="Name"
        ></q-input>
      </template>
      <template v-slot:default>
        <q-chip v-bind="chip" :label="form.name" @click="edit = 'name'"></q-chip>
      </template>
    </editable-item>
    <!--    END NAME-->

    <!--    PHONE-->
    <editable-item
        v-if="fields['phone']"
        label="Phones"
        tooltip="Phones"
    >
      <template v-slot:right>
        <primary-list-input
            type="phone"
            :show="edit === 'phone'"
            :plural="form.phones"
            :single="form.phone"
            :chip="chip"
            :field-attrs="{ filled: true }"
            placeholder=""
            @edit="edit = 'phone'"
            @update:plural="update($event, 'phones')"
            @update:single="update($event, 'phone')"
        ></primary-list-input>
      </template>
    </editable-item>
    <!--    END PHONE-->

    <!--    EMAIL-->
    <editable-item
        v-if="fields['email']"
        label="Emails"
        tooltip="Emails"
    >
      <template v-slot:right>
        <primary-list-input
            type="email"
            :show="edit === 'email'"
            :plural="form.emails"
            :single="form.email"
            :chip="chip"
            @edit="edit = 'email'"
            :field-attrs="{ filled: true }"
            @update:plural="update($event, 'emails')"
            @update:single="update($event, 'email')"
        ></primary-list-input>
      </template>
    </editable-item>
    <!--    END EMAIL-->


    <!--    STRUCTURE-->
    <editable-item
        v-if="fields['structure']"
        :editing="isEdit('structure')"
        label="Legal Structure"
        tooltip="Entity Structure"
    >
      <template v-slot:editing>
        <org-structure-picker
            :model-value="form.structure"
            @update:model-value="update($event, 'structure')"
            dense
            filled
        ></org-structure-picker>
      </template>
      <template v-slot:default>
        <q-chip v-bind="chip" @click="edit = 'structure'"
                :label="allStructures[form.structure || { label: 'Unknown'}].label"></q-chip>
      </template>
    </editable-item>
    <!--    END STRUCTURE-->

    <div class="row">
      <div class="col-12 col-sm-6 q-pa-sm" v-if="fields['owners']">
        <owns-card
            @update:model-value="addOwner"
            :editing="editing"
            :org="form"
        ></owns-card>
      </div>
<!--      <div class="col-12 col-sm-6 q-pa-sm" v-if="fields['owns']">-->
<!--        <owns-card-->
<!--            @update:model-value="addOwns"-->
<!--            :editing="editing"-->
<!--            :org="form"-->
<!--            path="owns"-->
<!--        ></owns-card>-->
<!--      </div>-->
    </div>

    <div class="__sep">
      <q-separator></q-separator>
    </div>

    <div class="col-12 q-py-sm" v-if="fields['projects']">
<!--      <project-list :projects="org.projects || []" :org="org"></project-list>-->
    </div>

    <div v-if="manualSave || !org?._id" class="row justify-end q-pt-md">
      <q-btn push label="save org" color="primary" @click="save()"></q-btn>
    </div>
  </div>
</template>

<script setup>
  import DefaultAvatar from 'src/components/common/avatars/DefaultAvatar.vue';
  import PrimaryListInput from 'src/components/common/input/PrimaryListInput.vue';
  import OwnsCard from 'src/components/orgs/cards/OwnsCard.vue';
  import OrgStructurePicker from 'src/components/orgs/forms/OrgStructurePicker.vue';
  import EditableItem from 'src/components/common/forms/EditableItem.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {HForm} from 'src/utils/hForm';
  import {useOrgs} from 'src/stores/orgs';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';

  import {$copyTextToClipboard} from 'src/utils/global-methods';

  import {allStructures} from '../forms/org-structures';
  // import ProjectList from 'src/components/projects/cards/ProjectList.vue';

  const orgStore = useOrgs();

  const emit = defineEmits(['update:model-value', 'close']);
  const props = defineProps({
    modelValue: Object,
    editing: Boolean,
    size: { type: String, default: 'md' },
    manualSave: Boolean,
    addFields: { type: Object },
    showFields: {
      type: Object, default: () => {
        return {
          name: true,
          avatar: true,
          email: true,
          phone: true,
          industries: true
        };
      }
    }
  });

  const fields = computed(() => {
    return { ...props.showFields, ...props.addFields };
  });

  const edit = ref('');
  const isEdit = (key) => {
    return props.editing && (edit.value === key || !form.value[key]);
  };
  const copy = (val) => {
    $copyTextToClipboard(val, 'copied to clipboard');
  };

  const touch = (key) => {
    if (props.editing) edit.value = key;
  };


  const runJoin = computed(() => {
    const obj = {};
    if (fields.value.owners) obj.owners = true;
    if (fields.value.owns) obj.owns = true;
    return obj;
  });

  const mv = computed(() => props.modelValue);

  const { item: org, refreshItem, loadedOnce } = idGet({
    value: mv,
    store: orgStore,
    params: computed(() => {
      return { runJoin: runJoin.value }
    })
  });

  let timeout = null;

  const maybeSave = () => {
    if (form.value?._id) {
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        save();
      }, 5000);
    }
  };


  const chip = computed(() => {
    return {
      color: 'background',
      size: props.size,
      clickable: true
    };
  });


  const { form, save, updateForm } = HForm({
    store: orgStore,
    value: org,
    resetOnSave: false,
    notify: false,
    addToStore: true,
    errWatch: true,
    afterFn: (val) => {
      emit('update:model-value', val);
      emit('close');
    },
    vOpts: ref({
      'name': { name: 'name', v: ['notEmpty'] }
    })
  });

  const update = (val, key) => {
    edit.value = key;
    updateForm(val, key);
    maybeSave();
    edit.value = key;
  };

  const addOwner = val => {
    updateForm(val.sort((a, b) => b.percent || 0 - a.percent || 0), 'owners');
    maybeSave();
  };

  const addOwns = val => {
    updateForm([...(form.value.owns || []).filter(a => a.id !== val.id), val].sort((a, b) => b.percent || 0 - a.percent || 0), 'owns');
    maybeSave();
  };

  watch(mv, (nv) => {
    if (nv && !loadedOnce.value && (fields.value?.owners || fields.value?.owns) && !nv._fastjoin) {
      console.log('loading once');
      refreshItem();
    }
  }, { immediate: true });

</script>

<style scoped>
  .__sep {
    width: 100%;
    padding: 5px 5%;
  }

  .__menu_wrap {
    position: absolute;
    top: 0;
    left: 0;
  }
</style>
