<template>
  <div>
    <div class="row q-pb-md">
      <q-btn size="sm" flat icon="mdi-plus" label="Add Org" @click="dialog = true"></q-btn>
    </div>

    <slot name="ownTitle" :data="ownsData.data">
      <div class="font-1r text-weight-bold q-px-md">Owned by <span
          class="text-primary">{{ person?.name || 'me' }}</span></div>
    </slot>

    <div class="_fw q-px-lg q-pb-lg text-weight-bold alt-font text-ir-grey-8" v-if="!ownsData.total">
      No Orgs Owned
    </div>
    <div class="row q-py-md" v-else>
      <default-item
          v-for="(org, i) in ownsData.data || []" :key="`ownOrg-${i}`"
          :model-value="org"
          :use-atc-store="useAtcStore"
          @click="openOrg(org)"
      >
      </default-item>
    </div>

    <slot name="inTitle" :data="inData.data">
      <div class="font-1r text-weight-bold q-px-md"><span class="text-primary">{{ person?.name }}</span> is a
        member of
      </div>
    </slot>

    <div class="_fw q-px-lg q-pb-lg text-weight-bold alt-font text-ir-grey-8" v-if="!(inData.total)">
      Not in any orgs
    </div>

    <div v-else class="row q-py-md">
      <default-item
          v-for="(org, i) in inData.data || []" :key="`inOrg-${i}`"
          :model-value="org"
          :use-atc-store="useAtcStore"
          @click="openOrg(org)"
      >
        <template v-slot:side>
          <q-icon name="mdi-open-in-new" color="secondary"></q-icon>
        </template>
      </default-item>
    </div>

    <common-dialog v-model="dialog">
      <div class="q-pa-md _fw">
        <org-form title="Add Org" @update:model-value="dialog = false"></org-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import OrgForm from 'src/components/orgs/forms/OrgForm.vue';
  import DefaultItem from 'src/components/common/avatars/DefaultItem.vue';

  import {useOrgs} from 'src/stores/orgs';
  import { useRouter } from 'vue-router';
  import {useAtcStore} from 'src/stores/atc-store';

  import { ref, watch} from 'vue';
  import {loginPerson} from 'src/stores/utils/login';
  const { person } = loginPerson()

  const dialog = ref(false);
  const orgStore = useOrgs();
  const router = useRouter();

  const inData = ref({ total: 0, data: [] });


  const ownsData = ref({ total: 0, data: [] })

  const openOrg = (o) => {
    const { href } = router.resolve({ name: 'my-groups', params: { id: o._id } });
    window.open(href, '_blank');
  }

  watch(person, async (nv, ov) => {
    if(nv && nv._id !== ov?._id){
      inData.value = await orgStore.find({
        query: {
          search_members: [person.value?._id]
        }
      })

      ownsData.value = await orgStore.find({
        query: {
          '_owners': { id: person.value?._id }
        }
      })
    }
  }, { immediate: true})

</script>

<style lang="scss" scoped>

</style>
