<template>
  <div class="_fw">
    <div class="row q-pb-sm" v-if="!selected">
      <q-chip v-bind="chip.ppls" @click="setService('ppls')"></q-chip>
      <q-chip v-bind="chip.orgs" @click="setService('orgs')"></q-chip>
      <q-chip color="transparent" clickable @click="addDialog = true">
        <q-icon size="20px" color="primary" name="mdi-plus"></q-icon>
        <span>Add Owner</span>
      </q-chip>
    </div>
    <q-input
        v-if="!selected"
        outlined
        dense
        :placeholder="idService === 'orgs' ? 'Search Companies...' : 'Search People...'"
        v-model="search.text"
    >
    </q-input>

    <q-slide-transition v-if="!selected?._id && options?.length">
      <q-list separator>
        <default-item
            v-for="(opt, i) in options" :key="`opt-${i}`"
            :model-value="opt"
            :use-atc-store="useAtcStore"
            @update:model-value="setSelected(opt)"
        ></default-item>
      </q-list>
    </q-slide-transition>

    <q-slide-transition>
      <div class="_form_grid" v-if="selected?._id">
        <div class="_form_label">Owner</div>
        <div class="q-pa-sm">
          <default-chip @remove="removeOwner" :chip-attrs="{ removable: true, iconRemove: 'mdi-close', color: 'transparent' }" :model-value="selected" :use-atc-store="useAtcStore">
          </default-chip>
        </div>

        <div class="_form_label">Owns(%)</div>
        <div class="q-pa-sm">
          <div class="mxw100">
            <money-input
                hide-bottom-space
                dense
                filled
                :model-value="form.percent"
                :decimal="2"
                prefix=""
                @update:model-value="checkPercent"
                suffix="%"
                :hint="over ? 'Total ownership cannot exceed 100%' : ''"
            ></money-input>
          </div>
        </div>

        <div class="_form_label">Title</div>
        <div class="q-pa-sm">
          <title-chip
              v-model="form.position"
              v-bind="{
              editing: true,
              size: 'md',
              color: 'ir-grey-4',
              iconRight: 'mdi-menu-down'
            }"
          ></title-chip>
        </div>

      </div>
    </q-slide-transition>

    <common-dialog v-model="addDialog" setting="small">
      <q-card class="q-pa-md">
        <div class="font-3-4r">Owner Type:</div>
        <div class="row">
          <q-chip v-bind="chip.ppls" @click="setService('ppls')"></q-chip>
          <q-chip v-bind="chip.orgs" @click="setService('orgs')"></q-chip>
        </div>
        <div>
          <quick-add
              v-if="!refresh"
              :store="stores[idService]"
              @update:model-value="setSelected"
          ></quick-add>
        </div>
      </q-card>
    </common-dialog>

    <q-slide-transition>
      <div v-if="form?.id" class="row justify-end q-py-sm">
        <q-btn icon="mdi-content-save" no-caps class="tw-six" push size="sm" :label="saveMsg" @click="save"></q-btn>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import QuickAdd from 'src/components/orgs/forms/QuickAdd.vue';
  import DefaultItem from 'src/components/common/avatars/DefaultItem.vue';
  import TitleChip from 'src/components/orgs/owners/TitleChip.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, nextTick, ref, watch} from 'vue';
  import {loginPerson} from 'stores/utils/login';
  const { person } = loginPerson()

  import {usePpls} from 'src/stores/ppls';
  import {useOrgs} from 'src/stores/orgs';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';

  const pplsStore = usePpls();
  const orgStore = useOrgs();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    saveMsg: { default: 'Add Owner' },
    org: { required: true },
    modelValue: Object,
    path: { type: String, default: 'owners' }
  });

  const idService = ref('ppls');
  const refresh = ref(false);
  const stores = computed(() => {
    return {
      'orgs': orgStore,
      'ppls': pplsStore
    };
  });
  const setService = (val) => {
    form.value.idService = val;
    refresh.value = true;
    idService.value = val;
    nextTick(() => {
      refresh.value = false;
    })
  }


  const chip = computed(() => {
    return {
      'ppls': {
        label: 'Person',
        dark: idService.value === 'ppls',
        color: idService.value === 'ppls' ? 'black' : 'white',
        clickable: true,
        icon: 'mdi-face-man'
      },
      'orgs': {
        label: 'Entity',
        dark: idService.value === 'orgs',
        color: idService.value === 'orgs' ? 'black' : 'white',
        clickable: true,
        icon: 'mdi-domain'
      }
    };
  });

  const { search, searchQ } = HQuery({})

  const { h$: p$ } = HFind({
    store: pplsStore,
    pause: computed(() => !person.value || idService.value !== 'ppls'),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value
        }
      }
    })
  })
  const { h$: o$ } = HFind({
    store: orgStore,
    pause: computed(() => !person.value || idService.value !== 'orgs'),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value
        }
      }
    })
  })

  const options = computed(() => {
    if (idService.value === 'ppls') return p$.data || [];
    else return o$.data || [];
  })

  const addDialog = ref(false);

  const selected = ref(undefined);
  const setSelected = (val) => {
    selected.value = val;
    form.value.id = val?._id;
    addDialog.value = false
  }

  const formFn = (defs) => {
    return {
      percent: 0,
      position: '',
      id: undefined,
      did: undefined,
      idService: 'ppls',
      ...defs
    };
  };

  const form = ref(formFn());
  const over = ref(false);

  const checkPercent = (val) => {
    const percent = Number(val);
    const existing = (props.org?.owners || []).filter(a => a.id !== props.modelValue?._id).reduce((acc, v) => acc + (v.percent || 0), 0);
    if ((existing + percent) > 100) {
      over.value = true;
      form.value.percent = 100 - existing;
    } else {
      over.value = false;
      form.value.percent = percent;
    }
  }

  const save = () => {
    emit('update:model-value', form.value);
    emit('close');
  }
  const removeOwner = () => {
    form.value.id = undefined;
    selected.value = undefined;
  }

  // const available = computed(() => {
  //   let used = 0;
  //   let list = _get(props.org, props.path, []).map(a => a);
  //   let idx = list.map(a => a.id).indexOf(form.value.id);
  //   if (idx > -1) list.splice(idx, 1);
  //   if (list) {
  //     list.forEach(owner => {
  //       used += owner.percent;
  //     });
  //   }
  //   return 100 - used - _get(form.value, 'percent', 0);
  // });

  const mv = computed(() => props.modelValue);
  watch(mv, async (nv) => {
    if (nv?._id) {
      selected.value = nv;
      form.value.id = nv._id;
      form.value.position = nv.position;
      form.value.idService = nv.idService;
      form.value.attribute = nv.attribute;
      form.value.percent = nv.percent;
      form.value.did = nv.did;
    } else if (nv?.id) {
      const store = nv.idService === 'orgs' ? orgStore : pplsStore;
      selected.value = await store.get(nv._id);
      form.value.id = selected.value?._id;
    } else selected.value = undefined;
  }, { immediate: true })

</script>

<style scoped>
  .__remain {
    font-size: .75rem;
    font-weight: 500;
    transform: translate(0, -30%);
  }
</style>
