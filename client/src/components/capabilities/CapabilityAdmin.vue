<template>
  <q-page class="q-pa-md">
    <div class="row q-py-sm">
      <div class="col-12 col-md-6">
      <q-input rounded outlined label="Search" v-model="search.text">
        <template v-slot:prepend>
          <q-icon name="mdi-magnify"></q-icon>
        </template>
      </q-input>
      </div>
    </div>
    <q-table
        flat
        :columns="columns"
        :rows="h$.data"
        hide-no-data
        hide-bottom
        hide-pagination
    >
      <template v-slot:header="scope">
        <!--        <q-th auto-width></q-th>-->
        <q-th
            v-for="col in scope.cols"
            :key="col.name"
            :props="scope"
        >
          {{ col.label }}
        </q-th>
      </template>
      <template v-slot:body="scope">
        <q-tr :props="scope">
          <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
            <component v-if="col.component" :is="col.component" v-bind="col.attrs(scope.row)"></component>
            <div v-else>{{ col.value }}</div>
          </q-td>
        </q-tr>
      </template>
    </q-table>
    <div class="row justify-end">
      <q-pagination
          @update:model-value="h$.toPage($event)"
          :model-value="pagination.currentPage"
          :min="1"
          :max="pagination.pageCount"
          direction-links
          boundary-numbers
      ></q-pagination>
    </div>
  </q-page>
</template>

<script setup>
  import DidChip from './cards/DidChip.vue';
  import TdChip from 'src/components/common/tables/TdChip.vue';
  import DefaultChip from 'src/components/common/avatars/DefaultChip.vue';
  import CapChip from 'src/components/capabilities/cards/CapChip.vue';
  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useLogins} from 'src/stores/logins';
  import {HQuery} from 'src/utils/hQuery';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useLogins();
  import {_get} from 'symbol-syntax-utils';

  const search = ref({ text: '', keys: ['name', 'email', 'phone.number.e164'] });

  const columns = computed(() => {
    return [
      {
        label: 'Owner',
        name: 'owner',
        component: DefaultChip,
        attrs: (row) => {
          return {
            modelValue: _get(row, '_fastjoin.owner', row),
            backupNamePath: 'username',
            defaultName: _get(row, 'username', row.email),
            useAtcStore: useAtcStore
          }
        }
      },
      {
        name: 'email',
        label: 'Email',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: _get(row, '_fastjoin.owner.email', _get(row, 'email'))
            }
          }
        }
      },
      {
        name: 'phone',
        label: 'Phone',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: _get(row, '_fastjoin.owner.phone.number.national', _get(row, 'phone'))
            }
          }
        }
      },
      {
        name: 'did',
        label: 'DID',
        component: DidChip,
        attrs: (row) => {
          return {
            color: 'white',
            label: row.did
          }
        }
      },
      {
        name: 'ucan',
        label: 'Ucan',
        component: CapChip,
        attrs: (row) => {
          return {
            subject: row,
            store: store,
            ucanPath: 'ucan',
            subjectService: 'logins'
          }
        }
      }
    ].map(a => {
      return {
        label: a.name,
        sortable: true,
        align: 'left',
        field: a.name,
        ...a
      };
    })
  });

  const query = computed(() => {
    return {
      $limit: 2
    }
  })
  const { searchQ } = HQuery({
    search,
    query
  })

  const params = computed(() => {
    return {
      query: searchQ.value
    }
  })
  const { h$, pagination } = HFind({
    store,
    params,
    qid: 'CapAdmin',
    limit: ref(10)
  })
</script>

<style scoped>

</style>
