<template>
  <div class="_fw">

    <div class="_f_l _f_chip">Help us take CARE of you</div>
    <div class="q-pa-sm">
      <div class="q-pa-sm tw-five">
        A little bit of information so we can help make your experience smooth by:
        <ul>
          <li>Finding the best provider options for you.</li>
          <li>Negotiating price (even if you bring your own doc)</li>
          <li>Working to eliminate unnecessary steps</li>
        </ul>
      </div>
    </div>

    <q-tab-panels class="_panel" v-model="tab" animated>
      <q-tab-panel class="_panel" name="menu">

        <div class="text-sm text-primary tw-six q-pa-md">I am trying to</div>

        <div class="q-pa-sm _fw">
          <div class="__c" @click="tab = 'form'">
            <div class="q-pa-sm font-1-1-8r tw-six flex items-center">
              <q-icon class="font-1-1-2r q-mr-sm" color="primary" name="mdi-calendar"></q-icon>
              <div>Schedule Care</div>
            </div>
            <div class="q-pa-sm font-1r">
              Find high quality doctors, labs, and meds and get help understanding how they are covered. If you already have a doctor - let's make sure we know how to pay them.
            </div>
          </div>
          <div class="__c" @click="$router.push({ name: 'quick-claim' })">
            <div class="q-pa-sm font-1-1-8r tw-six flex items-center">
              <q-icon class="font-1-1-2r q-mr-sm" color="secondary" name="mdi-receipt"></q-icon>
              <div>Submit Expenses</div>
            </div>
            <div class="q-pa-sm font-1r">
              Already got care and have a bill? Don't pay that thing until you've submitted it here! We'll work out the rates and what's covered.
            </div>
          </div>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="form">

        <div class="_f_g">

          <div class="_f_l _f_chip">
            Details
          </div>

          <div class="_form_grid">
            <div class="_form_label">Patient</div>
            <div class="q-pa-sm">
              <hh-member-select
                  auto-close
                  v-model="form.patient"
                  emit-value
                  :person="fullPerson"
              >
              </hh-member-select>
            </div>
            <div class="_form_label">Preventive</div>
            <div class="q-pa-sm">
              <q-checkbox label="Preventive care event" :model-value="!!form.preventive"
                          @update:model-value="form.preventive = $event"></q-checkbox>
            </div>
            <div class="_form_label">Illness/Injury</div>
            <div class="q-pa-sm">
              <conditions-manager v-model="form.conditions"></conditions-manager>
            </div>
            <div class="_form_label">Date Issue Began</div>
            <div class="q-pa-sm">
              <inline-date v-model="form.initDate"></inline-date>
            </div>

            <div class="_form_label">
              Target Treatment Date
            </div>
            <div class="q-pa-sm">
              <inline-date v-model="form.targetDate"></inline-date>
            </div>

            <div class="_form_label">Priority</div>
            <div class="q-pa-sm">
              <priority-picker type="patient" v-model="form.patientPriority"></priority-picker>
              <div v-if="(form.patientPriority || 0) > 3" class="font-1r text-s7 q-pt-sm tw-five">
                If you are experiencing a medical emergency, seek emergency care. This tool is for sourcing and
                arranging payment for care, not dispatching medical help.
              </div>
            </div>

          </div>
        </div>

        <div class="_f_l _f_chip">Choose a Doctor/Provider (Or we can help you find one)</div>
        <div class="q-pa-sm">
          <div class="font-7-8r tw-five text-grey-6 q-py-md">Doctors love our process 💕. We help them work directly
            with patients and groups, facilitate fast cash payments, and allow them to benefit from outcomes instead
            of
            transactions
          </div>
          <care-provider
              v-model:practitioners="form.practitioners"
              v-model:providers="form.providers"
          ></care-provider>
        </div>


        <div class="_f_l _f_chip _fw" @click="on === ''">
          <div>Medical Documents (not bills)</div>
        </div>

        <div class="q-pa-sm">
          <file-object-form v-model="form.files"></file-object-form>
        </div>

        <div class="q-py-md row justify-end">
          <q-btn push class="tw-six _p_btn" no-caps label="Submit Care Event" @click="save"
                 icon-right="mdi-chevron-right"></q-btn>
        </div>

      </q-tab-panel>
    </q-tab-panels>


  </div>


</template>

<script setup>
  import HhMemberSelect from 'components/households/lists/HhMemberSelect.vue';
  import ConditionsManager from 'components/care/conditions/forms/ConditionsManager.vue';
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import CareProvider from 'components/care/cards/CareProvider.vue';
  import FileObjectForm from 'components/common/uploads/utils/FileObjectForm.vue';
  import PriorityPicker from 'components/care/forms/PriorityPicker.vue';
  import {useAtcStore} from 'src/stores/atc-store';


  import {computed, ref} from 'vue';
  import {HForm} from 'src/utils/hForm';
  import {idGet} from 'src/utils/id-get';
  import {useCares} from 'stores/cares';
  import {usePlans} from 'stores/plans';
  import {loginPerson} from 'stores/utils/login';
  import {usePpls} from 'stores/ppls';

  const { person } = loginPerson()

  const careStore = useCares();
  const planStore = usePlans();
  const pplsStore = usePpls();

  const props = defineProps({
    modelValue: { required: false },
    plan: { required: true },
    personId: { required: false }
  })

  const tab = ref('menu')

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  ,
    useAtcStore
  })

  const { item: fullPerson } = idGet({
    store: pplsStore,
    value: computed(() => props.personId || person.value)
  ,
    useAtcStore
  })

  const formFn = (defs) => {
    return {
      status: 0,
      patientPriority: 1,
      ...defs
    }
  }

  const { form, save } = HForm({
    store: careStore,
    formFn,
    value: computed(() => props.modelValue),
    beforeFn: (val) => {
      if (fullPlan.value?._id) {
        val.plan = fullPlan.value._id
        val.org = fullPlan.value.org
      }
      if (!val.person) val.person = fullPerson.value?._id;
      return val;
    }
  })


</script>

<style lang="scss" scoped>
  .__ul {
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 5px;

    .__li {
      display: grid;
      grid-template-columns: auto 1fr;

      div {
        padding: 0 5px;
      }
    }
  }

  .__d {
    width: 100%;
  }

  .__ex {
    transition: transform .2s;
    transform: none;
  }

  .__flip {
    transform: rotate(180deg);
  }

  .__ch {
    cursor: pointer;
    transition: all .2s;

    &:hover {
      background: var(--q-p2);
    }
  }

  .__c {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 2px 6px var(--ir-light);
    padding: 20px;
    background: white;
    margin: 0 0 20px 0;
    transition: all .3s;
    cursor: pointer;

    &:last-child {
      margin: 20px 0 0 0;
    }

    &:hover {
      transform: translate(0, -3px);
      box-shadow: 0 2px 12px var(--ir-light);
    }
  }
</style>
