<template>
  <div class="_fw">
    <div class="row items-center">
      <div class="tw-six font-7-8r">{{care?.name || 'Untitled Event'}}</div>
    </div>


    <div class="row items-center">
      <span class="font-1r">📋&nbsp;&nbsp;</span>
      <template v-if="care?.conditions">
        <div>{{ $limitStr(care.conditions[0].name || care.conditions[0].notes, 40, '...') }}</div>
        <div v-if="care.conditions.length > 1" class="text-grey-7">&nbsp; +{{ care.conditions.length - 1 }}
          more
        </div>
      </template>
      <div v-else class="text-italic tw-five text-grey-7">No symptoms/conditions listed</div>
    </div>
    <div class="row items-center">
      <q-icon color="light-blue" name="mdi-calendar" class="q-mr-sm"></q-icon>
      <div>
        <span class="font-3-4r tw-six text-grey-7">Began:&nbsp;</span>
        <span>{{ $ago(care?.initDate || care?.createdAt, 'MM/DD/YYYY') }}</span>
      </div>
    </div>
    <div class="row items-center">
      <q-icon color="red-8" name="mdi-bullseye" class="q-mr-sm"></q-icon>
      <div>
        <span class="font-3-4r tw-six text-grey-7">Target:&nbsp;</span>
        <span v-if="care?.targetDate">{{ formatDate(care?.targetDate, 'MM/DD/YYYY') }}</span>
        <span v-else>ASAP</span>
      </div>
    </div>
    <div class="row items-center q-pt-sm">
      <div class="font-1-1-4r">
        <priority-emoji type="patient" :model-value="care?.patientPriority"></priority-emoji>
      </div>
      <q-space></q-space>
      <div>
        <q-chip color="white" v-if="!care?.patient">
          <q-avatar size="20px" color="grey-6">
            <q-icon color="white" name="mdi-help"></q-icon>
          </q-avatar>
          <span class="q-ml-sm">No patient listed</span>
        </q-chip>
        <member-chip v-else :model-value="care.patient"></member-chip>
      </div>
    </div>
  </div>
</template>

<script setup>
  import MemberChip from 'components/households/cards/MemberChip.vue';
  import PriorityEmoji from 'components/care/cards/PriorityEmoji.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useCares} from 'stores/cares';
  import {$ago, $limitStr} from 'src/utils/global-methods';
  import {formatDate} from 'src/utils/date-utils';


  const store = useCares();
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item:care } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })
</script>

<style lang="scss" scoped>

</style>
