<template>
  <div class="flex items-center">
  <q-chip v-bind="{iconRight: picker ? 'mdi-menu-down' : undefined, color:'white', ...$attrs}">
    <q-avatar size="20px" :color="statuses[modelValue]?.color || 'grey-4'"></q-avatar>
    <span class="q-ml-sm">{{multiple ? 'Choose Status' : statuses[modelValue]?.label || (picker ? 'Choose Status' : 'Unknown')}}</span>
    <q-menu v-if="picker">
      <div class="_fw mw300">
        <q-list separator>
          <q-item v-for="status in Object.keys(statuses)" :key="`status-${status}`" clickable @click="toggle(status)">
            <q-item-section avatar>
              <q-avatar size="30px" :color="statuses[status].color"></q-avatar>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{statuses[status].label}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-menu>
  </q-chip>
    <template v-if="multiple && picker">
      <avatar-row :limit="10" :model-value="modelValue || []" :use-atc-store="useAtcStore">
        <template v-slot:avatar="scope">
          <q-avatar :color="statuses[scope.item]?.color" size="25px" class="cursor-pointer" @click="toggle(scope.item)">
          </q-avatar>
        </template>
      </avatar-row>
    </template>
  </div>
</template>

<script setup>
  import AvatarRow from 'components/common/avatars/AvatarRow.vue';

  import { statuses } from '../utils';
  import {useAtcStore} from 'src/stores/atc-store';

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: [Number,String,Array],
    picker: Boolean,
    multiple: Boolean,
    set: { enum: ['cares', 'claims']}
  })

  const toggle = (val) => {
    const num = Number(val);
    if(props.multiple) {
      if(!props.modelValue) emit('update:model-value', [num]);
      else {
        const idx = props.modelValue.indexOf(num);
        if(idx > -1){
          const arr = [...props.modelValue];
          arr.splice(idx, 1);
          emit('update:model-value', arr);
        } else emit('update:model-value', [...props.modelValue, num]);
      }
    } else emit('update-model-value', num);
  }
</script>

<style lang="scss" scoped>

</style>
