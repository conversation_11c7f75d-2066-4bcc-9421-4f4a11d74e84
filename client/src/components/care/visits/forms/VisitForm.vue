<template>
  <div class="_fw">

    <div class="row">
      <div class="col-12 q-pa-sm">
        <div class="_f_g">
          <div class="_f_l _f_chip">Visit Detail</div>

          <div class="_form_grid">
            <div class="_form_label">Preventive</div>
            <div class="q-pa-sm">
              <q-checkbox label="Preventive care event" :model-value="!!form.preventive" @update:model-value="setForm('preventive', $event)"></q-checkbox>
            </div>
            <div class="_form_label">Emergency Room</div>
            <div class="q-pa-sm">
              <q-checkbox label="Emergency Room Visit" :model-value="!!form.er" @update:model-value="setForm('er', $event)"></q-checkbox>
            </div>
            <!--      PROVIDER-->
            <div class="_form_label">Provider</div>
            <div class="q-pa-sm flex items-center">
              <provider-chip empty-label="Choose Provider" :model-value="form.provider" :chip-attrs="{ iconRight: 'mdi-menu-down', color: 'transparent'}">
                <template v-slot:menu>
                  <q-menu>
                    <div class="bg-white _fw mw600 q-pa-md">
                      <q-list separator>
                        <q-item-label header>Choose Provider</q-item-label>
                        <add-provider-item emit-value @update:model-value="autoSave('provider')" v-model="form.provider"></add-provider-item>
                        <provider-item
                            simple
                            v-for="(p, i) in hh?.providers || []" :key="`p-${i}`"
                            :model-value="p"
                            @update:model-value="setForm('provider', p)">
                          <template v-slot:side>
                            <q-item-section side v-if="form.provider === p">
                              <q-icon name="mdi-check" color="green"></q-icon>
                            </q-item-section>
                          </template>
                        </provider-item>
                      </q-list>
                    </div>
                  </q-menu>
                </template>
              </provider-chip>
            </div>

            <!--      PRACTITIONERS-->
            <template v-if="form?._id">

              <div class="_form_label">Doctor/<br>Practitioners</div>
              <div class="q-pa-sm">
                <div class="row items-center">
                  <practitioner-chip
                      v-for="(id, i) in form.practitioners || []" :key="`doc-${i}`"
                      :model-value="id"
                      :chip-attrs="{ removable: true,iconRemove: 'mdi-close' }"
                      @remove="removeDoc(id)"
                  ></practitioner-chip>
                  <q-chip>
                    <span class="tw-five">Add</span>
                    <q-icon class="q-ml-sm" color="primary" name="mdi-plus"></q-icon>
                    <q-menu>
                      <div class="q-pa-md _fw mw500">
                        <q-list separator>
                          <q-item-label header>Choose from your practitioners</q-item-label>
                          <add-practitioner-item @update:model-value="addPractitioner"></add-practitioner-item>
                          <practitioner-item
                              v-for="(pr, i) in hh?.practitioners || []" :key="`pr-${i}`"
                              :model-value="pr"
                              clickable @click="addDoc(pr)">
                            <template v-slot:side>
                              <q-item-section side v-if="(form?.practitioners || {})[pr]">
                                <q-icon color="green" name="mdi-check"></q-icon>
                              </q-item-section>
                            </template>
                          </practitioner-item>
                        </q-list>
                      </div>
                    </q-menu>
                  </q-chip>
                </div>
              </div>
            </template>
            <template v-if="form.provider">
              <div class="_form_label">Visit Date</div>
              <div class="q-pa-sm mw400">
                <inline-date :input-attrs="{ dense: true, filled: true }" @update:model-value="maybeSave" v-model="form.date"></inline-date>
              </div>
            </template>

            <template v-if="form?._id">
              <div class="_form_label">Records</div>
              <div class="q-pa-sm">
                <file-object-form @update:model-value="autoSave('records')" v-model="form.records"></file-object-form>
              </div>
            </template>
          </div>

          <div class="_f_l _f_chip">Bills</div>
          <claims-list adding :visit="form"></claims-list>

        </div>
      </div>
      <div class="col-12 q-pa-sm" v-if="form?._id">
        <div class="_f_l _f_chip">Conditions</div>
        <conditions-manager
            :model-value="form.conditions"
            @update:one="addCondition($event, form)"
            @remove:one="removeCondition($event, form)"
        ></conditions-manager>
        <div class="_f_l _f_chip">Records</div>
        <div class="q-pa-sm">Upload bills here and we'll find you savings on
          <ul>
            <li>Billing Errors</li>
            <li>Overpriced Services</li>
            <li>Cash Network Services</li>
          </ul>
        </div>
        <div class="q-pa-sm">
          <file-object-form v-model="form.files" @update:model-value="autoSave('bills')"></file-object-form>
        </div>

        <template v-if="form._id">
        <div class="_f_l _f_chip">Delete Visit</div>
        <div class="q-pa-sm">
          <remove-proxy-btn name="Visit" label="Delete" v-if="canRemove"
          @remove="removeVisit"></remove-proxy-btn>
          <div v-else class="q-pa-sm text-italic font-7-8r">Cannot delete visit with claims associated</div>
        </div>
        </template>

      </div>

    </div>


  </div>
</template>

<script setup>
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';
  import PractitionerChip from 'components/practitioners/cards/PractitionerChip.vue';
  import PractitionerItem from 'components/practitioners/cards/PractitionerItem.vue';
  import ClaimsList from 'components/claims/lists/ClaimsList.vue';
  import AddProviderItem from 'components/providers/cards/AddProviderItem.vue';
  import FileObjectForm from 'components/common/uploads/utils/FileObjectForm.vue';
  import AddPractitionerItem from 'components/practitioners/cards/AddPractitionerItem.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import ConditionsManager from 'components/care/conditions/forms/ConditionsManager.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, nextTick, watch} from 'vue';
  import {HForm, HSave} from 'src/utils/hForm';
  import {useVisits} from 'stores/visits';
  import {idGet} from 'src/utils/id-get';
  import {useCares} from 'stores/cares';
  import {usePpls} from 'stores/ppls';
  import {useHouseholds} from 'stores/households';
  import {$errNotify} from 'src/utils/global-methods';
  import {visitConditions} from 'components/care/visits/utils';

  const visitStore = useVisits();
  const { addCondition, removeCondition } = visitConditions(visitStore);
  const careStore = useCares();
  const pplStore = usePpls();
  const hhStore = useHouseholds();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    care: { required: false },
    modelValue: { required: false }
  })

  const  {item:visit} = idGet({
    store: visitStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })
  const { item:fullCare } = idGet({
    store: careStore,
    value: computed(() => props.care || visit.value?.care)
  ,
    useAtcStore
  })
  const { item:person } = idGet({
    store: pplStore,
    value: computed(() => fullCare.value?.person)
  ,
    useAtcStore
  })
  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => person.value?.household)
  ,
    useAtcStore
  })
  const formFn = (defs) => {
    return {
      status: 'appointment',
      ...defs
    }
  }
  const { form, save } = HForm({
    store: visitStore,
    validate: true,
    value: visit,
    formFn,
    vOpts: computed(() => {
      return {
        'provider': { name: 'provider', v: ['notEmpty'] }
      }
    }),
    log: false,
    beforeFn: (val) => {
      if (!val.care) val.care = fullCare.value._id
      return val;
    },
    afterFn: (val) => {
      emit('update:model-value', val)
    }
  })

  const addPractitioner = (val) => {
    form.value.practitioners = [...(form.value.practitioners || []), val._id]
    if(form.value._id) visitStore.patch(form.value._id, { $addToSet: { practitioners: val._id } })
  }

  const maybeSave = () => {
    nextTick(() => {
      if (!form.value?._id) save();
      else autoSave('date')
    })
  }

  const pause = computed(() => !form.value?._id);
  const { autoSave, setForm } = HSave({ form, pause, store:visitStore })

  const removeDoc = (id) => {
    delete form.value.practitioners[id];
    autoSave('practitioners');
  }

  const addDoc = (id) => {
    if (!(form.value.practitioners || { [id]: false })[id]) {
      form.value.practitioners = { ...form.value.practitioners || {}, [id]: { id, notes: '' } }
    }
  }

  watch(fullCare, (nv) => {
    if (nv) {
      nextTick(() => {
        if (!form.value?.provider) {
          const def = { provider: nv.providers[0] };
          // if(nv.practitioners) {
          //   def.practitioners = {}
          //   for(const p of nv.practitioners){
          //     def.practitioners[p] = { id: p }
          //   }
          // }
          form.value = formFn(def)
        }
        form.value.care = nv._id
      })
    }
  }, { immediate: true })

  const canRemove = computed(() => {
    const { _id, claims, claimReqs } = form.value || {}
    return _id && !claims?.length && !claimReqs?.length;
  })

  const removeVisit = () => {
    visitStore.remove(form.value._id)
        .catch(err => $errNotify(`Error deleting visit: ${err.message}`))
  }
</script>

<style lang="scss" scoped>

</style>
