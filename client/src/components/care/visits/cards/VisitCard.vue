<template>
  <div class="_fw" @click="emit('update:model-value', visit)">

    <div class="row items-center q-pb-sm">
      <slot name="top-left"></slot>
      <q-chip color="transparent">
        <q-avatar size="14px" :color="visitType.color || 'grey-5'">
          <q-tooltip>{{ visitType.label || 'No Status' }}</q-tooltip>
        </q-avatar>
        <span
            class="q-ml-sm font-7-8r">{{
            formatDate(visit?.date, 'ddd MMM DD, YYYY')
          }}{{ visit?.endDate ? `- ${formatDate(visit?.endDate, 'ddd MMM dd, YYYY')}` : '' }}</span>
      </q-chip>
      <slot name="top-right"></slot>
    </div>
    <div class="row items-center" v-if="!noProviderChip">
      <provider-chip :chip-attrs="{ color: 'transparent' }" :model-value="visit.provider"></provider-chip>
    </div>
    <claims-table :visit="visit"></claims-table>
  </div>
</template>

<script setup>
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import ClaimsTable from 'components/claims/lists/ClaimsTable.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {types} from 'components/care/visits/utils';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useVisits} from 'stores/visits';
  import {formatDate} from 'src/utils/date-utils';

  const store = useVisits();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    noProviderChip: Boolean
  })

  const { item: visit } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const visitType = computed(() => {
    if (!visit.value?.status) return { label: 'No Status', color: 'grey' }
    else return types[visit.value.status]
  })
</script>

<style lang="scss" scoped>

</style>
