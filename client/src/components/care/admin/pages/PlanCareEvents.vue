<template>
  <div class="_fw">

    <q-tab-panels class="_panel" :model-value="!active" animated>
      <q-tab-panel class="_panel" :name="true">

        <div class="_fw row items-center">

          <q-chip color="grey-2" clickable>
        <span
            class="q-mr-sm">{{
            unassigned === -1 ? 'Select Status' : unassigned === 0 ? 'Has Provider' : 'Needs Provider'
          }}</span>
            <q-icon name="mdi-menu-down"></q-icon>
            <q-menu>
              <div class="w300 mw100 bg-white">
                <q-list separator>
                  <q-item clickable @click="unassigned = -1">
                    <q-item-section>
                      <q-item-label>Show All</q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-item clickable @click="unassigned = 0">
                    <q-item-section>
                      <q-item-label>Assigned Provider</q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-item clickable @click="unassigned = 1">
                    <q-item-section>
                      <q-item-label>Needs Provider</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-menu>
          </q-chip>
          <priority-chip cli color="grey-2" empty-label="Select Priority" v-model="priorityFilter" picker>
            <template v-slot:right>
              <q-icon name="mdi-menu-down" class="q-ml-sm"></q-icon>
            </template>
          </priority-chip>
        </div>
        <div class="row q-pt-md">
          <div v-for="(care, i) in c$.data" :key="`c-${i}`" class="col-12 col-md-4 q-pa-sm">
            <div class="__c" @click="$router.push({ ...$route, params: {careId: care._id} })">
              <cares-card :model-value="care"></cares-card>
            </div>
          </div>
        </div>

        <div class="row justify-end q-py-md">
          <q-pagination
              @update:model-value="c$.toPage($event)"
              :model-value="pagination.currentPage"
              :min="1"
              :max="pagination.pageCount"
              direction-links
              boundary-numbers
          ></q-pagination>
        </div>

      </q-tab-panel>
      <q-tab-panel class="_panel" :name="false">

        <care-detail :model-value="active"></care-detail>

      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import CaresCard from 'components/care/cards/CaresCard.vue';
  import PriorityChip from 'components/care/cards/PriorityChip.vue';
  import CareDetail from 'components/care/pages/CareDetail.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {computed, onMounted, ref, watch} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useCares} from 'stores/cares';
  import {useRoute} from 'vue-router';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const envStore = useEnvStore();
  const { getPlanId } = contextItems(envStore);

  const planStore = usePlans();
  const careStore = useCares();
  const route = useRoute();

  const { item: plan } = idGet({
    store: planStore,
    value: getPlanId
  ,
    useAtcStore
  })

  const { item: active } = idGet({
    store: careStore,
    routeParamsPath: 'careId'
  ,
    useAtcStore
  })

  const unassigned = ref(-1);
  const priorityFilter = ref(undefined)
  const params = computed(() => {
    const query = { patient: { $exists: true }, plan: plan.value?._id };
    if (unassigned.value > -1) query['visits.0'] = { $exists: !!unassigned.value }
    if (priorityFilter.value || priorityFilter.value === 0) query.patientPriority = priorityFilter.value
    return { query }
  })

  const { h$: c$, pagination } = HFind({
    store: careStore,
    limit: ref(10),
    params
  })

  watch(params, (nv) => {
    if (nv) {
      const toQueryString = (queryObject) => {
        const urlParams = new URLSearchParams();
        for (const key in queryObject) {
          if (queryObject.hasOwnProperty(key)) {
            const value = queryObject[key];
            if (Array.isArray(value)) {
              // If the value is an array, add each item individually
              value.forEach(item => urlParams.append(key, item));
            } else if (value !== undefined && value !== null) {
              // Only add if the value is not undefined or null
              urlParams.append(key, value);
            }
          }
        }
        return urlParams.toString();
      }
      const str = toQueryString(nv.query || {});

      const url = new URL(window.location); // Create a URL object from the current URL
      url.searchParams.set('careQuery', str);   // Update or add the query parameter
      window.history.replaceState(null, '', url); // Update the browser's URL without reloading the page
    }
  }, { immediate: true })

  const resetQuery = async (query) => {
    const obj = query
    const arr = Object.keys(obj || {});
    for (let i = 0; i < arr.length; i++) {
      const key = arr[i]
      if (key === 'visits.0' && JSON.parse(obj[key])?.$exists === false) unassigned.value = 1;
      if (key === 'patientPriority') {
        const parsed = JSON.parse(obj[key]);
        priorityFilter.value = Array.isArray(parsed) ? parsed[0] : parsed.$in ? parsed.$in[0] : parsed;
      }
    }
  }

  onMounted(() => {
    resetQuery(route.query.careQuery);
  })
</script>

<style lang="scss" scoped>
  .__c {
    padding: 20px 15px;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, .1);
    background: white;
    cursor: pointer;
    transition: all .2s;

    &:hover {
      box-shadow: 0 3px 8px rgba(0, 0, 0, .15);
    }
  }
</style>
