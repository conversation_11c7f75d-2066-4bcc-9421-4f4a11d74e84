<template>
  <div class="_fw">
    <template v-if="canEdit.ok">

      <div class="row">

        <div class="col-12 col-lg-6 q-pa-sm">
          <div class="__c">
            <div class="__title">
              Issue/Illness
            </div>
            <div class="q-pa-sm">
              <!--            CONDITION-->
              <conditions-manager
                  :removable="!providerView"
                  v-model="form.conditions"
                  @update:model-value="autoSave('conditions')"
              ></conditions-manager>
            </div>
          </div>

          <div class="__c">
            <div class="__title">Visits/Appointments (and bills)</div>

            <visit-manager :care="form"></visit-manager>
          </div>

        </div>
        <div class="col-12 col-lg-6 q-pa-sm">
          <div class="__c">
            <div class="__title">
              Care Details
            </div>
            <div class="q-py-sm">
              <div class="_form_grid">
                <div class="_form_label">Priority</div>
                <div class="q-py-sm q-px-md">
                  <div class="_fw mw500">
                    <priority-picker
                        :disabled="providerView"
                        v-model="form.patientPriority"
                        type="patient"
                        @update:model-value="autoSave('patientPriority')"
                    ></priority-picker>
                  </div>
                </div>

                <!--              &lt;!&ndash;            STATUS&ndash;&gt;-->
                <!--              <div class="_form_label">Status</div>-->
                <!--              <div class="q-pa-sm">-->
                <!--                <status-chip :model-value="care?.status"></status-chip>-->
                <!--              </div>-->

                <!--            INIT DATE-->
                <div class="_form_label">Date Issue Began</div>
                <div class="q-pa-sm">
                  <date-chip :model-value="form.initDate">
                    <template v-slot:menu>
                      <q-popup-proxy v-if="person?._id === care?.person">
                        <q-date :model-value="form.initDate"
                                @update:model-value="setForm('initDate', $event)"></q-date>
                      </q-popup-proxy>
                    </template>
                  </date-chip>
                </div>
                <!--            TARGET DATE-->
                <div class="_form_label">Target Treatment Date</div>
                <div class="q-pa-sm">
                  <date-chip :model-value="form.targetDate">
                    <template v-slot:menu>
                      <q-popup-proxy v-if="person?._id === care?.person">
                        <q-date :model-value="form.targetDate"
                                @update:model-value="setForm('targetDate', $event)"></q-date>
                      </q-popup-proxy>
                    </template>
                  </date-chip>
                </div>
                <div class="_form_label">Medical Documents (Not Bills)</div>
                <div class="q-pa-sm">
                  <file-object-form
                      v-model="form.files"
                      @update:model-value="autoSave('files')"
                  ></file-object-form>
                </div>
              </div>
            </div>
          </div>
          <div class="__c">
            <div class="__title">Communication</div>
            <thread-form :parent="{ id: care._id, service: 'cares' }"></thread-form>
            <show-threads empty-message="No Conversations" :threads="care.threads"></show-threads>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <loading-status></loading-status>
    </template>
  </div>
</template>

<script setup>
  // import StatusChip from 'components/care/cards/StatusChip.vue';
  import ConditionsManager from 'components/care/conditions/forms/ConditionsManager.vue';
  import DateChip from 'components/common/dates/DateChip.vue';
  import FileObjectForm from 'components/common/uploads/utils/FileObjectForm.vue';
  import VisitManager from 'components/care/visits/cards/VisitManager.vue';
  import PriorityPicker from 'components/care/forms/PriorityPicker.vue';
  import LoadingStatus from 'components/utils/LoadingStatus.vue';
  import ShowThreads from 'components/threads/cards/ShowThreads.vue';
  import ThreadForm from 'components/threads/forms/ThreadForm.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {loginPerson} from 'stores/utils/login';
  import {computed} from 'vue';
  import {useCares} from 'stores/cares';
  import {idGet} from 'src/utils/id-get';
  import {useRoute} from 'vue-router';

  import {HForm, HSave} from 'src/utils/hForm';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {usePlans} from 'stores/plans';

  const { person, login } = loginPerson()

  const route = useRoute();
  const careStore = useCares();
  const planStore = usePlans();

  const props = defineProps({
    modelValue: { required: false },
    providerView: Boolean
  })

  const { item: care } = idGet({
    value: computed(() => props.modelValue || route.params.careId),
    store: careStore
  ,
    useAtcStore
  })
  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => care.value?.plan),

    useAtcStore
  })

  const { form } = HForm({
    store: careStore,
    value: care
  })

  const { autoSave, setForm } = HSave({ form, store: careStore })

  const caps = computed(() => {
    const arr = [[`plans:${plan.value?._id}`, ['planAdmin']], [`orgs:${plan.value?.org}`, ['WRITE']], ['orgs', 'WRITE']]
    const p = care.value?.providers || [];
    for (let i = 0; i < p.length; i++) {
      arr.push([`providers:${p[i]}`, ['providerAdmin']])
      arr.push([`providers:${p[i]}`, ['WRITE']])
    }
    return arr;
  })

  const { canEdit } = clientCanU({
    subject: care,
    or: true,
    caps,
    login,
    cap_subjects: computed(() => plan.value._id),
    loginPass: [[['patient/owner', 'person/owner'], '*']]
  })


</script>

<style lang="scss" scoped>
  .__h {
    min-height: 95vh;
  }

  .__c {
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 12px -4px #999;
    padding: 50px 20px 20px 20px;
    position: relative;
    margin-top: 30px;
  }

  .__title {
    position: absolute;
    top: 0;
    left: 5%;
    transform: translate(0, -30%);
    border-radius: 6px;
    background: linear-gradient(170deg, var(--q-p7), var(--q-primary));
    color: white;
    font-weight: 600;
    padding: 6px 8px;
    font-size: 1rem;
  }
</style>
