<template>
  <q-page class="_fw _bg_ow">
    <plan-select-top></plan-select-top>

    <div class="row justify-center _bg_ow" v-if="care?._id">
      <div class="_cent pd4 pw2 mnh80 __h">

        <care-view :model-value="care"></care-view>

      </div>
    </div>
    <div v-else class="_fw row justify-center">
      <div class="_cent pd4 pw2 mnh80 __h">
        <router-view></router-view>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import PlanSelectTop from 'components/plans/utils/PlanSelectTop.vue';
  import CareView from 'components/care/pages/CareView.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {useCares} from 'stores/cares';
  import {idGet} from 'src/utils/id-get';
  import {useRoute} from 'vue-router';
  import {useVisits} from 'stores/visits';

  const route = useRoute();
  const careStore = useCares();
  const visitStore = useVisits();

  const { item: visit } = idGet({
    value: computed(() => route.params.visitId),
    store: visitStore,
    params: ref({ runJoin: { visit_claims: true }})
  })
  const { item: care } = idGet({
    value: computed(() => visit.value?.care || route.params.careId),
    store: careStore
  ,
    useAtcStore
  })


</script>

<style lang="scss" scoped>
  .__h {
    min-height: 95vh;
  }

  .__c {
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 12px -4px #999;
    padding: 50px 20px 20px 20px;
    position: relative;
    margin-top: 30px;
  }

  .__title {
    position: absolute;
    top: 0;
    left: 5%;
    transform: translate(0, -30%);
    border-radius: 6px;
    background: linear-gradient(170deg, var(--q-p7), var(--q-primary));
    color: white;
    font-weight: 600;
    padding: 6px 8px;
    font-size: 1rem;
  }
</style>
