<template>
  <q-item
      v-bind="{
    clickable: true,
    class: '_fw mw600',
    ...$attrs
      }"
       @click="$emit('update:model-value', procedure)">
    <q-item-section v-if="procedure">
      <q-item-label class="font-7-8r">{{ $limitStr((procedure.names || [])[0] || procedure.layName || procedure.name, limit, '...') }}
        <q-tooltip>
          <procedure-description :model-value="procedure"></procedure-description>
        </q-tooltip>
      </q-item-label>
      <q-item-label caption>
        {{$limitStr((procedure.descriptions || [])[0] || procedure.layDescription, limit, '...')}}
      </q-item-label>
    </q-item-section>
    <slot name="side"></slot>
  </q-item>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {useProcedures} from 'stores/procedures';
  import {computed} from 'vue';
  import {$limitStr} from 'src/utils/global-methods';
  import ProcedureDescription from 'components/care/procedures/cards/ProcedureDescription.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useProcedures();

  const props = defineProps({
    modelValue: { required: true },
    limit: {default: 100}
  })

  const { item:procedure } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const parent = computed(() => {
    const spl = procedure.value?.parent?.split(';') || [];
    return spl[spl.length-2]
  })
</script>

<style lang="scss" scoped>

</style>
