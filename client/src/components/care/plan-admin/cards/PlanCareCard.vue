<template>
  <div class="_fw">

    <q-table
        flat
        :rows="c$.data"
        :columns="cols"
    >
      <template v-slot:no-data>
        <div class="q-pa-sm text-italic">No care events</div>
      </template>
      <template v-slot:header="scope">
        <!--        <q-th auto-width></q-th>-->
        <q-th
            v-for="col in scope.cols"
            :key="col.name"
            :props="scope"
        >
          {{ col.label }}
        </q-th>
      </template>
      <template v-slot:body="scope">
        <q-tr :props="scope">
          <template v-if="!loading">
          <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
            <component
                v-if="col.component"
                :is="col.component"
                v-bind="col.attrs(scope.row)"
            ></component>
            <div v-else>{{ col.value }}</div>
          </q-td>
          </template>
          <q-td v-else class="q-pa-md">
            <q-spinner color="grey-3" size="20px"></q-spinner>
          </q-td>
        </q-tr>
      </template>
      <template v-slot:bottom>
        <div class="row items-center _fw">
          <div class="font-7-8r text-grey-8">{{ c$.data?.length ? 1 : 0 }} - {{ c$.data?.length || 0 }} of
            {{ c$.total }}
          </div>
          <q-space></q-space>
          <q-pagination
              @update:model-value="c$.toPage($event)"
              :model-value="pagination.currentPage"
              :min="1"
              :max="pagination.pageCount"
              direction-links
              boundary-numbers
          ></q-pagination>
        </div>
      </template>
    </q-table>
  </div>
</template>

<script setup>
  import ObscureChip from 'components/households/cards/ObscureChip.vue';
  import AvatarRow from 'components/common/avatars/AvatarRow.vue';
  import StatusChip from 'components/care/cards/StatusChip.vue';
  import PriorityEmoji from 'components/care/cards/PriorityEmoji.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';

  import {usePlans} from 'stores/plans';
  const planStore = usePlans();

  import {useCares} from 'stores/cares';
  const careStore = useCares();

  import {useProviders} from 'stores/providers';
  import {_flatten} from 'symbol-syntax-utils';
  import {formatDate} from 'src/utils/date-utils';
  import {$possiblyPlural} from 'src/utils/global-methods';
  const providerStore = useProviders()

  const props = defineProps({
    query: Object,
    plan: { required: true }
  })

  const { item:fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  ,
    useAtcStore
  })

  const { h$:c$, pagination } = HFind({
    store: careStore,
    limit: ref(10),
    pause: computed(() => !fullPlan.value),
    params: computed(() => {
      return {
        query: {
          ...props.query,
          $sort: { targetDate: -1 },
          plan: fullPlan.value?._id
        }
      }
    })
  })

  const { h$:pv$ } = HFind({
    store: providerStore,
    pause: computed(() => !c$.data?.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: _flatten((c$.data || []).map(a => a.providers))}
        }
      }
    })
  })

  const cols = computed(() => {
    return [
      {
        name: 'Participant',
        component: ObscureChip,
        attrs: (row) => {
          return {
            modelValue: row.patient
          }
        }
      },
      {
        name: 'Providers',
        component: AvatarRow,
        attrs: (row) => {
          return {
            size: 20,
            limit: 5,
            modelValue: row.providers,
            avatarStore: providerStore
          }
        }
      },
      {
        name: 'Status',
        component: StatusChip,
        attrs: (row) => {
          return {
            label: '',
            size: 'sm',
            modelValue: row.status
          }
        }
      },
      {
        name: 'Priority',
        component: PriorityEmoji,
        attrs: (row) => {
          return {
            modelValue: row.patientPriority
          }
        }
      },
      {
        name: 'Visits',
        component: TdText,
        attrs: (row) => {
          return {
            col: {
              value: $possiblyPlural('Visit', row.visits || [])
            }
          }
        }
      },
      {
        name: 'Target',
        component: TdText,
        attrs: (row) => {
          return {
            col: {
              value: formatDate(row.targetDate, 'MM-DD-YYYY')
            }
          }
        }
      }
    ].map(a => {
      return {
        ...a,
        label: a.name,
        sortable: true,
        align: 'left',
        field: a.name
      };
    });
  })

  const loading = ref(true);
  const unload = (tries = 0) => {
    if(pv$.total) loading.value = false;
    if(tries < 5){
      setTimeout(() => {
        unload(tries+1)
      }, 200)
    } else loading.value = false
  }
  onMounted(() => {
    unload()
  })
</script>

<style lang="scss" scoped>

</style>
