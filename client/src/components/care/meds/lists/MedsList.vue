<template>
  <slot name="input" v-bind="{ search, setSearch:searchIt, setFocus, setBlur, loading }">
    <q-input v-model="search.text" @update:model-value="searchIt" @focus="setFocus" @blur="setBlur"
             v-bind="{ ...$attrs }">
      <template v-slot:prepend>
        <slot name="prepend">
          <q-icon name="mdi-pill"></q-icon>
        </slot>
      </template>
      <template v-slot:append v-if="loading || $slots.append">
        <slot name="append">
          <q-spinner v-if="loading" color="primary"></q-spinner>
        </slot>
      </template>
    </q-input>
  </slot>

  <div :class="`__list ${focus || (med._id && ndc) ? '' : '__off'}`">
    <q-list separator v-if="!modelValue || multiple">
      <q-item dense v-if="loading">
        <q-item-section avatar>
          <q-spinner></q-spinner>
        </q-item-section>
      </q-item>
      <med-item @click="select(m)" :model-value="m" v-for="(m, i) in options" :key="`m-${i}`"></med-item>
    </q-list>
    <div class="_fw" v-else>
      <med-item :model-value="med"></med-item>
      <q-item-label header class="font-7-8r tw-six text-ir-mid">Select Drug Option</q-item-label>
      <q-item v-if="loading">
        <q-item-section avatar>
          <q-spinner color="primary"></q-spinner>
        </q-item-section>
        <q-item-section>
          <q-item-label>Loading drugs...</q-item-label>
        </q-item-section>
      </q-item>
      <div v-else-if="!h$.total" class="q-pa-sm">
        No drug options for this drug
      </div>
      <q-list separator>
        <q-item v-for="(s, idx) in h$.data" :key="`s-${idx}`" clickable @click="emit('ndc', s)">
          <q-item-section>
            <q-item-label>{{ s.synonym || s.name }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>
  </div>

</template>

<script setup>
  import MedItem from 'components/care/meds/cards/MedItem.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref, watch} from 'vue';
  import {careSearch} from 'components/care/utils/care-search';
  import {idGet} from 'src/utils/id-get';
  import {useMeds} from 'stores/meds';
  import {HFind} from 'src/utils/hFind';

  const medStore = useMeds();

  const emit = defineEmits(['update:model-value', 'focus', 'blur', 'ndc']);
  const props = defineProps({
    modelValue: { required: false },
    searchDefault: { required: false },
    multiple: Boolean,
    params: Object,
    ndc: Boolean,
    emitValue: Boolean
  })

  const setFocus = () => {
    focus.value = true;
    emit('focus')
  }
  const setBlur = () => {
    focus.value = false;
    emit('blur')
  }

  const setMed = ref()
  const { item: med } = idGet({
    store: medStore,
    value: computed(() => {
      if(setMed.value) return setMed.value
      return Array.isArray(props.modelValue) ? {} : props.modelValue
    })
  })

  const {
    loading,
    focus,
    options,
    setSearch,
    search
  } = careSearch({
    service: 'meds',
    value: med,
    searchDef: computed(() => props.searchDefault),
    multiple: props.multiple,
    params: computed(() => {
      return {
        query: {
          sbdOf: { $exists: false },
          ...props.params?.query
        },
        ...props.params
      }
    })
  })

  const searchIt = (val) => {
    emit('input-value', val);
    setSearch(val);
    setMed.value = undefined;
  }

  watch(med, async (nv, ov) => {
    if (props.ndc && nv && nv._id !== ov?._id) {
      loading.value = true
      try {
        await medStore.get(nv._id, { runJoin: { ndc_lookup: true } })
      } catch (e) {
        console.log('Error with ndc lookup in med list', e.message)
      } finally {
        setTimeout(() => {
          loading.value = false
        }, 250)
      }
    }
  }, { immediate: true })

  const { h$ } = HFind({
    store: medStore,
    pause: computed(() => loading.value || !props.ndc || !med.value._id),
    limit: ref(10),
    params: computed(() => {
      return {
        query: {
          rxcui: { $in: [med.value.rxcui, ...Object.keys(med.value.info?.SBD || {})] },
          'ndcs.0': { $exists: true }
        }
      }
    })
  })

  const select = (val) => {
    if(props.ndc) setMed.value = val;
    if (props.multiple) {
      const list = [...props.modelValue || []]
      if (props.emitValue) {
        const idx = list.indexOf(val._id);
        if (idx > -1) list.splice(idx, 1);
        else list.push(val._id);
      } else {
        const idx = list.map(a => a._id).indexOf(val._id);
        if (idx > -1) list.splice(idx, 1);
        else list.push(val);
      }
      emit('update:model-value', list)
    } else if (props.emitValue) emit('update:model-value', val._id);
    else emit('update:model-value', val);
  }

</script>

<style lang="scss" scoped>
  .__list {
    max-height: 100%;
    padding: 10px;
    transition: all .3s;
    overflow-y: scroll;
    box-shadow: 0 3px 5px var(--ir-light);
    border-radius: 0 0 10px 10px;
  }

  .__off {
    max-height: 0;
    padding: 0;
    overflow: hidden;
  }
</style>
