
<template>
  <q-item
      v-bind="{
    clickable: true,
    ...$attrs
      }"
      @click="$emit('update:model-value', med)">
    <q-item-section>
      <q-item-label class="font-3-4r text-uppercase text-p6">{{ ingredients.join(' | ') }}</q-item-label>
      <q-item-label>{{ med?.name }}</q-item-label>
      <q-item-label caption >
        <span>{{$possiblyPlural('Drug Option', Object.keys(med.info?.SBD || {}))}}</span>
      </q-item-label>
    </q-item-section>
    <slot name="side"></slot>
  </q-item>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {useMeds} from 'stores/meds';
  import {computed} from 'vue';
  import {$possiblyPlural} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useMeds();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item:med } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const generics = computed(() => {
    return Object.keys( med.value?.info?.GPCK || {})
  })
  const ingredients = computed(() => {
    const arr = [];
    const IN = med.value?.info?.IN || {};
    for(const k in IN) {
      if(IN[k].name) arr.push(IN[k].name);
    }
    return arr;
  })
</script>

<style lang="scss" scoped>

</style>
