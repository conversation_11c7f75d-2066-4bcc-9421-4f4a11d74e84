<template>
  <div class="_fw">


    <template v-if="!session._fastjoin?.sheetLength">
      <div class="q-py-sm _fw">
        <div class="text-center _fw font-1-1-2r tw-six text-ir-deep alt-font">Enter/Upload Bulk Drug Codes</div>
      </div>
      <div class="row q-py-md justify-end">

        <q-chip size="sm" clickable @click="uploadDialog = true" color="transparent">
          <q-icon name="mdi-upload" color="primary" class="q-mr-sm text-sm"></q-icon>
          <span class="tw-six text-xxs">Upload Drug List</span>
        </q-chip>

      </div>
      <div class="__tw">
        <div class="__tbl">

          <div class="__loader" v-if="loading">
            <div class="_fa flex flex-center">
              <ai-logo opaque></ai-logo>
            </div>
          </div>

          <div class="_fw">
            <slot name="top"></slot>

            <div class="row items-center">
              <div class="text-xxs tw-six text-ir-deep">NDC Version</div>
              <q-radio v-model="ndcVersion" :val="11" label="11"></q-radio>
              <q-radio v-model="ndcVersion" :val="10" label="10"></q-radio>
            </div>
            <div :class="`__errs ${Object.keys(errs).length ? '__on' : ''}`">
              <div class="text-red tw-six text-xxs">Address data errors to see results</div>
            </div>

            <q-btn v-if="sArray.length" flat size="sm" no-caps class="tw-six" @click="remove">
              <q-icon class="q-mr-sm" name="mdi-delete" color="red"></q-icon>

              <span>Remove {{ $possiblyPlural('Row', sArray) }}</span>
            </q-btn>
          </div>
          <div class="__table">
            <table class="__inp alt-font">
              <thead>
              <tr>
                <th style="text-align: right" class="cursor-pointer text-right">
                  <q-icon size="20px" :color="allOn ? 'primary' : 'ir-grey-6'"
                          :name="`mdi-checkbox-${allOn ? 'marked' : 'blank-outline'}`"
                          @click="selectAll(!allOn)"></q-icon>
                </th>
                <th v-for="(req, i) in reqs" :key="`req-${i}`">
                  {{ req.label }}<span class="text-secondary" v-if="req.required">&#42;</span>
                  <q-tooltip>
                    <div class="text-center mw300 text-xxs tw-five">{{ req.tooltip }}</div>
                  </q-tooltip>
                </th>
              </tr>
              </thead>
              <tbody>
              <tr>
                <td class="bg-ir-grey-3">Ex:</td>
                <td v-for="(req, i) in reqs" :key="`ex-${i}`">
                  {{ req.format(req.ex, 0, req.key) }}
                </td>
              </tr>
              <!--          first row-->
              <tr>
                <td class="cursor-pointer" @click.stop="selected[0] = !selected['0']">
                  <span v-if="!selected['0']">1</span>
                  <q-icon size="15px" v-else color="primary" name="mdi-checkbox-marked"></q-icon>
                </td>
                <td v-for="(req, idx) in reqs" :key="`ex-0-${idx}`" @click="setFocus([0, idx])" :id="`col-0-${idx}`"
                    :class="errs[`0-${idx}`] ? '__err' : ''">
                  <q-tooltip v-if="errs[`0-${idx}`]">
                    <div class="w300 tw-six text-xs">Err: {{ errs[`0-${idx}`] }}</div>
                  </q-tooltip>
                  <input
                      class="text-right"
                      :id="`inp-0-${idx}`"
                      @paste="handlePaste(0, idx, $event)"
                      @keydown="keyDown($event)"
                      :value="req.format((csvData[0] || [])[idx], 0, req.key)"
                      @input="setData(0, idx, $event.target.value)"
                      @blur="checkData(0, idx)"
                  >
                </td>

              </tr>
              <!--          ROWS 2 - end-->
              <tr v-for="(row, i) in csvData.slice(1)" :key="`row-${i + 1}`" :id="`row-${i + 1}`">
                <td class="cursor-pointer" @click.stop="selected[i+1] = !selected[i+1]">
                  <span v-if="!selected[i+1]">{{ i + 2 }}</span>
                  <q-icon size="15px" v-else color="primary" name="mdi-checkbox-marked"></q-icon>
                </td>
                <td v-for="(req, idx) in reqs" :key="`col-${i + 1}-${idx}`" :id="`col-${i+1}-${idx}`"
                    :class="errs[`${i+1}-${idx}`] ? '__err' : ''">
                  <q-tooltip v-if="errs[`${i+1}-${idx}`]">
                    <div class="mw300 tw-six">Err: {{ errs[`${i + 1}-${idx}`] }}</div>
                  </q-tooltip>

                  <input
                      class="text-right"
                      :id="`inp-${i+1}-${idx}`"
                      @paste="handlePaste(i+1, idx, $event)"
                      @keydown="keyDown($event)"
                      :value="req.format((csvData[i+1] || [])[idx], i+1, req.key)"
                      @input="setData(i+1, idx, $event.target.value)"
                      @blur="checkData(i+1, idx)"
                  >
                </td>
              </tr>
              </tbody>
            </table>
          </div>

          <div class="q-py-sm font-7-8r text-italic row items-center">
            <div>
              Row 1 - {{ csvData.length }} of {{ session._fastjoin?.sheetLength || csvData.length }}
            </div>
            <q-space/>
            <div>
              <span class="text-secondary font-1-1-2r">&#42;</span> Required for a price - otherwise data is optional
            </div>
          </div>
        </div>
      </div>
      <div v-if="csvData.length" class="row justify-end q-pt-lg q-px-md">
        <q-btn class="_pl_btn tw-six text-xs" no-caps push @click="save"
               :disable="loading || !!Object.keys(errs || {}).length">
          <span class="q-mr-sm">{{ 'Get Pricing' }}</span>
          <q-spinner color="white" v-if="loading"></q-spinner>
          <q-icon v-else name="mdi-chevron-right" color="white"></q-icon>
        </q-btn>
      </div>
    </template>
    <template v-else>

      <div class="q-py-sm _fw">
        <div class="text-center _fw font-1-1-2r tw-six text-ir-deep alt-font">View/Download Results</div>
        <div class="row justify-center" v-if="requiredDownload">
          <div class="_fw mw800">
            <div class="text-center q-py-sm _fw font-1r text-ir-deep tw-five">
              Showing only the first 100 rows - you can <span class="tw-six text-accent _hov cursor-pointer"
                                                              @click="downloadFile">download</span> the full file
            </div>
            <div class="_fw text-center font-7-8r">Deep search is limited to 100 rows. If you have some empty rows, try
              a smaller data set.
            </div>
          </div>
        </div>

      </div>
      <div class="row items-center q-py-md">
        <remove-proxy-btn color="ir-text" no-caps :icon="undefined" flat @remove="reset"
                      remove-label="Clear data and start over?">
          <span class="tw-six text-xxs">Start Over</span>
          <q-icon class="q-ml-sm" color="secondary" name="mdi-refresh"></q-icon>
        </remove-proxy-btn>
        <q-space/>
        <q-chip size="sm" clickable @click="downloadFile" color="transparent">
          <q-icon name="mdi-download" color="primary" class="q-mr-sm text-sm"></q-icon>
          <span class="tw-six text-xxs">Download Pricing</span>
        </q-chip>
      </div>

      <div class="__tw">
        <div class="__tbl">
          <div :class="`__slider ${popup > -1 ? '' : '__off'}`">
            <div class="row justify-end">
              <q-btn dense flat size="sm" color="red" icon="mdi-close" @click="popup = -1"></q-btn>
            </div>
            <div>
              <q-list separator dark>
                <q-item-label header>Results</q-item-label>
                <q-item-label header class="tw-six font-7-8r text-white" v-if="!p$.total">None</q-item-label>
                <q-item v-for="(p, i) in p$.data" :key="`p-${i}`">
                  <q-item-section>
                    <q-item-label class="text-a2 font-3-4r tw-six" v-if="p.s_f">{{ p.s_f }}</q-item-label>
                    <q-item-label class="font-7-8r tw-six text-white">{{ p.name }}</q-item-label>
                    <q-item-label class="font-3-4r text-ir-light">{{ p.description }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <span class="tw-six font-1r text-primary">{{ dollarString($round(p.price) / 100, '$', 2) }}</span>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
          <div class="__table">

            <table class="__inp __res">
              <thead>
              <tr>
                <th></th>
                <th v-for="(r, i) in resultReqs" :key="`res-h-${i}`">
                  {{ r.label }}
                </th>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(row, i) in csvData" :key="`res-row-${i}`" class="alt-font" @click="popup = i"
                  :class="popup === i ? '__active' : ''">
                <td>{{ i + 1 }}</td>
                <td v-for="(req, idx) in resultReqs" :key="`res-${i}-${idx}`" :class="req.class">
                  {{ req.format(row[req.idx], row) }}
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div class="q-py-md">
        Row 1 - {{ csvData.length }} of {{ session._fastjoin?.sheetLength || csvData.length }}
      </div>
    </template>

  </div>

  <common-dialog setting="medium" v-model="uploadDialog">
    <div class="q-pa-md _fw">
      <csv-upload
          :max-file-size="1024 * 1024 * 2.5"
          :headers="Object.keys(censusIndexByKey).join(',')"
          :response="response"
          :required="fields.required"
          :header-labels="uploadConfigs.headerLabels"
          @ready="uploadList"
          @clear="response = {}"
          :example-data="uploadConfigs.exampleData"
      ></csv-upload>
    </div>
  </common-dialog>

</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import CsvUpload from 'components/common/uploads/csv/CsvUpload.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {$errNotify, $possiblyPlural, $round, dollarString} from 'src/utils/global-methods';
  import {computed, ref, watch} from 'vue';
  import {multiCellArray} from 'src/utils/csv';
  import {idGet} from 'src/utils/id-get';
  import {toList, getReqs, censusIndexByKey} from './pricing';
  import {useBillErasers} from 'stores/bill-erasers';
  import {axiosFeathers, restCore} from 'components/common/uploads/services/utils';
  import {SessionStorage} from 'symbol-auth-client';
  import {useRoute, useRouter} from 'vue-router';
  import {HFind} from 'src/utils/hFind';
  import {usePrices} from 'stores/prices';

  const router = useRouter();
  const route = useRoute();

  const priceStore = usePrices()

  const ndcVersion = ref(11)

  const { reqs, errs, csvData } = getReqs({ ndcVersion });
  const fields = computed(() => {
    const example = [];
    const required = [];
    for(let i = 0; i < reqs.value.length; i++){
      const req = reqs.value[i];
      example.push({ header: req.key, required: req.required, ex: req.ex });
      required.push({ field: req.key, format: req.rev, v: { check: 'format', type: undefined, format: (v) => req.check(v?.value), required: req.required } });
    }
    return { example, required }
  })
  const eraserStore = useBillErasers();

  const uploadConfigs = computed(() => {
    const exampleData = []
    const headerLabels = {}
    for (const k of reqs.value) {
      headerLabels[k.key] = k.label;
      exampleData.push({ ...k, header: k.label })
    }
    return { exampleData, headerLabels }
  })

  const emit = defineEmits(['update:id']);
  const props = defineProps({
    id: String
  })

  const uploadDialog = ref(false);

  const focused = ref([0, 0]);
  const hasFocused = ref(false);
  const lastTab = ref(false);
  const setFocus = (val, lt) => {
    focused.value = val;
    hasFocused.value = true;
    const el = document.getElementById(`inp-${val[0]}-${val[1]}`);
    // console.log('el', el);
    if (el) el.focus();
    lastTab.value = lt;
  }
  const loading = ref(false);

  const selected = ref({});

  const sArray = computed(() => {
    let res = [];
    for (const k in selected.value) {
      if (selected.value[k]) res.push(k)
    }
    return res;
  })

  const { item: session } = idGet({
    store: eraserStore,
    value: computed(() => props.id)
  ,
    useAtcStore
  })

  watch(session, (nv, ov) => {
    if (nv._fastjoin?.sheetLength && nv._fastjoin.sheetLength !== ov?._fastjoin?.sheetLength) {
      csvData.value = (nv._fastjoin.sheet || [[]])
    }
  }, { immediate: true })

  const idIdxs = [6, 8, 10, 12, 14, 16];

  const extraReqs = computed(() => {
    const length = Object.keys(censusIndexByKey).length
    return [
      {
        class: 'tw-six text-primary',
        key: 'best',
        label: 'Best Price',
        idx: length + 2,
        format: (val, row) => {
          const num = Number(row[length])
          if (!num) return '-'
          return dollarString(num, '$', 2)
        }
      },
      {
        class: 'tw-six text-accent',
        key: 'second',
        label: 'Second Best',
        idx: length + 4,
        format: (val, row) => {
          let num = '';
          for (const idx of idIdxs) {
            if (row[idx + 1] && Number(row[idx + 1] > 0)) num = Math.min(num || Infinity, Number(row[idx + 1]));
          }
          if (!num) return '-'
          return dollarString(num, '$', 2)
        }
      },
      {
        class: 'tw-six text-a7',
        key: 'third',
        label: 'Exact Match',
        idx: length + 8,
        format: (val, row) => {
          let num = '';
          for (const idx of [6, 8, 10]) {
            if (row[idx + 1] && Number(row[idx + 1] > 0)) num = Math.min(num || Infinity, Number(row[idx + 1]));
          }
          if (!num) return '-'
          return dollarString(num, '$', 2)
        }
      }
    ]
  })
  const resultReqs = computed(() => {
    if (!session.value._fastjoin?.sheetLength) return []
    const filteredReqs = reqs.value.filter(a => a.results).map(a => {
      return {
        ...a,
        idx: censusIndexByKey[a.key]
      }
    })
    return [
      ...filteredReqs,
      ...extraReqs.value
    ]
  })


  const popup = ref(-1);
  const { h$: p$ } = HFind({
    store: priceStore,
    pause: computed(() => popup.value === -1),
    limit: ref(6),
    params: computed(() => {
      return {
        query: {
          $sort: { price: 1 },
          _id: {
            $in: idIdxs.map(a => (csvData.value[popup.value] || [])[a]).filter(a => !!a && a.length === 24)
          }
        }
      }
    })
  })


  const allOn = computed(() => sArray.value.length === csvData.value.length)

  const trySave = async () => {
    if (!Object.keys(errs.value).length) {
      loading.value = true;
      const args = [{ updatedAt: new Date() }, {
        runJoin: {
          drug_upload: {
            csvData: toList(csvData.value, reqs.value),
            headers: censusIndexByKey
          }
        }
      }];
      let sesh;
      if (session.value._id) sesh = await eraserStore.patch(session.value._id, ...args)
          .catch(err => $errNotify(err.message))
      else sesh = await eraserStore.create(...args)
          .catch(err => $errNotify(err.message))

      loading.value = false;
      if (sesh) {
        emit('update:id', sesh._id);
        eraserStore.patchInStore(sesh._id, sesh)
        csvData.value = sesh._fastjoin.sheet;
      }

    } else $errNotify('Correct all data errors to continue')
  }

  const checkData = (row, col) => {
    if (!reqs.value[col].required) {
      delete errs.value[row];
    } else if (!csvData.value[row] || !((csvData.value[row] || []).filter(a => !!a)).length) {
      delete errs.value[row];
      csvData.value.splice(row, 1);
    } else reqs.value[col].check(csvData.value[row][col], row, col);
  }

  const checkAll = () => {
    const data = csvData.value;
    for (let i = 0; i < data.length; i++) {
      for (let idx = 0; idx < data[i].length; idx++) {
        checkData(i, idx)
      }
    }
  }

  // const setTo = ref()
  const setData = (row, col, value) => {
    if (row > 1000) return $errNotify('If you need more than 1,000 rows, upload your file instead')
    // Ensure the row exists
    while (csvData.value.length <= row) {
      csvData.value.push([]); // Add an empty array for missing rows
    }

    // Ensure the row has enough columns
    while (csvData.value[row].length <= col) {
      csvData.value[row].push(''); // Fill missing columns with `null` or any placeholder value
    }

    // Set the value at the specified row and column
    csvData.value[row][col] = reqs.value[col].rev(value);
    // if(setTo.value) clearTimeout(setTo.value);
    // setTo.value = setTimeout(() => trackChange(), 1500);
  }

  // const handleFile = () => {
  //   for(let i = 0; i < data.length)
  // }

  const handlePaste = (row, col, evt) => {
    evt.preventDefault()
    const d = multiCellArray(evt.clipboardData.getData('text').trim());
    if (d.length + row > 1000) return $errNotify('If you need more than 1,000 rows, upload your file instead')
    for (let i = col; i < reqs.value.length; i++) {
      setData(row, i, d[0][i - col])
    }
    for (let i = 1; i < d.length; i++) {
      if (!Array.isArray(csvData.value[i + row])) csvData.value[i + row] = [];
      for (let idx = col; idx < Math.max(reqs.value.length, Math.min(reqs.value.length, (d[i] || []).length + col)); idx++) {
        setData(i + row, idx, d[i][idx - col])
      }
    }
    checkAll()
  }

  const remove = () => {
    const arr = Object.keys(selected.value);

    for (let i = arr.length; i > -1; i--) {
      if (selected.value[arr[i]]) {
        csvData.value.splice(Number(arr[i]), 1);
        selected.value[arr[i]] = false
      }
    }
    trackChange();
  }

  const selectAll = (val) => {
    if (val) {
      for (let i = 0; i < csvData.value.length; i++) {
        selected.value[i] = true;
      }
    } else selected.value = {};
  }

  const save = async () => {
    loading.value = true
    try {
      checkAll()
      await trySave()
    } catch (e) {
      console.error(`Error running analysis: ${e.message}`)
    } finally {
      loading.value = false;
    }
  }

  const response = ref({})
  const uploadList = async (val, meta) => {
    loading.value = true;
    uploadDialog.value = false
    const method = session.value._id ? axiosFeathers().patch : axiosFeathers().post;
    const path = session.value._id ? '/bill-erasers' : `/bill-erasers/${session.value._id}`
    const res = await method(path, val, {
      params: {
        runJoin: { drug_upload: { ...meta, def_headers: censusIndexByKey }, singleFile: true },
        core: restCore()
      }
    })
        .catch(err => {
          console.error(err.message);
          $errNotify(`Error - try again. ${err.message}`)
        })
    loading.value = false;
    if (res.data?._id) {
      emit('update:id', res.data._id);
      csvData.value = res.data._fastjoin.sheet;
      eraserStore.patchInStore(res.data._id, res.data)
    }
  };


  const keyDown = (e) => {
    if (e.keyCode === 9) {
      e.preventDefault();
      let i = focused.value[0];
      let idx = focused.value[1];
      if (idx >= reqs.value.length - 1) {
        if (i < csvData.value.length - 1) {
          i += 1;
          idx = 0;
        } else csvData.value.push([]);
      } else idx++
      setFocus([i, idx - (lastTab.value ? 0 : 1)], true);
    } else if (['Enter', 'ArrowDown'].includes(e.key)) {
      e.preventDefault();
      if (hasFocused.value) {
        const row = focused.value[0];
        if (row < csvData.value.length - 2) {
          setFocus([focused.value[0] + 1, focused.value[1]])
        }
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (focused.value[0] > 0) {
        setFocus([focused.value[0] - 1, focused.value[1]])
      }
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      const lastVisible = reqs.value.length - 1;
      if (focused.value[1] <= lastVisible - 1) {
        let idx = focused.value[1] + 1;
        setFocus([focused.value[0], idx])
      } else setFocus([focused.value[0], 0])
    } else if (e.key === 'ArrowLeft') {
      e.preventDefault();
      const firstVisible = 0
      if (focused.value[1] > firstVisible) {
        let idx = focused.value[1] - 1;
        setFocus([focused.value[0], idx])
      } else setFocus([focused.value[0], reqs.value.length - 1])
    }
  }

    const requiredDownload = computed(() => {
      if (!session.value._fastjoin) return false;
      return session.value._fastjoin.sheetLength > session.value._fastjoin.sheet?.length
    })

    const downloadFile = async () => {
      const blob = new Blob([session.value._fastjoin.rawCsv], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);

      // Create a temporary link element
      const a = document.createElement('a');
      a.href = url;
      a.download = 'commonrx_pricing';

      // Programmatically click the link
      document.body.appendChild(a);
      a.click();

      // Cleanup: Remove the link and revoke the Blob URL
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }

    const reset = () => {
      SessionStorage.removeItem('rx_session');
      const { href } = router.resolve({ name: route.name, params: { sessionId: '' } })
      window.open(href, '_self');
      // nextTick(() => router.go(0))
    }

</script>

<style lang="scss" scoped>

  .__tw {
    width: 100%;

    //background: var(--q-ir-grey-1);

    .__tbl {
      width: 100%;
      position: relative;
      min-height: 250px;

      .__loader {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(4px);
        z-index: 10;
      }


      .__slider {
        width: 350px;
        max-width: 99%;
        transition: all .3s;
        position: absolute;
        height: 100%;
        max-height: 90vh;
        z-index: 5;
        border-radius: 0 10px 10px 0;
        padding: 20px 10px;
        background: rgba(0, 0, 0, .8);
        backdrop-filter: blur(4px);
        box-shadow: 0 3px 5px var(--ir-light);
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;

        > div {
          &:last-child {
            height: 100%;
            width: 100%;
            overflow-y: scroll;
          }
        }
      }

      .__off {
        padding: 0;
        max-width: 0;
        overflow: hidden;
      }
    }
  }

  .__table {
    width: 100%;
    overflow-x: scroll;
    overflow-y: scroll;
    max-height: 90vh;

  }

  input {
    border: none;
    width: 6em;
    box-sizing: content-box;
    padding: 0px;
    background-color: transparent;

    &:focus {
      border: none;
      outline: none;
    }
  }

  .__hide {
    opacity: 0;
    pointer-events: none;
    width: 0;
  }

  .__inp {
    width: 100%;
    border-collapse: collapse;


    tr {
      th {
        padding: 2px 4px;
        text-align: left;
        //border: solid 1px #999;
        font-size: var(--text-xxs);
        color: var(--q-ir-grey-7);
      }

      td {
        font-size: var(--text-xxs);
        padding: 4px 8px;
        text-align: right;
        border: solid 1px #999;
        background: white;

        &:first-child {
          background: var(--q-ir-grey-2) !important;
        }
      }

      &:first-child {

        td {
          background: var(--q-ir-grey-2);
        }
      }
    }
  }

  .__res {
    tr {
      &:first-child {
        td {
          background: white;
        }
      }

      td {
        cursor: pointer;
      }
    }

    .__active {
      td {
        background: var(--q-ir-yellow-3) !important;
      }
    }
  }

  .__ers {
    tr {
      td {
        background: var(--q-ir-red-1) !important;
      }
    }
  }

  .__err {
    background: var(--q-ir-red-1) !important;
    color: var(-q-ir-red-10) !important;
  }

  .__errs {
    padding: 0;
    width: 100%;
    height: auto;
    max-height: 0;
    overflow: hidden;
    transition: all .3s ease;
  }

  .__on {
    max-height: 1500px;
    padding: 0 10px 20px 10px;
  }

</style>
