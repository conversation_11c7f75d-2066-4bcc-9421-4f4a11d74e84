<template>
  <div class="flex items-center">
    <q-chip icon-right="mdi-menu-down" label="Select Practitioner" color="transparent" clickable>
      <q-menu>
        <div class="_fw mw500">
          <q-input dense v-model="search.text" clearable clear-icon="mdi-close">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
          <q-list separator dense>
            <q-item-label header>Practitioners your plan has seen in the past 3 years</q-item-label>
            <practitioner-item v-for="(p, i) in p$.data" :key="`pract-${i}`" :model-value="p" clickable @click="toggle(p._id)">
            </practitioner-item>
          </q-list>
        </div>
      </q-menu>
    </q-chip>
    <practitioner-chip
        v-for="(p, i) in val || []"
        :key="`prac-${i}`"
        :chip-attrs="{ size: 'sm', color: 'transparent', removable: true, iconRemove: 'mdi-close' }"
        :model-value="p"
        @remove="toggle(p)"
    ></practitioner-chip>
  </div>
</template>
{
}

<script setup>
  import PractitionerItem from 'components/practitioners/cards/PractitionerItem.vue';
  import PractitionerChip from 'components/practitioners/cards/PractitionerChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed} from 'vue';
  import {HFind} from 'src/utils/hFind';

  import {usePlans} from 'stores/plans';
  import {idGet} from 'src/utils/id-get';
  import {usePractitioners} from 'stores/practitioners';
  import {HQuery} from 'src/utils/hQuery';
  import {_get} from 'symbol-syntax-utils';

  const planStore = usePlans();
  const pStore = usePractitioners();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    multiple: Boolean,
    plan: { required: false },
    modelValue: [Array,String,Number]
  })

  const { item:fullPlan } = idGet({
    value: computed(() => props.plan),
    store: planStore
  ,
    useAtcStore
  })

  const val = computed(() => {
    const m = props.modelValue;
    if(m === '*') return undefined
    else return m;
  })

  const toggle = (v) => {
    if(!props.multiple) emit('update:model-value', v)
    else {
      if(!props.modelValue) emit('update:model-value', [v])
      else {
        const idx = props.modelValue.indexOf(v);
        if(idx > -1) {
          const arr = [...props.modelValue];
          arr.splice(idx, 1);
          emit('update:model-value', arr);
        } else emit('update:model-value', [...props.modelValue, v]);
      }
    }
  }

  const { search, searchQ } = HQuery({})
  const params = computed(() => {
    const planId = fullPlan.value?._id;
    const hackId = { plan_procedures: planId };
    const getId = `plan_providers:plans:${planId}`
    const storeData = pStore.findInStore({ query: { [`_fastjoin.${getId}`]: { $exists: true} } });
    if(storeData.total) hackId[getId] = _get(storeData.data[0], `_fastjoin.${getId}`)
    return {
      hackId,
      query: searchQ.value
    }
  })

  const { h$:p$ } = HFind({
    store: pStore,
    pause: computed(() => !fullPlan.value),
    params
  })
</script>

<style lang="scss" scoped>

</style>
