<template>
  <div class="flex items-center">
    <q-chip icon-right="mdi-menu-down" label="Select Provider" color="transparent" clickable>
      <q-menu>
        <div class="_fw mw500">
          <q-input dense v-model="search.text" clearable clear-icon="mdi-close">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
          <q-list separator>
            <q-item-label header>Providers your plan has seen in the past 3 years</q-item-label>
            <provider-item
                clickable
                @click="toggle(p._id)"
                simple
                v-for="(p, i) in p$.data"
                :key="`provider-${i}`"
                :model-value="p">
            </provider-item>
          </q-list>
        </div>
      </q-menu>
    </q-chip>
    <provider-chip
        v-for="(provider, i) in val || []"
        :key="`prvdr-${i}`"
        :chip-attrs="{ size: 'sm', color: 'transparent', removable: true, iconRemove: 'mdi-close' }"
        :model-value="provider"
        @remove="toggle(provider)"></provider-chip>
  </div>
</template>

<script setup>
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';

  import {usePlans} from 'stores/plans';
  import {useProviders} from 'stores/providers';
  import {idGet} from 'src/utils/id-get';
  import {_get} from 'symbol-syntax-utils';
  import {HQuery} from 'src/utils/hQuery';

  const planStore = usePlans();
  const providerStore = useProviders();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    multiple: Boolean,
    plan: { required: false },
    modelValue: [Array, String, Number]
  })

  const { item: fullPlan } = idGet({
    value: computed(() => props.plan),
    store: planStore
  ,
    useAtcStore
  })

  const val = computed(() => {
    return props.modelValue;
  })

  const toggle = (v) => {
    if (!props.multiple) emit('update:model-value', v)
    else {
      if (!props.modelValue) emit('update:model-value', [v])
      else {
        const idx = props.modelValue.indexOf(v);
        if (idx > -1) {
          const arr = [...props.modelValue];
          arr.splice(idx, 1);
          emit('update:model-value', arr);
        } else {
          emit('update:model-value', [...props.modelValue, v]);
        }
      }
    }
  }

  const { search, searchQ } = HQuery({})

  const params = computed(() => {
    const planId = fullPlan.value?._id;
    const hackId = { plan_procedures: planId };
    const getId = `plan_procedures:plans:${planId}`
    const storeData = providerStore.findInStore({ query: { [`_fastjoin.${getId}`]: { $exists: true} } });
    if(storeData.total) hackId[getId] = _get(storeData.data[0], `_fastjoin.${getId}`)
    return {
      hackId,
      query: searchQ.value
    }
  })
  const { h$: p$ } = HFind({
    store: providerStore,
    pause: computed(() => !fullPlan.value),
    limit: ref(5),
    params
  })
</script>

<style lang="scss" scoped>

</style>
