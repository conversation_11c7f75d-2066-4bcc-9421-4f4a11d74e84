<template>
  <div class="_fw">
    <plan-people-picker
        v-bind="{
      query,
      emitValue: true,
      multiple: true,
      placeholder: 'Filter By Participant',
      ...$attrs,
      modelValue: val
    }"
        :plan="fullPlan"
        @update:model-value="emitUp"
    ></plan-people-picker>
  </div>
</template>

<script setup>

  import PlanPeoplePicker from 'components/plans/lists/PlanPeoplePicker.vue';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {usePlans} from 'stores/plans';

  const planStore = usePlans();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    plan: { required: true },
    modelValue: { required: true },
    query: Object
  })

  const { item: fullPlan } = idGet({
    value: computed(() => props.plan),
    store: planStore,

    useAtcStore
  })

  const val = computed(() => {
    if (props.modelValue === '*') return undefined
    return props.modelValue;
  })

  const emitUp = (val) => {
    emit('update:model-value', val);
  }
</script>

<style lang="scss" scoped>

</style>
