import {computed} from 'vue';
import {dollarString} from 'src/utils/global-methods';
import {getErContribution} from 'components/enrollments/utils/contributions';
import {AnyRef} from 'src/utils/types';
import {getElectedContributions, getEmployerFlexCredits} from 'components/plans/utils/calcs';
import {idGet} from 'src/utils/id-get';
import {useHouseholds} from 'stores/households';
import {incomeDisplay} from 'components/comps/utils/index';
import {useAtcStore} from 'src/stores/atc-store';

type Options = {
    plan:AnyRef,
    person:AnyRef,
    enrollment?:AnyRef,
    coverageId?:AnyRef,
    household?:AnyRef
}
export const erSubsidy = ({ plan, enrollment, person, coverageId, household }:Options) => {
    const hhStore:any = useHouseholds();
    const { item:hh } = idGet({
        store: hhStore,
        value: computed(() => household?.value || person.value.household),
        useAtcStore
    })
    const allowance = computed(() => {
        if (!plan.value) return 'See Plan'
        let cont = (plan.value.employerContribution || {})['*']
        if (!cont) {
            if (!person.value) return 'See Plan'
            const groupMatch = (plan.value.groups || []).filter(a => (person.value.inGroups || []).includes(a))[0];
            if (!groupMatch) return 'See Plan'
            cont = (plan.value.employerContribution || {})[groupMatch]
            if (!cont) return 'See Plan'
        }
        if (cont.type === 'percent') return `${dollarString(cont.amount, '', 2)}% of ${cont.percentType === 'cost' ? 'cost' : 'pay'}${cont.match ? ' - match' : ''}`
        if (cont.type === 'flat') return `${dollarString(cont.amount, '$', 2)}${cont.match ? ' - match' : ''}`
        else return 0
    })

    const income = incomeDisplay((hh.value._fastjoin?.cams || []).filter(a => [...plan.value.orgs || [], plan.value.org].includes(a.org)))

    const elected = computed(() => getElectedContributions(enrollment.value))

    const employer = computed(() => getEmployerFlexCredits(plan.value, { enrollment: enrollment.value, totalIncome: income.value.total, baseIncome: income.value.amount, contributions: elected.value.total }) || 0)


    const subsidy = computed(() => {
        if (!plan.value) return '';
        return (getErContribution({
            covId: coverageId?.value,
            plan: plan.value,
            enrollment: enrollment.value
        }) || 0)
    })

    return { subsidy, allowance, income, elected, employer, hhStore, household:hh }
}
