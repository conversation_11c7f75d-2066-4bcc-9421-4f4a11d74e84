<template>
  <div>
    <slot name="top"></slot>
    <div class="_form_grid">
      <template v-for="(sec, i) in Object.keys(sections)" :key="`sec-${i}`">
        <div class="_form_label">{{ sections[sec]?.label }}</div>
        <div class="q-pa-sm">
          <div class="font-7-8r tw-five" v-html="sections[sec]?.text"></div>

          <div class="q-pt-md">
            <template v-if="!form[sec]?.signature">
              <div class="text-xxs tw-six text-ir-deep flex items-center">
                <div>Draw Signature</div>
                <q-icon class="q-ml-xs" size="25px" name="mdi-signature" color="primary"></q-icon>
              </div>
              <div class="__sig">
                <signature-draw
                    v-bind="sizes"
                    @update:model-value="addSignature($event, sec)"></signature-draw>
              </div>
              <template v-if="signature">
                <div class="q-pt-sm text-xxs text-ir-deep">Use this one</div>
                <div @click="addSignature(signature, sec)" v-html="signature" :class="`__sigc w200 h50 __${sec}`"></div>
              </template>
            </template>
            <q-slide-transition>
              <div class="_fw" v-if="form[sec]?.signature">
              <div class="row items-center" >
                <div v-html="form[sec].signature" :class="`SigDisplay __${sec}`"></div>
                <q-btn dense flat color="red" icon="mdi-close" @click="delete form.value[sec].signature"></q-btn>
              </div>
              <div class="font-3-4r tw-five">Signed: {{formatDate(form[sec].acceptedAt, 'MM-DD-YYYY h:mm a')}}</div>
              </div>
            </q-slide-transition>

          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
  import SignatureDraw from 'components/bill-collective/utils/SignatureDraw.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {loginPerson} from 'stores/utils/login';
  import {idGet} from 'src/utils/id-get';
  import {useRefs} from 'stores/refs';
  import {computed, nextTick, ref, watch} from 'vue';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {useHosts} from 'stores/hosts';
  import {attestations} from 'components/market/shop/utils/attest';
  import {checkOrAdd} from 'src/fingerprints';
  import { formatDate } from 'src/utils/date-utils';

  const { person, login } = loginPerson();

  const refStore = useRefs();
  const hostStore = useHosts();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    shop: { required: true }
  })

  const sizes = ref({ height: '150px', width: '500px', maxHeight: '60vh', maxWidth: '100%' });


  const { item: def_host } = idGet({
    store: hostStore,
    value: computed(() => SessionStorage.getItem('def_host_id'))
  ,
    useAtcStore
  })
  const hostId = computed(() => props.shop?.host || LocalStorage.getItem('host_id') || def_host.value._id)
  const { item: host } = idGet({
    store: hostStore,
    value: hostId
  ,
    useAtcStore
  })

  const refId = computed(() => props.shop?.ref)
  const { item: reF } = idGet({
    store: refStore,
    value: refId
  ,
    useAtcStore
  })

  const agent = ref({})

  watch(() => host.value._id, (nv) => {
    if (nv && !agent.value._id) {
      setTimeout(() => {
        if (host.value.npn) agent.value = host.value;
        else if (reF.value.npn) agent.value = reF.value;
        else agent.value = def_host.value;
      }, 1000);
    }
  }, { immediate: true })

  const { sections } = attestations({ person, agent })

  const formFn = (defs) => {
    const obj = {};
    for (const k in sections.value) {
      obj[k] = {}
    }
    return {
      ...obj,
      ...defs
    }
  }
  const form = ref(formFn())

  const signature = computed(() => {
    let sig = '';
    for (const k in form.value) {
      if (form.value[k].signature) {
        sig = form.value[k].signature;
        break;
      }
    }
    return sig;
  })

  watch(sections, (nv) => {
    if (nv) {
      for (const k in nv) {
        if (!form.value[k]) form.value[k] = {}
      }
    }
  }, { immediate: true, deep: true })


  const resetPads = () => {
    nextTick(() => {
      for (const k in sections.value) {
        const els = document.querySelectorAll(`.__${k}`);
        for (const el of els) {
          if (el) {
            el.innerHTML = form.value[k].signature || signature.value;
            const svg = el.querySelector('svg');
            const width = el.offsetWidth;
            const height = el.offsetHeight;

            const bbox = svg.getBBox();

            // Set the viewBox based on the bounding box
            svg.setAttribute('viewBox', `${bbox.x} ${bbox.y} ${bbox.width} ${bbox.height}`);

            // Match the SVG size to the container
            // svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
            svg.setAttribute('width', width);
            svg.setAttribute('height', height);
          }
        }
      }
    })
  }
  const addSignature = async (signature, id) => {
    const terms = document.getElementById('ReviewTerms')?.innerText;
    // console.log('got terms?', terms);
    const { data } = await checkOrAdd()
    const mandate = {};
    mandate.ip = data.ipInfo.ip;
    mandate.user_agent = window.navigator.userAgent;
    mandate.acceptedAt = new Date();
    mandate.copy = terms;
    mandate.login = login.value._id
    mandate.phone = person.value.phone?.number?.e164
    mandate.email = person.value.email
    mandate.signature = signature;
    mandate.copy = sections.value[id].text
    form.value[id] = mandate
    emit('update:model-value', form.value);
    setTimeout(() => {
      resetPads();
    }, 500)
  }

  watch(() => props.shop, (nv, ov) => {
    if(nv && nv._id !== ov?._id){
      form.value = formFn(nv.attest)
      resetPads()
    }
  }, { immediate: true })


</script>

<style lang="scss" scoped>
  .SigDisplay {
    height: 50px;
    width: 200px;
  }

  .__sig {
    width: 100%;
    max-width: 500px;
  }
  .__sigc {
    border-radius: 5px;
    box-shadow: 2px 2px 8px var(--ir-light);
    transition: all .3s;
    cursor:pointer;
    background: var(--ir-bg2);

    &:hover {
      transform: translate(0, -2px);
    }
  }
</style>
