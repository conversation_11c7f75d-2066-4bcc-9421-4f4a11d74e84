<template>
  <div class="flex items-center __hr">
    <q-icon @click="editing = p" class="__h" v-for="(p, i) in people" :key="`p-${i}`" :size="p.age < 18 ? smSize(p.age) : size" :color="greyList?.includes(p._id) ? 'ir-mid' : colors[p.gender] || 'ir-mid'" :name="`mdi-human-${p.gender === 'female' ? 'female' : 'male'}`">
      <q-tooltip class="text-xxs tw-six">
        {{p.firstName}}: {{p.gender || 'unknown gender'}} - age {{p.age}}
      </q-tooltip>
    </q-icon>

    <q-btn v-if="form || add" size="sm" dense flat color="ir-mid" icon="mdi-plus-thick" @click="adding = true"></q-btn>

    <common-dialog v-if="form" setting="smmd" :model-value="!!editing || adding" @update:model-value="toggleDialog">
      <div class="_fw bg-ir-bg1 text-ir-text q-pa-lg">
        <div v-if="editing" class="row justify-end q-pb-sm">
          <remove-proxy-btn :name="editing.firstName || 'member'" dense flat color="red" icon="mdi-delete" @remove="remove(editing)"></remove-proxy-btn>
        </div>
        <member-form limited special-change @update:model-value="addMember" :model-value="editing">
          <template v-slot:bottom="scope">
            <slot name="bottom" :form="scope.form" :member="editing"></slot>
          </template>
        </member-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import MemberForm from 'components/households/forms/MemberForm.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useHouseholds} from 'stores/households';
  import {getAge} from 'components/enrollments/ichra/utils';
  import {usePpls} from 'stores/ppls';

  import {$errNotify} from 'src/utils/global-methods';
  import {HFind} from 'src/utils/hFind';

  const hhStore = useHouseholds();
  const pplStore = usePpls();

  const colors = {
    'male': 'ir-light-blue',
    'female': 'ir-pink-4'
  }

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    greyList: Array,
    modelValue: { required: true },
    simple: Array,
    size: { default: '30px' },
    form: Boolean,
    add: Boolean
  })

  const { item:household } = idGet({
    store: hhStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const { item:person } = idGet({
    store: pplStore,
    value: computed(() => household.value?.person)
  ,
    useAtcStore
  })

  const memberIds = computed(() => Object.keys(household.value.members || {}))
  const { h$:p$ } = HFind({
    store: pplStore,
    limit: computed(() => memberIds.value.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: memberIds.value }
        }
      }
    })
  })

  const adding = ref(false);
  const editing = ref();
  const toggleDialog = (val) => {
    if(!val) {
      editing.value = undefined;
      adding.value = false;
    }
  };

  const people = computed(() => {
    const arr = p$.data;
    const sorted = [];
    for(let i = 0; i < arr.length; i++) {
      const age = getAge(arr[i].dob) || 40
      sorted.push({ ...arr[i], age })
    }
    sorted.sort((a, b) => b.age - a.age)
    sorted.unshift({ ...person.value, age: getAge(person.value?.dob) || 50 })
    return sorted;
  })

  const smSize = (age) => {
    const num = Number((props.size || '30').replace(/[^\d.]/g, ''));
     return Math.floor(num * ((85 - (18 - age))/100)) + 'px'
  }

  const addMember = () => {
    console.log('member updated');
    emit('update:model-value', household.value)
  }

  const remove = async (p) => {
    editing.value = undefined;
    const hh = await hhStore.patch(household.value?._id, { $unset: {[`members.${p._id}`]: ''} })
        .catch(err => {
          console.error(err.message);
          $errNotify(`Error removing person: ${err.message}`)
        })
    if(hh && !p.login){
      await pplStore.remove(p._id, { special_change: '*', disableSoftDelete: true })
    }
    emit('update:model-value', household.value)
  }

</script>

<style lang="scss" scoped>

  .__h {
    cursor:pointer;
    transition: all .2s;
    &:hover {
      transform: translate(0, -2px);
    }
  }
  .__hr {
    max-width: 100%;
    overflow-x: scroll;
  }
</style>
