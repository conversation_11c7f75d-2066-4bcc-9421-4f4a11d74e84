<template>
  <div class="_fw relative-position bg-white">
    <q-tab-panels class="_panel" v-model="flow" animated transition-prev="jump-down" transition-next="jump-up">
      <q-tab-panel class="_panel" name="login">
        <div class="_fw bg-white" v-if="!isAuthenticated">
          <q-chip square dense class="__title">Login or Register</q-chip>
          <div class="flex items-center q-pt-sm">
            <q-chip square dense size="sm" class="bg-transparent" clickable @click="tab = 'webauthn'">
              <span :class="`q-mr-sm tw-${tab === 'webauthn' ? 'six' : 'five'} font-5-8r text-uppercase`">by device</span>
              <q-icon :color="tab === 'webauthn' ? 'primary' : 'ir-deep'" name="mdi-key" class="font-7-8r"></q-icon>
            </q-chip>
            <div>|</div>
            <q-chip square dense size="sm" class="bg-transparent" clickable @click="tab = 'email'">
              <span :class="`q-mr-sm tw-${tab === 'email' ? 'six' : 'five'} font-5-8r text-uppercase`">by email</span>
              <q-icon :color="tab === 'email' ? 'primary' : 'ir-deep'" name="mdi-email" class="font-7-8r"></q-icon>
            </q-chip>
            <div>|</div>
            <q-chip square dense size="sm" class="bg-transparent" clickable @click="tab = 'phone'">
              <span :class="`q-mr-sm tw-${tab === 'phone' ? 'six' : 'five'} font-5-8r text-uppercase`">by phone</span>
              <q-icon :color="tab === 'phone' ? 'primary' : 'ir-deep'" name="mdi-cellphone" class="font-7-8r"></q-icon>
            </q-chip>
          </div>
          <q-tab-panels keep-alive class="_panel" v-model="tab" animated transition-prev="jump-down" transition-next="jump-up">
            <q-tab-panel class="_panel" name="webauthn">
              <div class="_fw q-py-sm">
                <web-authn-client
                    :person-id="personId"
                    @update:tab="tab = $event;"
                    v-model:email="email" @update:on="setWebAuthn"></web-authn-client>
              </div>
              <div class="q-py-sm">
                <q-chip color="transparent" clickable @click="webAuthnOff = true;tab='email'">
                  <span class="q-mr-sm">Use a password instead</span>
                  <q-icon name="mdi-arrow-right" color="primary"></q-icon>
                </q-chip>
              </div>
            </q-tab-panel>
            <q-tab-panel class="_panel" name="email">
              <div :class="`_fw bg-ir-bg ${focusOn === 'email' ? '__show' : ''}`">
                <login-input :person-id="personId" @reset="flow = 'reset'" type="email" @focus="focusOn = 'email'" @blur="focusOn = ''" v-model="email"></login-input>
              </div>
            </q-tab-panel>
            <q-tab-panel class="_panel" name="phone">
              <div :class="`_fw bg-ir-bg ${focusOn === 'phone' ? '__show' : ''}`">
                <login-input :person-id="personId" @reset="flow = 'reset'" type="phone" @focus="focusOn = 'phone'"
                             @blur="focusOn = ''"></login-input>
              </div>
            </q-tab-panel>
          </q-tab-panels>


        </div>
        <div v-else>
          <div class="font-3-4r text-weight-medium q-py-sm">You are logged in - <span
              class="cursor-pointer text-ir-blue-6 text-uppercase font-2-3r" @click="logout">logout</span></div>
          <div>
            <q-separator class="q-mb-sm"></q-separator>
            <q-slide-transition>
              <div v-if="total">
                <q-chip color="primary" square dark class="font-3-4r">We found profiles that match your data</q-chip>
                <div class="row items-center">
                  <div class="col-6 q-pa-xs" v-for="(prsn, i) in data || []" :key="`prsn-${i}`">
                    <div :class="`__tile cursor-pointer q-pa-sm ${choosing === i ? '__active' : ''}`"
                         @click="choosing = i">
                      <person-tile :model-value="prsn">
                        <q-tooltip>Claim Profile</q-tooltip>
                      </person-tile>
                    </div>
                  </div>
                </div>
                <q-slide-transition>
                  <div v-if="choosing > -1" class="row items-center">
                    <div class="font-1r text-weight-medium">
                      Is this you?
                      <q-btn size="sm" flat label="No" icon="mdi-close" @click="choosing = -1"></q-btn>
                      <q-btn size="sm" outline color="green" label="Yes" icon="mdi-check" @click="claimProfile"></q-btn>
                    </div>
                  </div>
                </q-slide-transition>
                <q-separator class="q-mt-sm"></q-separator>
              </div>
            </q-slide-transition>

            <div class="flex items-center cursor-pointer" @click="$router.push('/profile')">
              <default-avatar :model-value="person" :use-atc-store="useAtcStore"></default-avatar>
              <div class="q-pa-sm font-3-4r">
                <div class="font-1r"><b>{{ person?.name }}</b></div>
                <div class="flex items-center">
                  <q-chip size="sm" color="white" v-if="person?.phone" :label="person.phone.number?.national"
                          icon="mdi-cellphone"></q-chip>
                  <q-chip size="sm" color="white" v-if="person?.email" :label="person.email" icon="mdi-email"></q-chip>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="_fw bg-white q-pt-sm">
          <q-chip dense square class="bg-transparent font-5-8r tw-five text-uppercase q-py-sm">
            <span class="q-mr-sm">By Social</span>
            <q-icon name="mdi-account" class="font-7-8r" color="primary"></q-icon>
          </q-chip>
          <oauth-list></oauth-list>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="reset">
        <password-manager></password-manager>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import LoginInput from 'src/components/auth/traditional/LoginInput.vue';
  import PersonTile from 'src/components/ppls/cards/PersonTile.vue';
  import OauthList from '../oauth/OauthList.vue';
  import DefaultAvatar from 'src/components/common/avatars/DefaultAvatar.vue';
  import PasswordManager from 'components/auth/traditional/PasswordManager.vue';
  import WebAuthnClient from 'components/auth/webauthn/components/WebAuthnClient.vue';

  import {computed, onMounted, ref, watch} from 'vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useAuth} from 'src/stores/auth';
  import {_get} from 'symbol-syntax-utils';
  import {parsePhoneNumber} from 'awesome-phonenumber';
  import {isWebAuthnSupported} from 'components/auth/webauthn/webauthn';

  const authStore = useAuth();
  import {useLogins} from 'src/stores/logins';

  const store = useLogins();
  import {HFind} from 'src/utils/hFind';
  import {usePpls} from 'src/stores/ppls';
  import {useRoute} from 'vue-router';

  const props = defineProps({
    personId:String
  })

  const pplStore = usePpls();

  const route = useRoute();

  const tab = ref('email');

  const isWebAuthn = computed(() => isWebAuthnSupported());
  const webAuthnOff = ref(false);

  watch(isWebAuthn, (nv) => {
    if (nv && !webAuthnOff.value) tab.value = 'webauthn';
  }, { immediate: true })

  const setWebAuthn = (val) => {
    if (val && !webAuthnOff.value) tab.value = 'webauthn';
  }

  const login = computed(() => {
    return authStore.user;
  });

  const flow = ref('login');
  const focusOn = ref('');
  const choosing = ref(-1);

  const isAuthenticated = computed(() => {
    return authStore.isAuthenticated;
  });

  const logout = () => {
    authStore.logout();
  };

  const person = computed(() => {
    const { email, phone } = login.value || {};
    return {
      email,
      phone: phone ? parsePhoneNumber(phone) : undefined,
      ..._get(login.value, '_fastjoin.owner') || {}
    }
  })

  const email = ref('')


  const params = computed(() => {
    const query = {};
    if (login.value?.owner) query._id = { $ne: login.value.owner };
    const { email, phone } = login.value || {};
    if (email && phone) query['$or'] = [
      { email },
      { 'phone.number.e164': phone }
    ];
    else if (email) query['email'] = email;
    else if (phone) query['phone.number.e164'] = phone;
    else query.name = '****';
    return { query };
  })

  const { total, data } = HFind({
    store: pplStore,
    immediate: true,
    watch: true,
    params
  })

  const claimProfile = async () => {
    const np = data.value[choosing.value];
    if (!login.value?.id || !np?._id) return;
    if (!login.value?.owner) {
      return await store.patch(login.value._id, { owner: np._id })
    }
    const p = { ...person.value };
    const patch = {};
    let changes = false;
    Object.entries(np).forEach(([key, value]) => {
      if (!p[key]) {
        changes = true;
        patch[key] = value;
      }
    });
    if (changes) {
      await pplStore.patch(p._id, patch);
    }
    const { total } = await store.find({ query: { owner: np._id } })
    if (!total) await store.remove(np._id, { disableSoftDelete: true });
  };

  onMounted(() => {
    if (route.name === 'password-reset' || route.query.resetPassword) flow.value = 'reset';
  })
</script>

<style scoped>
  .__tile {
    border-radius: 8px;
    border: solid .5px rgba(0, 0, 0, .5);
    transition: all .3s ease-out;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0);
  }

  .__active {
    border: solid .5px rgba(0, 0, 0, 1);
    box-shadow: 0 0 8px rgba(0, 0, 0, .3);
  }

  .__title {
    background: var(--ir-bg);
    color: var(--ir-text);
    font-weight: 400;
    z-index: 1;
    font-size: 1rem;
  }
</style>
