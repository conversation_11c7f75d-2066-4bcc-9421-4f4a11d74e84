<template>
  <div class="_fw">
    <default-chip :limit="25" :model-value="item" :use-atc-store="useAtcStore"></default-chip>
    <div>
      <div class="flex items-center no-wrap">
        <q-btn dense size="sm" flat icon="mdi-email" @click="menuClick('email')">
          <q-menu :model-value="menus.email" @update:model-value="$event ? undefined : menus.email = $event">
            <q-list separator dense>
              <q-item v-for="(email, i) in item.emails || []" :key="`email-${i}`" clickable
                      @click="copy(email, 'email')">
                <q-item-section>
                  <q-item-label>{{ email }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
        <div class="font-3-4r q-ml-xs">{{item.email || _get(item, ['emails', 0])}}</div>
      </div>
      <div class="flex items-center no-wrap">
        <q-btn dense size="sm" flat icon="mdi-phone" @click="menuClick('phone')">
          <q-menu :model-value="menus.phone" @update:model-value="$event ? undefined : menus.phone = $event">
            <q-list separator dense>
              <q-item v-for="(phone, i) in item.phones || []" :key="`phone-${i}`" clickable
                      @click="copy(phone.number.e164, 'phone')">
                <q-item-section>
                  <q-item-label>{{ phone.number.national }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
        <div class="font-3-4r q-ml-xs">{{item.phone?.number?.e164 || _get(item, ['phones', 0, 'number', 'e164'])}}</div>

      </div>
    </div>
  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {$copyTextToClipboard} from 'src/utils/global-methods';
  import {usePpls} from 'src/stores/ppls';

  const store = usePpls();
  import {_get} from 'symbol-syntax-utils';
  import DefaultChip from 'src/components/common/avatars/DefaultChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  const props = defineProps({
    modelValue: { required: true },
    dense: Boolean
  });

  const menus = ref({
    email: false,
    phone: false
  });

  const { item } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  });

  const menuClick = (val) => {
    const list = _get(item.value, [`${val}s`]);
    console.log('list', list, item.value[val]);
    if (list?.length > 1) {
      menus.value[val] = true
    } else if(!!item.value[val]) copy(item.value[val], val);
  }

  const emails = computed(() => {
    return Array.from(new Set(item.value.email ? [item.value.email, ...item.value.emails || []] : []));
  });
  const phones = computed(() => {
    return Array.from(new Set(item.value.phone ? [item.value.phone, ...item.value.phones || []] : []));
  });

  const copy = (val, key) => {
    $copyTextToClipboard(val, `${key} copied`);
  };
</script>

<style scoped>

</style>
