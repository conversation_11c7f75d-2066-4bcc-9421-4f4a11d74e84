<template>
  <div class="_fw q-pa-md">
    <div class="flex items-center">
      <default-avatar :size="dense ? '40px' : '60px'" square :model-value="item" class="q-mr-sm" :use-atc-store="useAtcStore"></default-avatar>
      <div class="font-1r text-weight-medium">{{item.name}}</div>
    </div>
    <q-separator class="q-my-sm"></q-separator>
    <div class="flex items-center">
      <div :class="dense ? 'q-pa-sm' : ''">
        <q-icon name="mdi-email" color="primary"></q-icon>
      </div>
      <q-chip
          color="white"
          class="q-mr-xs"
          v-for="(email, i) in emails" :key="`email-${i}`"
          :label="email"
          clickable
          @click="copy(email, 'email')"
      ></q-chip>
    </div>
    <q-separator class="q-my-sm"></q-separator>
    <div class="flex items-center">
      <div :class="dense ? 'q-pa-sm' : ''">
        <q-icon name="mdi-phone" color="primary"></q-icon>
      </div>
      <q-chip
          color="white"
          class="q-mr-xs"
          v-for="(phone, i) in phones" :key="`phone-${i}`"
          :label="phone.number.national"
          clickable
          @click="copy(phone.number.national, 'phone')"
      ></q-chip>
    </div>
  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import { $copyTextToClipboard } from 'src/utils/global-methods';
  import { usePpls } from 'src/stores/ppls';
  import DefaultAvatar from 'src/components/common/avatars/DefaultAvatar.vue';
  import {useAtcStore} from 'src/stores/atc-store';
  const store = usePpls();

  const props = defineProps({
    modelValue: { required: true },
    dense: Boolean
  });

  const { item } = idGet({
    store,
    value: computed(() => props.modelValue ),
    useAtcStore
  });

  const emails = computed(() => {
    return Array.from(new Set(item.value.email ? [item.value.email, ...item.value.emails || []] : []));
  });
  const phones = computed(() => {
    return Array.from(new Set(item.value.phone ? [item.value.phone, ...item.value.phones || []] : []));
  });

  const copy = (val, key) => {
    $copyTextToClipboard(val, `${key} copied`);
  };
</script>

<style scoped>

</style>
