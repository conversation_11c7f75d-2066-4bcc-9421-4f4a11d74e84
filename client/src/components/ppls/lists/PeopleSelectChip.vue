<template>
  <q-chip v-bind="{ color: 'ir-bg2', clickable: true, ...$attrs }">
    <default-avatar v-if="!multiple" :model-value="person" :use-atc-store="useAtcStore"></default-avatar>
    <span v-if="!multiple">{{ person.name || picker ? 'Select Person' : ' - ' }}</span>
    <span v-else>{{ emptyLabel || picker ? 'Select Person' : ' - ' }}</span>
    <slot name="side">
      <q-icon v-if="picker" name="mdi-menu-down"></q-icon>
    </slot>

    <template v-if="picker">
      <q-popup-proxy v-model="popup">
        <div class="w500 mw100 q-pa-sm">
          <q-input v-model="search.text" dense filled>
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
          <q-list separator>
            <q-item v-for="(p, i) in p$.data" :key="`p-${i}`" clickable @click="select(p)">
              <q-item-section avatar>
                <default-avatar :model-value="p" :use-atc-store="useAtcStore"></default-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ p.name }}</q-item-label>
                <q-item-label caption>{{ p.email }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-popup-proxy>
    </template>
  </q-chip>
  <template v-if="multiple && picker">
    <q-chip v-bind="{ color: 'ir-bg2', ...$attrs }">
      <default-avatar :model-value="p" :use-atc-store="useAtcStore"></default-avatar>
    </q-chip>
  </template>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {usePpls} from 'stores/ppls';
  import {HFind} from 'src/utils/hFind';
  import {useAtcStore} from 'src/stores/atc-store';
  import {fakeId} from 'src/utils/global-methods';
  import {HQuery} from 'src/utils/hQuery';

  const pplStore = usePpls();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    comp: { required: false },
    picker: Boolean,
    multiple: Boolean,
    emptyLabel: String,
    emitValue: Boolean
  })

  const { item: person } = idGet({
    store: pplStore,
    value: computed(() => props.multiple ? undefined : props.modelValue),
    useAtcStore
  })

  const mv = computed(() => {
    if (props.multiple) return Array.isArray(props.modelValue) ? props.modelValue : [];
    else return props.modelValue;
  })

  const select = (val) => {
    if (props.emitValue) {
      if (props.multiple) {
        const idx = mv.value.indexOf(val._id);
        if (idx > -1) {
          const arr = [...mv.value];
          arr.splice(idx, 1);
          emit('update:model-value', arr)
        } else emit('update:model-value', [...mv.value, val._id])
      } else emit('update:model-value', val._id)
    } else {
      if (props.multiple) {
        const idx = mv.value.map(a => a._id).indexOf(val._id);
        if (idx > -1) {
          const arr = [...mv.value];
          arr.splice(idx, 1);
          emit('update:model-value', arr)
        } else emit('update:model-value', [...mv.value, val])
      } else emit('update:model-value', val)
    }
  }

  const popup = ref(false)

  const activeIds = computed(() => props.multiple ? props.emitValue ? mv.value : mv.value.map(a => a._id) : [])
  const { h$: ps$ } = HFind({
    store: pplStore,
    pause: computed(() => !props.picker || !props.multiple),
    limit: computed(() => mv.value?.length || 1),
    params: computed(() => {
      return {
        query: { _id: { $in: activeIds.value } }
      }
    })
  })

  const { search, searchQ } = HQuery({})
  const { h$: p$ } = HFind({
    store: pplStore,
    pause: computed(() => !popup.value || !props.picker),
    params: computed(() => {
      return {
        query: {
          _id: { $nin: activeIds.value },
          /** Must pass a query */
          ...props.params?.query || { _id: { $in: [fakeId] } },
          ...searchQ.value
        }
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
