<template>
  <div class="flex items-center">
    <default-chip
        v-for="(prsn, i) in p$.data"
        :key="`prs-${i}`"
        :model-value="prsn"
        :chip-attrs="{ removable: true, iconRemove: 'mdi-close', ...chipAttrs }"
        :use-atc-store="useAtcStore"
        @remove="remove(prsn._id)">
      <template v-if="!multiple"  v-slot:menu>
        <q-menu v-model="dialog">
          <div class="w300 mw100 bg-white q-pa-md">
            <slot name="menu-top"></slot>
            <people-list v-if="dialog" v-bind="{ params, multiple, emitValue }"
                         @update:model-value="emitUp"></people-list>
          </div>
        </q-menu>
      </template>
    </default-chip>
    <q-chip v-if="multiple || !modelValue"
            v-bind="{label: 'Select Person', iconRight: 'mdi-menu-down', color: 'ir-bg2', clickable: true, ...selectChipAttrs}">
      <q-menu v-model="dialog">
        <div class="w300 mw100 bg-white q-pa-md">
          <slot name="menu-top"></slot>
          <people-list v-if="dialog" v-bind="{ params, multiple, emitValue }"
                       @update:model-value="emitUp"></people-list>
        </div>
      </q-menu>
    </q-chip>
  </div>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import PeopleList from 'components/ppls/lists/PeopleList.vue';

  import {HFind} from 'src/utils/hFind';
  import {computed, ref} from 'vue';
  import {usePpls} from 'stores/ppls';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = usePpls();

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    inputAttrs: Object,
    searchLimit: { default: 5 },
    params: Object,
    modelValue: { required: false },
    emitValue: Boolean,
    multiple: Boolean,
    chipAttrs: Object,
    selectChipAttrs: Object
  })

  const dialog = ref(false);

  const mvIds = computed(() => props.multiple ? props.emitValue ? [...props.modelValue || []] : [...props.modelValue || []].map(a => a._id) : props.modelValue ? props.emitValue ? [props.modelValue] : [props.modelValue._id] : [])

  const { h$: p$ } = HFind({
    store,
    pause: computed(() => !dialog.value),
    limit: computed(() => mvIds.value?.length || 1),
    params: computed(() => {
      return {
        query: { _id: { $in: mvIds.value || [] } }
      }
    })
  })

  const remove = (id) => {
    if (props.multiple) {
      const idx = props.emitValue ? props.modelValue.indexOf(id) : props.modelValue.map(a => a._id).indexOf(id);
      const list = [...props.modelValue];
      list.splice(idx, 1);
      emit('update:model-value', list);
    } else emit('update:model-value', undefined)
  }
  const emitUp = (val) => {
    emit('update:model-value', val)
  }
</script>

<style lang="scss" scoped>

</style>
