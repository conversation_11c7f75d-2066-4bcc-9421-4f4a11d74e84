<template>
  <div class="_fw">
    <div class="q-pa-sm flex items-center">
      <q-img v-if="logo.url" :src="logo.url" class="h50 w50"></q-img>
      <div class="q-ml-md font-1-1-4r tw-five">{{ hs.name }}</div>
    </div>

    <q-tab-panels v-model="tab" class="_panel" animated>

      <q-tab-panel class="_panel" name="docs">
        <div class="row q-py-sm">
          <q-btn dense flat color="red" icon="mdi-close" @click="setTab('info')"></q-btn>
        </div>

        <div v-if="!product">
          <div class="q-pa-sm tw-six font-1r text-ir-mid">Select Product</div>
          <q-list separator>
            <q-item v-for="(k, i) in Object.keys(hs.products)" :key="`k-${i}`" clickable @click="setProduct(k)">
              <q-item-section>
                <q-item-label class="font-1r tw-five text-ir-deep">{{ hs.products[k].name }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <div v-html="html"></div>
<!--        <md-preview style="width: 800px; max-width: 100%;" v-else :model-value="mdBody"></md-preview>-->
      </q-tab-panel>

      <q-tab-panel class="_panel" name="info">
        <div class="q-py-lg row justify-center items-start">
          <div class="col-12 col-md-6 q-py-md pw2" v-if="hs.video">
            <div class="row justify-center">
              <div class="__vc">
                <div>
                  <q-video ratio="1.777" :src="video.url"></q-video>
                </div>
                <div>
                  From {{hs.name}}
                </div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 q-py-md pw2" v-if="hs.cc_video">
            <div class="row justify-center">
              <div class="__vc">
                <div>
                  <q-video ratio="1.777" :src="cc_video.url"></q-video>
                </div>
                <div class="row justify-center items-center">
                  <div class="q-mr-sm">From</div>
                  <q-img class="h20 w20" :src="icon"></q-img>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="_fw q-py-lg">
          <div class="q-pa-sm font-1-1-4r tw-five">Documents</div>

          <q-chip v-if="hasGuidelines" color="ir-bg2" clickable @click="setTab('docs')">
            <q-icon name="mdi-file" color="primary"></q-icon>
            <span class="q-ml-sm tw-five">Explore Membership Guidelines</span>
          </q-chip>
          <q-list separator>
            <q-item v-for="(doc, i) in Object.keys(byId.byUpload).map(a => byId.byUpload[a])"
                    :key="`doc-${i}`">
              <q-item-section avatar>
                <file-type-handler
                    height="85px"
                    width="65px"
                    :file="doc"
                    :url="doc.url"
                ></file-type-handler>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ doc.info.name || 'Untitled' }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn dense flat icon="mdi-open-in-new" @click="openFile(doc, $router)"></q-btn>
              </q-item-section>
            </q-item>
          </q-list>

        </div>

        <div class="_fw q-py-lg">
          <div class="q-pa-sm font-1-1-4r tw-five">Financial Historical Data</div>
          <div class="__table">
            <table>
              <tbody>
              <tr>
                <td class="__yr">Year</td>
              </tr>
              <template v-for="(cat, i) in Object.keys(headerCategories)" :key="`cat-init-${i}`">

                <tr class="__cat">
                  <td>{{ cat }}</td>
                </tr>
                <tr :class="`__data ${idx % 2 === 0 ? '__hgh' : ''}`" v-for="(sub, idx) in headerCategories[cat]"
                    :key="`init-${cat}-${idx}`">
                  <td class="__ind">{{ sub }}</td>
                </tr>
              </template>
              </tbody>
            </table>
            <div v-for="(row, inDex) in table" :key="`row-${inDex}`">
              <table>
                <tbody>
                <tr>
                  <td class="__yr">{{ row[0] }}</td>
                </tr>
                <template v-for="(cat, i) in Object.keys(headerCategories)" :key="`cat-${inDex}-${i}`">

                  <tr class="__cat">
                    <td class="__inv">foobar</td>
                  </tr>
                  <tr :class="`__data ${idx % 2 === 0 ? '__hgh' : ''}`" v-for="(sub, idx) in headerCategories[cat]"
                      :key="`row-${cat}-${inDex}-${idx}`">
                    <td class="__data_text">{{ row[headerObj[sub]?.index] }}</td>
                  </tr>
                </template>
                </tbody>
              </table>
            </div>
          </div>
        </div>

      </q-tab-panel>
    </q-tab-panels>




  </div>
</template>

<script setup>
  import icon from 'src/assets/commoncare_icon.svg'
  import FileTypeHandler from 'components/common/uploads/file-types/fileTypeHandler.vue';
  import {idGet} from 'src/utils/id-get';
  import {useHealthShares} from 'stores/health-shares';
  import {computed, reactive, ref, watch} from 'vue';
  import {manageFindUploads, manageGetUploads} from 'components/utils/uploads/file-manager';
  import {$infoNotify, dollarString} from 'src/utils/global-methods';
  import {openFile} from 'components/common/uploads/utils/files';
  import showdown from 'showdown';
  import {useAtcStore} from 'src/stores/atc-store';

  const hsStore = useHealthShares();

  const props = defineProps({
    modelValue: { required: true }
  })

  const tab = ref('info')

  const { item: hs } = idGet({
    store: hsStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const hasGuidelines = computed(() => {
    return Object.keys(hs.value.products || {}).some(a => hs.value.products[a].guidelines?.uploadId)
  })

  const { item: logo } = manageGetUploads({ source: hs, path: 'logo' })
  const { item: video } = manageGetUploads({ source: hs, path: 'video' })
  const { item: cc_video } = manageGetUploads({ source: hs, path: 'cc_video' })

  const { byId, uploadStore } = manageFindUploads({
    sources: reactive({ data: [hs.value.files || {}] }),
    paths: Object.keys(hs.value.files || {}),
    sourceIdPath: 'uploadId'
  })

  const mdBody = ref('Loading...');
  const html = ref('Loading...');
  const product = ref('')
  const products = computed(() => hs.value.products || {});

  const setBody = async () => {
    if(product.value){
      const p = products.value[product.value] || {};
      if(!p.guidelines?.uploadId) return $infoNotify('No guidelines available for this product')
      let file = uploadStore.getFromStore(p.guidelines.uploadId).value;
      if(!file) file = await uploadStore.get(p.guidelines.uploadId);
      const res = await fetch(file.url);
      mdBody.value = await res.text();
      const coverter = new showdown.Converter();
      html.value = coverter.makeHtml(mdBody.value)
    } else if(Object.keys(hs.value.products || {}).length === 1){
      product.value = Object.keys(hs.value.products)[0]
      setBody()
    }
  }

  const setProduct = (k) => {
    product.value = k;
    setBody()
  }

  const setTab = (val) => {
    tab.value = val;
    if(val === 'docs') setBody()
    else product.value = undefined;
  }

  const headerObj = {
    'Year': { index: 0, category: 'Year' },
    'Total Revenue': { index: 1, category: 'Revenue' },
    'Bill Sharing (Expense)': { index: 2, category: 'Expenses' },
    'Admin Expense': { index: 3, category: 'Expenses' },
    'Total Expense': { index: 4, category: 'Expenses' },
    'Profit': { index: 5, category: 'Profit' },
    'Profit Margin': { index: 6, category: 'Profit' },
    'MLR %': { index: 7, category: 'Medical Loss Ratio' },
    'Admin %': { index: 8, category: 'Admin %' },
    'Net Assets (Start)': { index: 9, category: 'Assets' },
    'Net Assets (End)': { index: 10, category: 'Assets' },
    'Asset Growth': { index: 11, category: 'Assets' },
    'Cash on Hand (End)': { index: 12, category: 'Cash' },
    '% of Assets as Cash': { index: 13, category: 'Cash' },
    'Assets as % of Expense': { index: 14, category: 'Cash' },
    'Cash as % of Expense': { index: 15, category: 'Cash' },
    'Assets as % of Medical Bills': { index: 16, category: 'Cash' },
    'Cash as % of Medical Bills': { index: 17, category: 'Cash' },
    'Highest Paid Executive': { index: 18, category: 'Executive' }
  }

  const headers = [
    'Year',
    'Total Revenue',
    'Bill Sharing (Expense)',
    'Admin Expense',
    'Total Expense',
    'Profit',
    'Profit Margin',
    'MLR %',
    'Admin %',
    'Net Assets (Start)',
    'Net Assets (End)',
    'Asset Growth',
    'Cash on Hand (End)',
    '% of Assets as Cash',
    'Assets as % of Expense',
    'Cash as % of Expense',
    'Assets as % of Medical Bills',
    'Cash as % of Medical Bills',
    'Highest Paid Executive'
  ];

  const headerCategories = {
    'Revenue': ['Total Revenue'],
    'Expenses': ['Bill Sharing (Expense)', 'Admin Expense', 'Total Expense'],
    'Profit': ['Profit', 'Profit Margin'],
    'Medical Loss Ratio': ['MLR %'],
    'Admin %': ['Admin %'],
    'Assets': ['Net Assets (Start)', 'Net Assets (End)', 'Asset Growth'],
    'Cash': ['Cash on Hand (End)', '% of Assets as Cash', 'Assets as % of Expense', 'Cash as % of Expense', 'Assets as % of Medical Bills', 'Cash as % of Medical Bills'],
    'Executive': ['Highest Paid Executive']
  }

  const table = ref([])
  const setTable = (financials) => {
    const rows = []
    const format = (num, prefix = '') => dollarString(num, prefix, 0);
    Object.keys(financials || {}).sort((a, b) => Number(b) - Number(a)).forEach(year => {
      const f = financials[year];
      const totalRevenue = f.total_revenue || 0;
      const sharingExpense = f.sharing_expense || 0;
      const adminExpense = f.admin_expense || 0;
      const totalExpense = sharingExpense + adminExpense;
      const profit = totalRevenue - totalExpense;

      const row = [
        year,
        format(totalRevenue, '$'),
        format(sharingExpense, '$'),
        format(adminExpense, '$'),
        format(totalExpense, '$'),
        format(profit, '$'),
        format(totalRevenue ? (profit / totalRevenue) * 100 : 0) + '%',
        format(totalRevenue ? (sharingExpense / totalRevenue) * 100 : 0) + '%',
        format(totalRevenue ? (adminExpense / totalRevenue) * 100 : 0) + '%',
        format(f.net_assets_start || 0, '$'),
        format(f.net_assets || 0, '$'),
        format((f.net_assets || 0) - (f.net_assets_start || 0), '$'),
        format(f.cash_on_hand || 0, '$'),
        format((f.net_assets || 0) ? (f.cash_on_hand || 0) / f.net_assets * 100 : 0) + '%',
        format(totalExpense ? (f.net_assets || 0) / totalExpense * 100 : 0) + '%',
        format(totalExpense ? (f.cash_on_hand || 0) / totalExpense * 100 : 0) + '%',
        format(sharingExpense ? (f.net_assets || 0) / sharingExpense * 100 : 0) + '%',
        format(sharingExpense ? (f.cash_on_hand || 0) / sharingExpense * 100 : 0) + '%',
        format(f.highest_paid_executive || 0, '$')
      ];
      rows.push(row)
    });
    return rows;
  }

  watch(hs, (nv, ov) => {
    if (nv?._id && nv._id !== ov?._id) {
      table.value = setTable(nv.financials);
    }
  }, { immediate: true })


</script>

<style lang="scss" scoped>
  .__vc {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 600px;
    max-width: 100%;

    > div {
      &:first-child {
        width: 600px;
        max-width: 100%;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 -2px 6px var(--ir-light);
      }
      &:last-child {
        text-align: center;
        padding: 10px;
        font-size: 1rem;
        font-weight: 600;
        color: var(--ir-deep);
      }
    }

  }

  .__table {
    width: 100%;
    overflow-x: scroll;
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;

    table {
      width: 100%;
      border-collapse: collapse;
      font-family: var(--alt-font);

      tr {
        th {
          padding: 4px 8px;
          font-weight: 600;
          color: var(--ir-mid);
          text-align: left;
          font-size: .8rem;
        }

        td {
          font-size: .9rem;
          padding: 4px 8px;
          //border-bottom: solid .2px var(--ir-light);
        }

      }

      .__yr {
        font-weight: 600;
        color: var(--q-a10);
      }

      .__cat {
        td {
          font-size: .8rem;
          font-weight: 600;
          color: var(--q-a4);
          background: var(--q-a1);
          font-family: var(--main-font);
          padding: 2px 8px;

        }
      }

      .__ind, .__data_text {
        font-size: .8rem;
        font-weight: 500;
        color: var(--ir-deep);

      }

      .__ind {
        padding-left: 15px
      }

      .__data {
        td {
          background: var(--q-a0);
        }
      }

      .__hgh {
        td {
          background: var(--ir-bg) !important;
        }

      }
    }
  }

  .__inv {
    color: transparent !important;
  }
</style>
