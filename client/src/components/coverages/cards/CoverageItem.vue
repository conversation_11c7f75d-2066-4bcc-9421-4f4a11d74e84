<template>
  <q-item
      @click="$emit('update:model-value', cvg)"
      v-bind="{
    ...$attrs
      }"
  >
    <slot name="avatar">
      <q-item-section avatar>
        <q-avatar size="20px" :color="typeColors[cvg?.type]"></q-avatar>
      </q-item-section>
    </slot>
    <q-item-section>
      <q-item-label class="tw-six">{{ cvg?.name }}</q-item-label>
      <slot name="caption" v-bind="{ totalRate, enrolled }">
        <q-item-label class="font-7-8r text-grey-9" v-if="enrollment">
          {{ enrolled?.length || 0 }} Covered
        </q-item-label>
      </slot>
    </q-item-section>
    <slot name="side" v-bind="{ totalRate, enrolled }">
      <q-item-section side v-if="totalRate">
        <span class="text-primary tw-six">{{ dollarString(totalRate, '$', 0) }}</span>
      </q-item-section>
    </slot>
  </q-item>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useCoverages} from 'stores/coverages';
  import {typeColors} from 'components/coverages/utils/types';
  import {dollarString} from 'src/utils/global-methods';
  import {coverageCost} from 'components/coverages/utils/display';
  import {useAtcStore} from 'src/stores/atc-store';

  const cStore = useCoverages();

  const props = defineProps({
    modelValue: { required: true },
    enrollment: { required: false }
  })

  const { item: cvg } = idGet({
    value: computed(() => props.modelValue),
    store: cStore
  ,
    useAtcStore
  })

  const { totalRate, enrolled } = coverageCost(cvg, computed(() => props.enrollment || {}))

</script>

<style lang="scss" scoped>

</style>
