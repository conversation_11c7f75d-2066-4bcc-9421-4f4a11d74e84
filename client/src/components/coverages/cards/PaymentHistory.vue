<template>
  <div class="_fw">
    <table class="__history q-mb-md">
      <tr>
        <th></th>
        <th>Amount</th>
        <th>Paid</th>
        <th>Pending</th>
      </tr>
      <template v-if="selected?.deductible?.type === 'annual'">
        <tr>
          <td class="text-primary">Indv. Ded.</td>
          <td>{{ dollarString(selected?.deductible?.single, '$', 0) }}</td>
          <td>{{ dollarString((paid.single.paid?.ded || 0) / 100, '$', 0) }}</td>
          <td>{{ dollarString((paid.single.pending?.ded || 0) / 100, '$', 0) }}</td>
        </tr>
        <tr>
          <td class="text-primary">Family Ded.</td>
          <td>{{ dollarString(selected?.deductible?.family, '$', 0) }}</td>
          <td>{{ dollarString((paid.family.paid?.ded || 0) / 100, '$', 0) }}</td>
          <td>{{ dollarString((paid.family.pending?.ded || 0) / 100, '$', 0) }}</td>
        </tr>

      </template>
      <template v-else>
        <tr>
          <td class="text-p6">Event</td>
          <td>{{ dollarString(selected?.deductible?.single, '$', 0) }}</td>
          <td>{{ dollarString((care?.paid?.amount || 0) / 100, '$', 2) }}</td>
          <td>{{ dollarString((care?.pending?.amount || 0) / 100, '$', 2) }}</td>
        </tr>
      </template>
      <tr>
        <td class="text-accent">Indv. Max OOP</td>
        <td>{{ dollarString(selected?.moop?.single, '$', 0) }}</td>
        <td>{{ dollarString((paid.single.paid?.oop || 0) / 100, '$', 0) }}</td>
        <td>{{ dollarString((paid.single.pending?.oop || 0) / 100, '$', 0) }}</td>
      </tr>
      <tr>
        <td class="text-accent">Family Max OOP</td>
        <td>{{ dollarString(selected?.moop?.family, '$', 0) }}</td>
        <td>{{ dollarString((paid.family.paid?.oop || 0) / 100, '$', 0) }}</td>
        <td>{{ dollarString((paid.family.pending?.oop || 0) / 100, '$', 0) }}</td>
      </tr>
    </table>
  </div>
</template>

<script setup>
  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useVisits} from 'stores/visits';
  import {dollarString} from 'src/utils/global-methods';
  import {useCares} from 'stores/cares';
  import {useAtcStore} from 'src/stores/atc-store';

  const visitStore = useVisits();
  const careStore = useCares();

  const props = defineProps({
    claim: { required: false },
    visit: { required: true },
    coverage: { required: true },
    enrollment: { required: true },
    paid: { required: true },
    selected: { required: false },
  })

  const {item:fullVisit } = idGet({
    store: visitStore,
    value: computed(() => props.visit)
  ,
    useAtcStore
  })
  const { item:care } = idGet({
    store: careStore,
    value: computed(() => fullVisit.value?.care)
  ,
    useAtcStore
  })

</script>

<style lang="scss" scoped>
  .__history {
    width: 100%;
    border-collapse: collapse;

    tr {
      td:first-child {
        font-size: .75rem;
        font-weight: bold;
        //color: #999;
      }
    }

    tr:nth-child(even) {
      td {
        background: #f5f5f5;
      }
    }

    th {
      font-size: .75rem;
      font-weight: bold;
      color: #999;
    }

    th, td {
      text-align: left;
      padding: 5px 8px;
      border-bottom: solid .3px #999;
    }

    tr:last-child {
      td {
        border-bottom: none;
      }
    }
  }
</style>
