import {computed, ComputedRef, ref, Ref} from 'vue';
import {usePpls} from 'stores/ppls.js';
import {HFind} from 'src/utils/hFind.js';
import {dateDiff} from 'src/utils/date-utils.js';
import {idGet} from 'src/utils/id-get.js';
import {useHouseholds} from 'stores/households.js';
import {useEnrollments} from 'stores/enrollments.js';
import {useAtcStore} from 'src/stores/atc-store';

type Options = {
    enrollment: Ref<any>|ComputedRef<any>,
}
export const enrollHousehold = ({ enrollment }:Options) => {
    const pplsStore:any = usePpls();
    const hhStore:any = useHouseholds();
    const eStore:any = useEnrollments();

    const { item:person } = idGet({
        store: pplsStore,
        value: computed(() => enrollment.value?.person),
        useAtcStore
    })
    const { item:household } = idGet({
        store: hhStore,
        value: computed(() => person.value?.household),
        useAtcStore
    })

    const { h$:ppls } = HFind({
        store: pplsStore,
        limit: ref(25),
        pause: computed(() => !household.value?.members),
        params: computed(() => {
            return {
                query: {
                    _id: { $in: Object.keys(household.value?.members || {}) }
                }
            }
        })
    })

    const combineMember = (p:any) => {
        const obj = (household.value.members || {})[p._id];
        return { ...obj, ...p }
    };
    const getEnrolledProperties = (p:any) => {
        const d = new Date();
        const { lastName, firstName, ssn, dob, gender, relation, dependent, monthsSinceSmoked, disabled, annualIncome, incarcerated } = p;
        const zip = p.address?.postal || person.value.address?.postal || undefined;
        const obj = { age: 0, zip, relation, lastName, firstName, ssn, dob, gender, dependent, monthsSinceSmoked, disabled, annualIncome, incarcerated }
        if (p.dob) obj.age = Math.floor(dateDiff(d, p.dob, 'months')/12);
        return obj;
    }

    const typeSet = ref('');
    const setEnrolled = async (patch?:boolean) => {
        let enrolled:any = {};
        let run;
        if(enrollment.value?.status !== 'complete') {
            if(typeSet.value === 'single'){
                run = true;
                enrolled = { [person.value._id]: { ...getEnrolledProperties(combineMember(person.value)), relation: 'self' } }

            } else {
                const er = enrollment.value.enrolled || {};
                for (const p of [...ppls.data, {...person.value, relation: 'self'}].map(a => combineMember(a))) {
                    if(!er[p._id]) run = true;
                    enrolled[p._id] = getEnrolledProperties(p);
                }
            }
            if(patch || run) {
                const { _id } = enrollment.value;
                eStore.patchInStore(_id, { enrolled });
                await eStore.patch(_id, { enrolled })
            } return enrolled;
        }
        return undefined;
    }

    const patchType = async (tries = 0) => {
        console.log('patch type', typeSet.value, household.value._id)
        if(household.value?._id){
            const patchObj:any = { type: typeSet.value }
            const enrolled = await setEnrolled();
            if(enrolled) patchObj.enrolled = enrolled;
            await eStore.patch(enrollment.value._id, patchObj);
        } else if(tries < 10) setTimeout(() => patchType(tries+1), 500)
    }

    const confirmResetType = ref(false);
    const pendingType = ref('family');
    const setType = (val:any, confirm:any) => {
        pendingType.value = val;
        if(!confirm && val === 'single' && Object.keys(household.value.members || {}).length > 1) confirmResetType.value = true;
        else {
            confirmResetType.value = false;
            typeSet.value = val;
            if (val === 'single' && enrollment.value.type !== 'single') setEnrolled(true)
            if (val && val !== enrollment.value?.type) {
                eStore.patchInStore(enrollment.value._id, { type: val });
                setTimeout(() => {
                    patchType(0);
                }, 500)
            }
        }
    }

    const updateHH = () => {
        setEnrolled(true)
    }

    const hhLoaded = (tries = 0) => {
        if(enrollment.value?._id) {
            setTimeout(() => {
                setEnrolled()
            }, 1000)
        }
        else if(tries < 5) setTimeout(() => {
            hhLoaded(tries+1)
        }, 1000)
    }

    return {
        ppls,
        hhLoaded,
        updateHH,
        setType,
        confirmResetType,
        patchType,
        typeSet,
        setEnrolled,
        household,
        person
    }
}
