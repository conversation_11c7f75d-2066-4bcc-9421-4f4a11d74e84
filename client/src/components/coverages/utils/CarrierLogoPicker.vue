<template>
  <div>
    <q-img v-if="modelValue?.url" class="w40 h40" :src="modelValue.url"></q-img>
    <q-icon v-else size="40px" color="ir-grey-5" name="mdi-upload"></q-icon>

    <q-popup-proxy :breakpoint="50000" :model-value="pp" @update:model-value="togglePp">
      <div class="w650 mw100 q-py-lg q-px-md bg-white">
        <div class="row">
          <q-tabs align="left" no-caps v-model="tab">
            <q-tab label="Lookup" name="find"></q-tab>
            <q-tab label="Add" name="add"></q-tab>
          </q-tabs>
        </div>
        <q-tab-panels class="_panel" v-model="tab" animated>
          <q-tab-panel class="_panel" name="find">

            <q-input class="q-my-xs" dense filled v-model="search.text">
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
            </q-input>

            <div class="row q-pt-sm items-center">
              <div v-for="(c, i) in u$.data" :key="`c-${i}`" class="q-pa-sm cursor-pointer" @click="emit('update:model-value', getFileSchema(c)), pp = false">
                <q-img class="h40 w40" :src="c.url"></q-img>
              </div>
            </div>

            <pagination-row v-bind="{ pAttrs: { size: 'sm' }, pageRecordCount, pagination, limit, h$:j$ }"></pagination-row>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="add">

            <div class="row justify-center items-center q-py-lg mnh200">
              <image-form :model-value="modelValue" @update:model-value="emitUp" :use-atc-store="useAtcStore"></image-form>
            </div>

          </q-tab-panel>
        </q-tab-panels>


      </div>
    </q-popup-proxy>
  </div>
</template>

<script setup>
  import ImageForm from 'components/common/uploads/images/ImageForm.vue';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';

  import {computed, ref} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {useAtcStore} from 'src/stores/atc-store';
  import {_flatten} from 'symbol-syntax-utils';
  import {useUploads} from 'stores/uploads';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {getFileSchema} from 'components/common/uploads/services';

  const junkStore = useJunkDrawers();
  const uploadStore = useUploads();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true }
  })

  const pp = ref(false)
  const tab = ref('find')

  const { search, searchQ } = HQuery({ keys: ['itemName'] })

  const limit = ref(10);
  const { h$:j$, pageRecordCount, pagination } = HFind({
    store: junkStore,
    limit,
    pause: computed(() => !pp.value),
    params: computed(() => {
      return {
        query: {
          drawer: 'carrier_logos',
          ...searchQ.value
        }
      }
    })
  })

  const uploadIds = computed(() => {
    return _flatten(j$.data.map(a => Object.keys(a.data)))
  })

  const { h$:u$ } = HFind({
    store: uploadStore,
    pause: computed(() => !pp.value),
    limit: computed(() => Math.min(50, uploadIds.value.length)),
    params: computed(() => {
      return {
        query: {
          _id: { $in: uploadIds.value }
        }
      }
    })
  })


  const emitUp = (val) => {
    emit('update:model-value', val)
  }

  const togglePp = (val) => {
    pp.value = val;
  }
</script>

<style lang="scss" scoped>

</style>
