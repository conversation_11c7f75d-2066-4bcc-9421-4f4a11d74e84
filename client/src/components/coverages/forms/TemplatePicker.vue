<template>
  <template v-if="!modelValue">
    <q-chip class="ir-bg2" :clickable="!searchOn" @click="searchOn = true">
      <span>No Template - Add One</span>
      <q-btn v-if="searchOn" dense flat icon="mdi-close" @click="searchOn = false" color="red"></q-btn>
    </q-chip>
    <q-slide-transition>
      <div class="_fw" v-if="searchOn">
        <q-tab-panels class="_panel" :model-value="!!publicCheck">
          <q-tab-panel class="_panel" :name="false">
            <q-input class="w500 mw100 q-py-sm" dense filled v-model="search.text"
                     placeholder="Search for template...">
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
            </q-input>
            <q-list separator>
              <q-item v-for="(item, i) in pc$.data" :key="`pc-${i}`" clickable @click="publicCheck = item">
                <q-item-section avatar v-if="item.carrierLogo">
                  <default-avatar avatar-path="carrierLogo" :model-value="item" :use-atc-store="useAtcStore"></default-avatar>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ item.name }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-tab-panel>
          <q-tab-panel class="_panel" :name="true">
            <div class="row">
              <q-btn dense flat icon="mdi-close" color="red" @click="publicCheck = undefined"></q-btn>
            </div>
            <div class="_fw q-py-md">
              <coverage-card :model-value="publicCheck"></coverage-card>
            </div>
            <div class="row justify-end">
              <q-btn label="Track This Template" push class="_pl_btn tw-six" size="sm" no-caps
                     @click="assignTemplate(publicCheck)"></q-btn>
            </div>
          </q-tab-panel>
        </q-tab-panels>

      </div>
    </q-slide-transition>
  </template>
  <default-chip v-else hide-avatar :model-value="modelValue" :store="coverageStore" :use-atc-store="useAtcStore">
    <template v-slot:right>
      <remove-proxy-btn :label="undefined"
                    remove-label="Remove template? You will no longer track updates to the template if you do this."
                    dense flat class="q-ml-sm" icon="mdi-close" color="red"
                    @remove="removeTemplate"></remove-proxy-btn>
    </template>
  </default-chip>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import CoverageCard from 'components/coverages/cards/CoverageCard.vue';

  import {computed, ref} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {useAtcStore} from 'src/stores/atc-store';
  import {useCoverages} from 'stores/coverages';

  const coverageStore = useCoverages();

  const props = defineProps({
    modelValue: { required: true },
    coverageId: { required: true }
  })

  const searchOn = ref(false);

  const publicCheck = ref();
  const assignTemplate = (v) => {
    emit('update:model-value', v._id);
    publicCheck.value = undefined;
    coverageStore.patch(props.coverageId, { $set: { fromTemplate: v._id } });
  }
  const { search, searchQ } = HQuery({})
  const { h$: pc$ } = HFind({
    store: coverageStore,
    limit: ref(5),
    pause: computed(() => !searchOn.value),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          _id: { $ne: props.coverageId },
          public: true,
          template: true,
        }
      }
    })
  })

  const removeTemplate = () => {
    coverageStore.patch(props.coverageId, { $unset: { fromTemplate: '' } });
  }
</script>

<style lang="scss" scoped>

</style>
