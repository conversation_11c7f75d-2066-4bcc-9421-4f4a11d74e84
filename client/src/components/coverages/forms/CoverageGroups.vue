<template>
  <div class="_fw">
    <slot name="title" v-bind="{ on: groups.on, planGroupCount }"></slot>
    <div class="row items-center">
      <div>
        <q-chip color="transparent" clickable v-if="!planC?.groups?.length" class="text-italic">
          <span class="q-mr-sm">All groups are eligible</span>
          <q-icon color="primary" name="mdi-pencil"></q-icon>
        </q-chip>
        <q-chip v-else clickable>
          <span class="q-mr-sm">Add Group</span>
          <q-icon name="mdi-plus" color="primary"></q-icon>
        </q-chip>
        <q-menu>
          <div class="w400 mw100 q-pa-md bg-white">
            <q-input dense filled placeholder="Search Groups" v-model="search">
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
            </q-input>
            <div class="row items-center q-py-sm">
              <div class="tw-five alt-font text-grey-7 font-7-8r">Select Eligible Groups</div>
              <q-space></q-space>
              <q-btn flat no-caps size="sm" @click="addAll">
                <q-icon color="primary" name="mdi-plus"></q-icon>
                <span class="q-ml-xs">All</span>
              </q-btn>
            </div>
            <q-list separator>
              <q-item v-for="(group, i) in groups.off" :key="`off-${i}`" clickable @click="toggle(group)">
                <q-item-section avatar>
                  <default-avatar :model-value="group.org" :store="orgStore" :use-atc-store="useAtcStore"></default-avatar>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{group.name}}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-menu>
      </div>
      <q-chip size="sm" color="transparent" v-for="(group, i) in groups.on" :key="`group-${i}`">
        <default-avatar :store="orgStore" :model-value="group.org" :use-atc-store="useAtcStore"></default-avatar>
        <span class="q-mx-xs">{{ group.name }}</span>
        <q-btn dense flat size="xs" color="red" icon="mdi-close" @click="toggle(group, i)"></q-btn>
      </q-chip>
    </div>
  </div>
</template>

<script setup>

  import {computed, ref, watch} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useGroups} from 'stores/groups';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import {useOrgs} from 'stores/orgs';
  import {usePlans} from 'stores/plans';
  import {useAtcStore} from 'src/stores/atc-store';

  const groupStore = useGroups();
  const orgStore = useOrgs();
  const planStore = usePlans();

  const props = defineProps({
    plan: { required: true },
    coverage: { required: true }
  })

  const fullPlan = computed(() => props.plan);
  const fullC = computed(() => props.coverage);

  const planC = computed(() => (fullPlan.value?.coverages || {})[fullC.value?._id])

  const search = ref('');
  const planGroupCount = computed(() => fullPlan.value?.groups?.length || 0)
  const { h$: g$ } = HFind({
    store: groupStore,
    limit: planGroupCount,
    params: computed(() => {
      return {
        query: { _id: { $in: fullPlan.value?.groups || [] } },
      }
    })
  })
  const groupIds = ref([]);
  watch(planC, (nv) => {
    if(nv?.groups) groupIds.value = nv.groups;
  }, { immediate: true })
  const groups = computed(() => {
    const on = [];
    const off = [];
    for(let i = 0; i < g$.data.length; i++){
      const grp = g$.data[i];
      if((groupIds.value || []).includes(grp._id)) on.push(grp)
      else off.push(grp)
    }
    return { on, off: off.filter(a => a.name.toLowerCase().includes(search.value.toLowerCase())) }
  })

  const patchObj = ref({});
  const to = ref()
  const maybeSave = () => {
    if (to.value) clearTimeout(to.value);
    to.value = setTimeout(() => {
      planStore.patch(fullPlan.value._id, patchObj.value);
      patchObj.value = {};
    }, 3000)
  }
  const addAll = () => {
    groupIds.value = [];
    patchObj.value = { $unset: { [`coverages.${fullC.value._id}.groups`]: ''} }
    maybeSave();
  }
  const toggle = (g, i) => {
    if(typeof i === 'number') groupIds.value.splice(i, 1);
    else {
      groupIds.value.push(g._id);
      if(groupIds.value.length === fullPlan.value.groups.length) return addAll()
    }
    const $set = {
      [`coverages.${fullC.value._id}`]: {
        ...planC.value,
        groups: groupIds.value
      }
    }
    patchObj.value = { $set }
    maybeSave();
  }


</script>

<style lang="scss" scoped>

</style>
