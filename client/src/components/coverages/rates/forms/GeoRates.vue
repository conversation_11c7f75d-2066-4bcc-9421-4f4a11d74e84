<template>
  <div class="_fw">
    <div class="row items-center">
      <q-chip clickable dense color="transparent" label="Coverage" @click="emit('home')"></q-chip>
      <q-icon color="primary" name="mdi-chevron-right" size="12px"></q-icon>
      <q-chip :class="`${!activeState ? 'tw-six' : ''}`" dense color="transparent" label="Location Rates" clickable @click="activeState = undefined"></q-chip>
      <template v-if="activeState">
        <q-icon color="primary" name="mdi-chevron-right" size="12px"></q-icon>
        <q-chip class="tw-six" dense color="transparent" :label="activeState.state"></q-chip>
      </template>
    </div>

    <q-separator class="q-my-sm"></q-separator>

    <q-tab-panels class="_panel" :model-value="!!activeState">
      <q-tab-panel class="_panel" :name="false">
        <q-list separator dense>
          <add-item label="New State Rate Schedule">
            <template v-slot:menu>
              <q-menu>
                <div v-if="!stateList.length" class="q-pa-lg text-italic">No more states available</div>
                <q-list dense separator>
                  <q-item clickable @click="addState(st.value)" v-for="(st, i) in stateList" :key="`so-${i}`">
                    <q-item-section>
                      <q-item-label>{{st.text}}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </template>
          </add-item>
          <q-item clickable v-for="(st, i) in r$.data" :key="`st-${i}`" @click="activeState = st">
            <q-item-section>
              <q-item-label class="tw-six text-p6">{{getStateName(st.state)}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-tab-panel>
      <q-tab-panel class="_panel" :name="true">
        <div class="q-pa-sm tw-six text-grey-8">{{getStateName(activeState.state)}}</div>
        <rate-form :coverage="fullC" @update:model-value="activeState = undefined" :model-value="activeState"></rate-form>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import AddItem from 'components/common/buttons/AddItem.vue';
  import RateForm from 'components/coverages/rates/forms/RateForm.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useCoverages} from 'stores/coverages';
  import {useRates} from 'stores/rates';
  import {HFind} from 'src/utils/hFind';
  import {getStateName} from 'src/components/common/geo/data/states';
  import {statePicker} from 'components/common/geo/data/state-picker';

  const cStore = useCoverages();
  const rateStore = useRates();

  const emit = defineEmits(['update:model-value', 'home'])
  const props = defineProps({
    coverage: { required: true }
  })

  const { item:fullC } = idGet({
    store: cStore,
    value: computed(() => props.coverage)
  ,
    useAtcStore
  })

  const activeState = ref(undefined);
  const { useList, searchFilter } = statePicker();

  const { h$:r$ } = HFind({
    store: rateStore,
    pause: computed(() => !fullC.value),
    limit: ref(55),
    params: computed(() => {
      return {
        query: {
          coverage: fullC.value?._id
        }
      }
    })
  })

  const stateList = computed(() => {
    const map = (r$.data || []).map(a => a.state);
    return useList.value?.filter(a => !map.includes(a.value))
  })

  const addState = (st) => {
    activeState.value = { state: st };
  }
</script>

<style lang="scss" scoped>

</style>
