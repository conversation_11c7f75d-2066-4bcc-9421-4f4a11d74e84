<template>
  <div class="_fw">

    <div class="q-py-md _fw">
      <div class="q-py-sm _fw">
        <q-btn flat color="ir-bg" glossy no-caps>
          <span class="text-ir-deep tw-six">Add New</span>
          <q-icon class="q-ml-sm" color="primary" name="mdi-plus" @click="dialog = true"></q-icon>
        </q-btn>
      </div>
      <div class="row q-py-sm" v-if="canEdit">
        <q-tabs align="left" v-model="tab" no-caps class="tw-six">
          <q-tab name="current" label="Current" icon-right="mdi-list"></q-tab>
          <q-tab name="explore" label="Explore" icon-right="mdi-magnify"></q-tab>
          <!--          <q-tab name="market" label="Market" icon-right="mdi-web"></q-tab>-->
        </q-tabs>
      </div>

      <q-input filled dense class="w800 mw100" v-model="search.text" placeholder="Search coverages...">
        <template v-slot:prepend>
          <q-icon name="mdi-magnify"></q-icon>
        </template>
      </q-input>

      <div class="row items-center q-py-md" v-if="tab === 'current'">
        <q-icon size="20px" color="accent" name="mdi-filter" class="q-mr-sm"></q-icon>
        <type-picker
            v-model="coverageType"
            v-bind="{ removable: true }"
            @remove="coverageType = undefined"></type-picker>
      </div>
      <div class="row items-center q-py-md" v-else>
        <type-picker v-model="coverageType" v-bind="{ removable: true }"
                     @remove="coverageType = undefined"></type-picker>
        <q-radio v-model="publicSearch" :val="false" label="Your Coverages"></q-radio>
        <q-radio v-model="publicSearch" :val="true" label="Public Coverages"></q-radio>

      </div>

      <div class="q-py-sm row items-center">
        <div class="font-7-8r text-ir-md tw-six">Covered:</div>
        <q-chip square dense clickable @click="covered = 'group'" label="Group" color="ir-bg"
                :class="`${covered === 'group' ? 'tw-six text-accent' : ''}`"></q-chip>
        <div>|</div>
        <q-chip square dense clickable @click="covered = 'individual'" label="Individual" color="ir-bg"
                :class="`${covered === 'individual' ? 'tw-six text-accent' : ''}`"></q-chip>

      </div>

      <div class="q-pa-sm font-1r mw800" v-if="covered === 'group'">
        These coverages are group contracts - employees make payroll elections to contribute to these and you (the group
        sponsor) facilitate payment.
      </div>

      <div class="q-pa-sm font-1r mw800" v-if="covered === 'individual'">
        These coverages are individual contracts. They are not directly sponsored by the group plan, but employees may
        elect to "shop" for them through the group plan and pay directly through payroll deductions.
      </div>

      <div v-if="!h$.total" class="q-pa-lg text-italic font-1r">No Coverages Selected</div>

      <q-scroll-area id="I_S" @scroll="senseScrollLoad">

        <div class="row q-pa-sm">
          <div class="col-12 col-md-6 col-lg-4 q-pa-sm" v-for="(cov, i) in h$.data || []" :key="`cov-${i}`">
            <div :id="`i_s-${i}`"
                 :class="`__c _hov ${selectable ? 'cursor-pointer' : ''}`"
                 @click="select(cov)">
              <coverage-card hide-for :plan="plan" :model-value="cov"></coverage-card>
              <div class="t-r">
                <q-btn dense flat icon="mdi-menu">
                  <q-menu>
                    <div class="w300 br10 bg-white q-pa-md">
                      <q-list separator>
                        <q-item clickable @click="openCoverage(cov)">
                          <q-item-section>
                            <q-item-label>View</q-item-label>
                          </q-item-section>
                          <q-item-section side>
                            <q-icon color="accent" name="mdi-open-in-new"></q-icon>
                          </q-item-section>
                        </q-item>
                      </q-list>
                      <q-list separator v-if="canEdit && (tab === 'current' || !publicSearch)">
                        <q-item clickable @click="editing = cov">
                          <q-item-section>
                            <q-item-label>Edit</q-item-label>
                          </q-item-section>
                          <q-item-section side>
                            <q-icon name="mdi-pencil"></q-icon>
                          </q-item-section>
                        </q-item>
                        <q-item>
                          <q-item-section label>
                            Remove
                          </q-item-section>
                          <q-item-section side>
                            <remove-proxy-btn v-if="tab === 'current'" name="this coverage from your plan" @remove="removeCoverage(cov)">
                            </remove-proxy-btn>
                            <remove-proxy-btn v-if="tab === 'explore'" remove-label="Delete this coverage?" @remove="deleteCoverage(cov)">
                            </remove-proxy-btn>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </div>
                  </q-menu>
                </q-btn>
              </div>

                <div class="__bottom">
                  <q-separator class="q-my-sm"></q-separator>


                  <div class="row justify-end q-py-sm" v-if="tab === 'explore'">
                    <q-btn push color="accent" no-caps class="tw-six" label="Add To Plan" glossy icon="mdi-plus"
                           @click="addCoverage(cov)">
                      <q-spinner class="q-ml-sm" color="white" v-if="copying === cov._id"></q-spinner>
                    </q-btn>
                  </div>


                  <template v-else>
                    <q-chip color="grey-1" clickable>
                      <span>Employer Contribution:&nbsp;</span>
                      <span class="alt-font">{{
                          dollarString(getErContribution({
                            covId: cov._id,
                            plan
                          }), getErContributionSymbols(cov._id, plan).prefix, 0)
                        }}{{ getErContributionSymbols(cov._id, plan).suffix }}</span>
                      <q-popup-proxy>
                        <div class="w400 mw100 bg-white q-pa-md">
                          <plan-coverage-contributions :coverage-id="cov._id"
                                                       :plan="plan"></plan-coverage-contributions>
                        </div>
                      </q-popup-proxy>
                    </q-chip>
                    <q-chip color="grey-1" clickable>
                      <q-icon class="q-mr-sm" :color="coverages[cov._id]?.groups?.length ? typeColors[cov.type] : ''"
                              name="mdi-account-group"></q-icon>
                      <span
                          v-if="coverages[cov._id]?.groups?.length">{{
                          $possiblyPlural('Group', coverages[cov._id].groups)
                        }} Eligible</span>
                      <span v-else>No Group Restrictions</span>
                      <q-popup-proxy breakpoint="50000">
                        <div class="w400 mw100 bg-white q-pa-md">
                          <div class="q-pa-sm tw-six font-7-8r">Eligible Groups</div>

                          <coverage-groups :coverage="cov" :plan="plan"></coverage-groups>
                        </div>
                      </q-popup-proxy>
                    </q-chip>
                  </template>
              </div>
            </div>
          </div>
        </div>
      </q-scroll-area>

      <pagination-row v-bind="{ limit, pageRecordCount, pagination, h$ }"></pagination-row>

    </div>


    <common-dialog v-if="canEdit" setting="right" @update:model-value="closeDialog" :model-value="dialog || !!editing">
      <div class="_fw q-pa-md">
        <coverage-form
            :plan="plan"
            @update:model-value="newCoverage"
            :model-value="editing"
            :added-by="plan?.org"
        ></coverage-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CoverageCard from 'components/coverages/cards/CoverageCard.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import CoverageForm from 'components/coverages/forms/CoverageForm.vue';
  import TypePicker from 'components/coverages/cards/TypePicker.vue';
  import CoverageGroups from 'components/coverages/forms/CoverageGroups.vue';
  import PlanCoverageContributions from 'components/coverages/forms/PlanCoverageContributions.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {_pick} from 'symbol-syntax-utils';
  import {$errNotify, $possiblyPlural, dollarString} from 'src/utils/global-methods';
  import { pickCoverageFieldsForCopy } from 'components/coverages/utils/copy';
  import {HFind, hInfiniteScroll} from 'src/utils/hFind';
  import {typeColors} from 'components/coverages/utils/types';
  import {HQuery} from 'src/utils/hQuery';
  import {useCoverages} from 'stores/coverages';
  import {usePlans} from 'stores/plans';
  import {useRouter, useRoute} from 'vue-router';
  import {getErContribution, getErContributionSymbols} from 'src/components/enrollments/utils/contributions';
  import {LocalStorage} from 'symbol-auth-client';
  import {useEnvStore} from 'stores/env';

  const router = useRouter();
  const route = useRoute();
  const planStore = usePlans();
  const coverageStore = useCoverages();
  const envStore = useEnvStore();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false },
    canEdit: { default: true },
    selectable: Boolean
  })

  const tab = ref('current');
  const editing = ref(undefined);
  const dialog = ref(false);
  const closeDialog = (val) => {
    if (!val) {
      dialog.value = false;
      editing.value = undefined;
    }
  }

  const { item: plan } = idGet({
    value: computed(() => props.modelValue || envStore.getPlanId),
    store: planStore,
    routeParamsPath: 'planId'
  ,
    useAtcStore
  })
  const coverages = computed(() => plan.value?.coverages || {});

  const select = (val) => {
    emit('update:model-value', val);
  }

  const coverageType = ref(undefined);
  const publicSearch = ref(true);
  const copying = ref(undefined);
  const { search, searchQ } = HQuery({ or: true })
  const covered = ref('group');

  const query = computed(() => {
    const q = { ...searchQ.value, covered: covered.value, $sort: { createdAt: -1 } };
    const coverages = Object.keys(plan.value?.coverages || {})
    if (coverageType.value) q.type = coverageType.value;
    if (tab.value !== 'explore') {
      q._id = { $in: coverages }
    }
    else {
      if (coverages?.length) q._id = { $nin: coverages }
      if (publicSearch.value) q.public = true;
      else q.org = plan.value.org
    }
    return q;
  })

  const limit = ref(25);
  const { h$, pagination, pageRecordCount } = HFind({
    store: coverageStore,
    paginateApi: 'server',
    limit,
    params: computed(() => {
      return {
        query: query.value
      }
    })
  })

  const addCoverage = async (val) => {
    if (val.org === plan.value.org || val.public) {
      if (val.covered === 'group') {
        copying.value = val._id;
        const rest = _pick(val, pickCoverageFieldsForCopy);
        rest.org = plan.value.org;
        rest.fromTemplate = val._id;
        const newCoverage = val.template ? await coverageStore.create(rest)
            .catch(err => $errNotify(`Error copying template: ${err.message}`)) : val;
        await planStore.patch(plan.value._id, {
          $set: {
            [`coverages.${newCoverage._id}`]: {
              id: newCoverage._id,
              type: newCoverage.type
            }
          }
        }, { special_change: [] })
        copying.value = undefined;
      } else {
        await planStore.patch(plan.value._id, {
          $set: {
            [`coverages.${val._id}`]: {
              id: val._id,
              type: val.type
            }
          }
        })
      }
    }
  }
  const newCoverage = async (val) => {
    editing.value = undefined;
    dialog.value = false;
    await planStore.patch(plan.value._id, { $set: { [`coverages.${val._id}`]: { id: newCoverage._id } } })

  };

  const { senseScrollLoad } = hInfiniteScroll({ h$, loadNum: 10 })

  const openCoverage = (val) => {
    const { href } = router.resolve({
      name: 'coverage-admin',
      params: { planId: route.params.planId || envStore.getPlanId, coverageId: val._id }
    });
    window.open(href, '_blank');
  }
  const removeCoverage = (val) => {
    planStore.patch(plan.value._id, { $unset: { [`coverages.${val._id}`]: '' } })
  }
  const deleteCoverage = (val) => {
    planStore.patch(plan.value._id, { $unset: { [`coverages.${val._id}`]: '' } })
    coverageStore.remove(val._id)
  }
</script>

<style lang="scss" scoped>

  .__c {
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px var(--ir-light);
    background: white;
    position: relative;
    height: 100%;
    display: grid;
    grid-template-rows: auto 1fr;
    align-items: end;
  }

  #I_S {
    height: 800px;
  }

  .__fl {
    font-size: .8rem;
    font-weight: 600;
    color: #999;
  }

  .__bottom {
    display: flex;
    align-items: end;
    justify-content: end;
  }
</style>
