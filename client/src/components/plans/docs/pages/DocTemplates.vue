<template>
  <q-page class="_bg_ow">
    <div class="row justify-center">
      <div class="_cent bg-white pa3">
        <div class="row items-center q-py-xs">
          <q-chip color="white" class="tw-six text-p7 font-1r" label="Doc Templates" clickable
                  @click="$router.push({ name: 'doc-templates'})"></q-chip>
          <template v-if="docId">
            <q-icon color="p7" name="mdi-chevron-right"></q-icon>
            <q-chip :label="doc?.name" color="white" class="tw-six text-p7"></q-chip>
          </template>
        </div>
        <q-tab-panels class="_panel" :model-value="!!docId">
          <q-tab-panel class="_panel" :name="false">
            <docs-search @update:model-value="setActive"></docs-search>
          </q-tab-panel>
          <q-tab-panel class="_panel" :name="true">
            <div class="_fw">
              <doc-display :model-value="doc"></doc-display>
            </div>
          </q-tab-panel>
        </q-tab-panels>
      </div>
    </div>


  </q-page>
</template>

<script setup>

  import DocsSearch from 'src/components/plans/docs/lists/DocsSearch.vue';
  import {computed, ref, watch} from 'vue';
  import {useRoute, useRouter} from 'vue-router';
  import DocDisplay from 'components/plans/docs/cards/DocDisplay.vue';
  import {idGet} from 'src/utils/id-get';
  import {useDocTemplates} from 'stores/doc-templates';
  import {useAtcStore} from 'src/stores/atc-store';

  const route = useRoute();
  const router = useRouter();
  const store = useDocTemplates();

  const setActive = (val) => {
    router.push({ ...route, params: { docId: val._id } })
  }

  const docId = computed(() => {
    return route.params.docId;
  })

  const { item: doc } = idGet({
    store,
    value: docId
  ,
    useAtcStore
  })

  watch(() => route.params)

</script>

<style lang="scss" scoped>

</style>
