<template>
  <plan-doc-chip v-if="docs && modelValue" removable @remove="emit('update:model-value', undefined)"></plan-doc-chip>
  <q-slide-transition>
    <q-select
        v-if="!docs || !modelValue"
        v-bind="{
          label: modelValue ? '' : 'Choose Doc Template',
          modelValue,
          options: h$.data,
      ...$attrs
      }"
    >
      <template v-slot:selected-item>
        <div></div>
      </template>
      <template v-slot:option="scope">
        <q-item clickable @click="emit('update:model-value', scope.opt)">
          <q-item-section>
            <q-item-label class="tw-six text-grey-7">{{scope.opt.name}}</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>
  </q-slide-transition>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useDocTemplates} from 'stores/doc-templates';
  import {planClasses} from 'components/plans/utils';
  import PlanDocChip from 'components/plans/docs/cards/PlanDocChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';
  const store = useDocTemplates();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    query: Object
  })

  const { item: docs } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })

  const { h$ } = HFind({
    store,
    limit: ref(10),
    params: computed(() => {
      return {
        query: props.query
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
