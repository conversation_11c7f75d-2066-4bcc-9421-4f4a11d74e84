<template>
  <div class="_fw">
    <div class="_f_g">
      <div class="_f_l _f_chip">Doc Title</div>
      <div class="q-pa-sm">
        <q-input v-model="form.name" input-class="tw-six" @update:model-value="autoSave('name', $event)"></q-input>
      </div>
      <div class="_f_l _f_chip">Sections</div>
      <div class="_fw">
        <docs-editor
            :id="form._id"
            v-model="form.sections"
            @update:model-value="autoSave('sections', $event)"
            :custom-values="customValues"
        ></docs-editor>
      </div>
    </div>
  </div>
</template>

<script setup>
  import DocsEditor from 'components/plans/docs/forms/DocsEditor.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {usePlanDocs} from 'stores/plan-docs';
  import {HForm, HSave} from 'src/utils/hForm';
  import { customValues } from '../../utils';

  const store = usePlanDocs();

  const props = defineProps({
    path: { type: String },
    modelValue: { required: false }
  })

  const { item:doc } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })

  const { form, save } = HForm({
    store,
    notify: false,
    value: doc
  })

  const { autoSave } = HSave({ form, store })
</script>

<style lang="scss" scoped>

</style>
