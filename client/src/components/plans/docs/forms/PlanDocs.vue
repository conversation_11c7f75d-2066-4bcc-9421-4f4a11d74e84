<template>
  <div class="_fw" v-if="plan">

    <div class="font-1-1-4r tw-six">Custom Plan Docs</div>
    <div class="font-1r">Review recommendations, make adjustments, generate your plan docs</div>
    <q-separator class="q-my-md"></q-separator>
    <q-stepper flat class="_panel" header-nav v-model="step" color="p7" animated>
      <q-step active-icon="mdi-playlist-check" class="_panel" name="list" title="List" icon="mdi-playlist-check"></q-step>
      <q-step class="_panel" name="view" title="View" icon="mdi-file-edit" active-icon="mdi-file-edit"></q-step>
      <q-step class="_panel" name="docs" title="Export" icon="mdi-file-pdf-box" active-icon="mdi-file-pdf-box"></q-step>
    </q-stepper>
    <q-tab-panels keep-alive animated class="_panel __up" :model-value="step">
<!--      DOCS LIST-->
      <q-tab-panel class="_panel" name="list">
        <plan-docs-form @update:path="setPath" :model-value="plan"></plan-docs-form>
      </q-tab-panel>
<!--      DOCS VIEW-->
      <q-tab-panel class="_panel" name="view">
        <doc-assign :path="path" :plan-id="plan?._id"></doc-assign>
      </q-tab-panel>
<!--      DOCS PRINT-->
      <q-tab-panel class="_panel" name="docs">
        <docs-display :plan="plan"></docs-display>
      </q-tab-panel>

    </q-tab-panels>
    <div class="row items-center">
      <q-btn v-if="idx > 0" flat icon="mdi-chevron-left" :label="labels[idx-1]" color="primary" @click="step = steps[idx - 1]" no-caps class="tw-six"></q-btn>
      <q-btn v-if="idx < 2" push color="primary" no-caps class="tw-six" :label="labels[idx+1]" icon-right="mdi-chevron-right" @click="step = steps[idx+1]"></q-btn>
    </div>

  </div>
</template>

<script setup>
  import PlanDocsForm from 'components/plans/docs/forms/PlanDocsForm.vue';
  import DocAssign from 'components/plans/docs/pages/DocAssign.vue';
  import DocsDisplay from 'components/plans/docs/cards/DocsDisplay.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {useEnvStore} from 'stores/env';

  const envStore = useEnvStore();
  const store = usePlans();
  const props = defineProps({
    modelValue: { required: true }
  })

  const step = ref('list');
  const path = ref('doc');

  const { item:plan } = idGet({
    store,
    value: computed(() => props.modelValue || envStore.getPlanId),
    routeParamsPath: 'planId'
  ,
    useAtcStore
  })

  const setPath = (val) => {
    path.value = val;
    step.value = 'view';
  }

  const steps = ref(['list', 'view', 'docs']);
  const labels = ref(['Docs List', 'Docs Editor', 'Review & Print'])
  const idx = computed(() => steps.value.indexOf(step.value));


</script>

<style lang="scss" scoped>
  .__h {
    font-size: 1.125rem;
    font-weight: 600;
    padding: 20px 0 5px 0;
  }
  .__up {
    transform: translate(0, -40px);
  }
</style>
