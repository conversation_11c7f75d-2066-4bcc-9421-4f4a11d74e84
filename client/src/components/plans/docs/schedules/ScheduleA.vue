<template>
  <div class="_fw">
    <div style="padding: 20px 0; font-size: 1.5rem; font-weight: 700; color: #6e6e6e">Schedule A</div>
    <div style="padding: 20px 0; font-size: 1rem;">
      <div style="font-weight: 600; font-size: 1.25rem">Schedule of Plan Benefits</div>
      <div>The {{ fullPlan?.name }} provides different forms of benefits which we will summarize in 3 sections:
        1. Cafeteria, 2. Reimbursement, and 3. Coverage.
      </div>
    </div>

    <div style="padding: 10px 0; font-size: 1rem;">
      <div style="font-weight: 600" >1. Cafeteria (section 125)</div>
      <div>Cafeteria plans allows you to select benefits via payroll reduction election. For pretax benefits
        this means you skip on payroll taxes - and this is the only tax code section (125) that allows for this tax
        advantage. {{ fullPlan?.name || 'This Plan' }} offers the following Cafeteria options.
      </div>
      <div v-if="!Object.keys(cafe)?.length" style="padding: 15px; font-style: italic">No cafeteria options</div>
      <ul v-else>
        <li v-for="(k, i) in Object.keys(cafe) || {}" :key="`cafe-${i}`" style="padding: 7px 0;">
          <div>
            <div style="font-weight: 600; color: #424242">{{ cafeKeys[k].name }}</div>
            <div style="font-size: .85rem">{{ cafeKeys[k].description }}</div>
          </div>
        </li>
      </ul>
    </div>

    <div style="padding: 10px 0; font-size: 1rem;">
      <div style="font-weight: 600" >2. Reimbursement (HRA - section 105)</div>
      <div>HRA or health reimbursement arrangements allow your employer to fund a pretax account to reimburse
        qualified medical expenses or premiums on a tax free basis. {{ fullPlan?.name || 'This Plan' }} offers the
        following HRA options.
      </div>
      <div v-if="!Object.keys(hra)?.length" style="padding: 15px; font-style: italic">No reimbursement options</div>
      <ul v-else>
        <li v-for="(k, i) in Object.keys(hra) || {}" :key="`hra-${i}`" style="padding: 7px 0;">
          <div>
            <div style="font-weight: 600; color: #424242">{{ hraKeys[k].name }}</div>
            <div style="font-size: .85rem">{{ hraKeys[k].description }}</div>
          </div>
        </li>
      </ul>
    </div>

    <div style="padding: 10px 0; font-size: 1rem;">
      <div style="font-weight: 600">3. Coverages</div>
      <div>Coverages are arrangements of insurance, direct care, cost sharing or other similar arrangements
        where you pay an affordable participation fee or premium, and your medical expenses are shared or covered by a
        community of participants. {{ fullPlan?.name || 'This Plan' }} offers the following Coverage options.
      </div>
      <ul>
        <li v-for="(cov, i) in h$.data || []" :key="`cov-${i}`" style="padding: 7px 0;">
          <div>
            <div style="font-weight: 600; color: #424242">{{ cov.name }}</div>
            <div style="font-size: .85rem">{{ cov.description }}</div>
            <rate-table :model-value="cov"></rate-table>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {cafeKeys, hraKeys} from 'components/plans/utils';
  import {HFind} from 'src/utils/hFind';
  import {useCoverages} from 'stores/coverages';
  import RateTable from 'components/coverages/cards/RateTable.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = usePlans();
  const coverageStore = useCoverages();

  const props = defineProps({
    plan: { required: false }
  })

  const limit = ref(20);

  const { item: fullPlan } = idGet({
    store,
    value: computed(() => props.plan),
    onWatch: (val) => {
      const l = Object.keys(val?.coverages || {,
    useAtcStore
  })?.length;
      if (l) limit.value = l;
    }
  })

  const cafe = computed(() => {
    const obj = {};
    for (const k in fullPlan.value?.cafe || {}) {
      if (fullPlan.value.cafe[k]?.active) obj[k] = fullPlan.value.cafe[k];
    }
    return obj;
  })
  const hra = computed(() => {
    const obj = {};
    for (const k in fullPlan.value?.hra || {}) {
      if (fullPlan.value.hra[k]?.active) obj[k] = fullPlan.value.hra[k];
    }
    return obj;
  })


  const { h$ } = HFind({
    store: coverageStore,
    limit,
    params: computed(() => {
      return {
        query: {
          _id: { $in: Object.keys(fullPlan.value?.coverages || {}) }
        }
      }
    })
  })

</script>

<style lang="scss" scoped>

</style>
