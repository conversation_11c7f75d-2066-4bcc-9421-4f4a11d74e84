<template>
  <div class="_fw">
    <div class="q-pa-sm w500 mw100">
      <q-input v-model="search.text" placeholder="Search Docs...">
        <template v-slot:prepend>
          <q-icon name="mdi-magnify"></q-icon>
        </template>
      </q-input>
    </div>
    <docs-class-filter v-bind="{planClass, subClass}" v-model="searchQ"></docs-class-filter>
    <div class="__dg">
      <div class="__c" v-for="(doc, i) in h$.data || []" :key="`doc-${i}`">
        <q-btn class="t-r-a" dense flat icon="mdi-dots-vertical">
          <q-menu>
            <q-list separator>
              <remove-item name="Doc" @remove="remove(doc)"></remove-item>
            </q-list>
          </q-menu>
        </q-btn>
        <doc-card :model-value="doc"></doc-card>
        <div class="q-pt-sm row items-center">
          <q-chip color="black" outline size="sm" v-if="isActive(doc)">
            <q-icon color="green" name="mdi-checkbox-marked-outline"></q-icon>
            <span>Active</span>
          </q-chip>
          <q-chip v-else color="black" outline size="sm" label="Not Active"></q-chip>
          <q-space></q-space>
          <q-btn size="sm" push label="Select Doc" class="_p_btn" @click="$emit('select', doc)">
          </q-btn>
        </div>
      </div>
    </div>
    <div v-if="!h$.total" class="q-pa-lg">
      <i>No Results</i>
    </div>
  </div>
</template>

<script setup>
  import DocsClassFilter from 'components/plans/docs/lists/DocsClassFilter.vue';
  import DocCard from 'components/plans/docs/cards/DocCard.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {usePlanDocs} from 'stores/plan-docs';
  import RemoveItem from 'components/common/buttons/RemoveItem.vue';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {_get} from 'symbol-syntax-utils';

  const store = usePlanDocs();
  const planStore = usePlans();

  const emit = defineEmits(['select'])
  const props = defineProps({
    plan: { required: true },
    planClass: String,
    subClass: String
  })

  const { search, searchQ } = HQuery({});

  const { item:fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan)
  ,
    useAtcStore
  })
  const remove = async (doc) => {
    await store.remove(doc._id);
  }
  const { h$ } = HFind({
    store,
    params: computed(() => {
      return {
        query: {
          plan: props.plan?._id || props.plan,
          ...searchQ.value
        }
      }
    })
  })

  const isActive = (doc) => {
    const { path } = doc;
    return _get(fullPlan.value, path) === doc._id
  }
</script>

<style lang="scss" scoped>
  .__dg {
    padding: 15px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 350px));
    justify-content: center;
    grid-gap: 15px;
  }

  .__c {
    width: 100%;
    border-radius: 7px;
    padding: 20px;
    border: solid 2px #999;
    position: relative;
  }
</style>
