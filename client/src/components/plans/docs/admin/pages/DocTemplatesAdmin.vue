<template>
  <q-page class="q-pa-md">
  <div class="q-py-md q-px-xl">
    <q-btn flat icon-right="mdi-plus" label="New" @click="adding = true"></q-btn>
  </div>

  <q-tab-panels class="_panel" :model-value="adding || !!route.params.id">
    <q-tab-panel class="_panel" :name="true">
      <div class="q-pa-sm">
        <q-btn flat icon="mdi-chevron-left" @click="$router.push({...$route, params: {}})"></q-btn>
      </div>
      <doc-template-form :model-value="editing"></doc-template-form>
    </q-tab-panel>
    <q-tab-panel class="_panel" :name="false">
      <div class="_fw mw500">
        <q-input filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
      </div>
      <div class="row">
        <div v-for="(doc, i) in h$.data || []" :key="`doc-${i}`" class="col-12 col-sm-6 col-md-3 q-pa-sm">
          <div class="q-pa-md br10 bs2-8 bg-white cursor-pointer" @click="openDoc(doc)">
            <div class="tw-six font-1r">{{doc.name}}</div>
            <div class="row items-center">
              <q-chip class="tw-six" dark :color="planClasses[doc.class]?.color" :label="planClasses[doc.class]?.label"></q-chip>
              <q-space></q-space>
              <q-btn @click.stop="copyDialog = i" dense flat icon="mdi-content-copy" color="ir-grey-6">
                <common-dialog setting="xsmall" :model-value="copyDialog === i" @update:model-value="val => val ? copyDialog = i : copyDialog = -1">
                  <div class="w300 q-pa-md bg-white">
                    <div class="tw-six ir-grey-7">Create copy of {{doc.name}}?</div>
                    <div class="row justify-end items-center q-pt-md">
                      <q-btn push color="blue" label="Copy" no-caps size="sm" @click="copy(doc)"></q-btn>
                    </div>
                  </div>
                </common-dialog>
              </q-btn>
            </div>

          </div>
        </div>
      </div>
    </q-tab-panel>
  </q-tab-panels>

  </q-page>
</template>

<script setup>
  import DocTemplateForm from 'components/plans/docs/admin/forms/DocTemplateForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useDocTemplates} from 'stores/doc-templates';
  import {$errNotify, $successNotify} from 'src/utils/global-methods';

  import {idGet} from 'src/utils/id-get';
  import {useRoute, useRouter} from 'vue-router';
  import {planClasses} from 'components/plans/utils';
  import {_pick} from 'symbol-syntax-utils';
  import {HQuery} from 'src/utils/hQuery';
  import {pickDocTemplateFieldsForCopy} from 'components/plans/docs/utils';

  const route = useRoute();
  const router = useRouter();
  const store = useDocTemplates();

  const adding = ref(false);
  const copyDialog = ref(-1);

  const { search, searchQ } = HQuery({})

  const { h$ } = HFind({
    store,
    limit: ref(20),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value
        }
      }
    })
  })

  const { item: editing } = idGet({
    store,
    value: computed(() => route.params.id)
  ,
    useAtcStore
  })

  const openDoc = (doc) => router.push({...route, params: { id: doc._id }});

  const copy = async (doc) => {
    const rest = _pick(doc, pickDocTemplateFieldsForCopy);
    const newDoc = await store.create({ ...rest, name: `Copy of ${doc.name}` })
        .catch(err => $errNotify(err.message));

    if(newDoc) $successNotify('Doc Copied');
  }

</script>

<style lang="scss" scoped>

</style>
