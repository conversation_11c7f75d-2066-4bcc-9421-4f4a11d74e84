<template>
  <div class="_fw">
    <div class="q-py-sm">
      <q-radio :model-value="global" :val="1" label="Global (all groups)" @update:model-value="setAll"></q-radio>
      <q-radio :model-value="global" :val="0" label="By Employee Group" @update:model-value="setAll"></q-radio>
    </div>

    <q-tab-panels class="_panel" animated transition-prev="jump-down" transition-next="jump-up" :model-value="tab">
      <q-tab-panel class="_panel" name="global">

        <employer-contribution @update:model-value="dirty = true" v-model="form['*']"></employer-contribution>

      </q-tab-panel>

      <q-tab-panel class="_panel" name="groups">

        <q-chip color="transparent" clickable @click="dialog = true">
          <span class="q-mr-sm">Add Groups</span>
          <q-icon name="mdi-plus" color="primary"></q-icon>
        </q-chip>
        <div v-if="!g$.total" class="q-pa-md text-italic">No groups participating</div>

        <q-list dense separator>
          <q-expansion-item dense group="11" v-for="(grp, i) in g$.data" :key="`grp-${i}`" expand-icon="mdi-menu-down">
            <template v-slot:header>
              <q-item class="_fw">
                <q-item-section avatar>
                  <default-avatar :model-value="grp._fastjoin?.org || grp.org" :store="orgStore" :use-atc-store="useAtcStore"></default-avatar>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ grp.name }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>

            <employer-contribution
                @update:model-value="dirty = true"
                v-model="form[grp._id]"
            ></employer-contribution>

          </q-expansion-item>
        </q-list>
      </q-tab-panel>
      <q-tab-panel name="add" class="_panel">
        <div class="_fw q-pa-md bg-white">
          <div class="q-pa-sm tw-six font-1r">Eligible Employee Groups</div>
          <q-list dense separator>
            <q-expansion-item dense v-for="(o, i) in affiliated.h$.data || []" :key="`org-${i}`" expand-icon="mdi-menu-down">
              <template v-slot:header>
                <q-item class="_fw">
                  <q-item-section avatar>
                    <default-avatar :model-value="o" :use-atc-store="useAtcStore"></default-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{o.name}}</q-item-label>
                    <q-item-label caption>{{ $possiblyPlural('Eligible Group', groups[o._id].data) }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>

              <q-item v-for="(grp, i) in groups[o._id]?.data || []" :key="`grp-${i}`" clickable @click="selectGroup(grp)">
                <q-item-section avatar>
                  <q-spinner v-if="adding === grp._id"></q-spinner>
                  <q-icon v-else-if="grpIDs[grp._id]" color="green" name="mdi-checkbox-marked"></q-icon>
                  <q-icon v-else color="grey-7" name="mdi-checkbox-blank-outline"></q-icon>
                </q-item-section>
                <q-item-section>
                  <q-item-label><span class="tw-six">{{ grp.name }}</span> - {{ $possiblyPlural('Member', grp.memberCount) }}
                  </q-item-label>
                  <q-item-label :class="`text-${grpIDs[grp._id] ? 'primary' : 'grey-7'}`">
                    {{ grpIDs[grp._id] ? 'Participating' : 'Not Participating' }}
                  </q-item-label>
                </q-item-section>
              </q-item>

              <div class="q-pa-md text-italic" v-if="!groups[o._id].data?.length">No Eligible Employee Groups</div>
            </q-expansion-item>
          </q-list>
        </div>
      </q-tab-panel>
    </q-tab-panels>

    <q-slide-transition>
      <div class="_fw row justify-end q-py-md" v-if="dirty">
        <q-btn flat no-caps class="tw-six" @click="save">
          <span class="q-mr-sm">Save Changes</span>
          <q-icon name="mdi-content-save" color="secondary"></q-icon>
        </q-btn>
      </div>
    </q-slide-transition>

  </div>
</template>

<script setup>
  import EmployerContribution from './EmployerContribution.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {computed, ref, watch} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {usePlans} from 'stores/plans';
  import {planGroups} from 'components/plans/groups/utils';
  import {useAtcStore} from 'src/stores/atc-store';
  import {HFind} from 'src/utils/hFind';
  import {$possiblyPlural} from 'src/utils/global-methods';

  const orgStore = useOrgs();
  const planStore = usePlans();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    plan: { required: true }
  })

  const formFn = (defs) => {
    return {
      type: 'flat',
      ...defs
    }
  }
  const form = ref({ ['*']: formFn() })

  const dialog = ref(false);

  const dirty = ref(false);

  const save = () => {
    if (props.plan?._id) {
      planStore.patch(props.plan?._id, { employerContribution: form.value })
    } else {
      planStore.patchInStore(props.plan?._id || props.plan?.__tempId, { employerContribution: form.value })
    }
    dirty.value = false;
  }

  const { adding, addGroup, grpIDs, groups, affiliated, groupStore } = planGroups(computed(() => props.plan))

  const selectGroup = (g) => {
    addGroup(g);
    dialog.value = false;
  }


  const { h$: g$ } = HFind({
    store: groupStore,
    pause: computed(() => !props.plan?.groups),
    limit: computed(() => props.plan?.groups?.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: props.plan?.groups }
        }
      }
    })
  })

  const global = computed(() => {
    if (form.value['*']) return 1;
    else return 0;
  })
  const tab = computed(() => {
    if(dialog.value) return 'add'
    else if(global.value === 1) return 'global';
    else return 'groups'
  })

  const last = ref({});

  const setAll = (val) => {
    dialog.value = false;
    if (val === 1) {
      last.value = form.value;
      form.value = { ['*']: formFn() }
    } else form.value = { ...last.value };
    dirty.value = false;
  }

  watch(() => props.plan, (nv) => {
    if (nv?.employerContribution) form.value = { ...nv.employerContribution };
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
