<template>
  <div class="_fw">

    <div class="q-pa-sm q-pb-lg">
      <div class="tw-six font-1r">Plan Permissions</div>
      <div class="font-7-8r">Create groups of people and grant them plan management permissions</div>
    </div>

    <subject-capabilities :def-need="defNeed" :subject-id="plan._id" :caps="caps"></subject-capabilities>

  </div>
</template>

<script setup>
  import SubjectCapabilities from 'components/capabilities/forms/SubjectCapabilities.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {defaultHierPart, defaultScheme} from 'src/utils/ucans';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const planStore = usePlans();
  const envStore = useEnvStore();
  const { getPlanId:planId } = contextItems(envStore);

  const { item: plan } = idGet({
    store: planStore,
    value: planId
  ,
    useAtcStore
  })

  const wit = { scheme: defaultScheme, hierPart: defaultHierPart };

  const defNeed = computed(() => [['orgs', 'WRITE'], [`orgs:${plan.value.org}`, '*'], [`plans:${planId.value}`, '*']],
  )
  const caps = computed(() => {
    return {
      planAdmin: {
        label: 'Plan Admin',
        description: 'Members will have ability to manage health plan and member details',
        cap: {
          with: wit,
          can: { namespace: `plans:${planId.value}`, segments: ['*'] }
        }
      },
      claimsAdmin: {
        label: 'Claims Admin',
        description: 'Members will have ability to approve/deny plan claims',
        cap: {
          with: wit,
          can: { namespace: `plans:${planId.value}`, segments: ['claimsAdmin'] }
        }
      },
      financeAdmin: {
        label: 'Finance Admin',
        description: 'Members will have ability to manage plan accounts, budgets, and cards',
        cap: [
          {
            with: wit,
            can: { namespace: `orgs:${plan.value.org}`, segments: ['financeAdmin'] }
          },
          {
            with: wit,
            can: { namespace: `plans:${planId.value}`, segments: ['financeAdmin'] }
          }
        ]
      },
      enrollmentAdmin: {
        label: 'Enrollment Admin',
        description: 'Members will have ability to manage enrollments and approve changes',
        cap: {
          with: wit,
          can: { namespace: `plans:${planId.value}`, segments: ['enrollmentAdmin'] }
        }
      },
    }
  })
</script>

<style lang="scss" scoped>

</style>
