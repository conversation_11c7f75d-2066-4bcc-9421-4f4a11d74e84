<template>
  <div class="_fw">

    <div class="row items-center">
      <q-chip size="sm" v-if="plan?.template" label="Template"></q-chip>
      <q-space></q-space>
      <q-chip size="sm" color="transparent">
        <span class="q-mr-sm">{{plan?.active ? 'Active' : 'Inactive'}}</span>
        <q-icon size="16px" v-if="plan?.active" name="mdi-checkbox-marked-outline" color="green"></q-icon>
        <q-icon size="16px" v-else name="mdi-checkbox-blank-outline" color="ir-grey-7"></q-icon>
      </q-chip>
    </div>
    <div class="font-1-1-8r tw-six">{{plan?.name}}</div>
    <div class="font-7-8r" v-html="$limitStr(description, 100, '...')"></div>

    <q-separator class="q-my-sm"></q-separator>
    <div class="row items-center">
      <benefits-chip class="tw-six" dark color="p6" :model-value="plan?.cafe"></benefits-chip>
      <coverages-chip class="tw-six" dark color="p6" :model-value="plan?.coverages"></coverages-chip>
    </div>

  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {$limitStr} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';

  import { useOrgs } from 'stores/orgs';
  import { usePlans } from 'stores/plans';
  import CoveragesChip from 'components/plans/cards/CoveragesChip.vue';
  import BenefitsChip from 'components/plans/cards/BenefitsChip.vue';
  const store = usePlans();
  const orgStore = useOrgs();

  const props = defineProps({
    modelValue: { required: true },
    template: Boolean
  });

  const description = computed(() => {
    const d = plan.value?.description || '';
    const rem = Math.max(0, 80 - d.length);
    return `${d}${' '.repeat(rem)}`
  })

  const { item: plan } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })

  const { item: org } = idGet({
    value: computed(() => plan.value.org),
    store: orgStore
  ,
    useAtcStore
  })
</script>

<style lang="scss" scoped>

</style>
