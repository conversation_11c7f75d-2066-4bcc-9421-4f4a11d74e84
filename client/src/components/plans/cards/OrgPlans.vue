<template>
  <div class="_fw">

    <div class="_fw q-px-md">
      <div class="font-1r tw-six flex items-center">
        <div>{{ fullOrg?.name }} Health Plans</div>
        <q-btn @click="dialog = true" flat no-caps class="tw-six">
          <q-icon name="mdi-plus" color="primary" size="25px"></q-icon>
        </q-btn>
      </div>
<!--      <div class="row">-->
<!--        <q-radio v-model="active" :val="true" label="Active"></q-radio>-->
<!--        <q-radio v-model="active" :val="false" label="Inactive"></q-radio>-->
<!--      </div>-->
    </div>
    <div class="q-pa-lg" v-if="!h$.data?.length">
      <div class="font-1r alt-font text-ir-grey-8">No plans found for {{ fullOrg?.name }}</div>
    </div>

    <div class="__cg q-pa-md">
      <div class="__c q-pa-md cursor-pointer" v-for="(pln, i) in h$.data || []" :key="`pln-${i}`"
           @click.stop="selectPlan(pln)">
        <plan-card :model-value="pln"></plan-card>
      </div>
    </div>


    <common-dialog setting="right" v-model="dialog">
      <div class="_fw q-pa-md">
        <plan-form
            @new="$router.push({ name: 'plan-admin', params: { planId: $event._id }})"
            :org="fullOrg"
        ></plan-form>
      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import PlanForm from 'components/plans/forms/PlanForm.vue';
  import PlanCard from 'components/plans/cards/PlanCard.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, onMounted, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {HFind} from 'src/utils/hFind';
  import {useRoute, useRouter} from 'vue-router';
  import {useOrgs} from 'stores/orgs';
  import {LocalStorage} from 'symbol-auth-client';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const store = usePlans();
  const route = useRoute();
  const router = useRouter();
  const orgStore = useOrgs();
  const envStore = useEnvStore();

  const props = defineProps({
    org: { required: true },
    selectMode: Boolean
  })

  const { getOrgId } = contextItems(envStore)

  const dialog = ref(false);
  const selected = ref(undefined);
  const manualBack = ref(false);

  const { item: plan } = idGet({
    value: computed(() => selected.value || route.query.planId || getOrgId.value),
    store
  ,
    useAtcStore
  })

  const orgId = computed(() => route.params.orgId || props.org?._id || props.org);

  const { item:fullOrg } = idGet({
    value: computed(() => orgId.value),
    store: orgStore
  ,
    useAtcStore
  })

  const selectPlan = (pln) => {
    manualBack.value = !pln;
    selected.value = pln;
    if(pln._id){
      envStore.setPlanId(pln._id, 'org-plans')
      router.go()
    }
    // router.push({ name: 'group-plan-admin', params: { planId: pln._id || '' } })
  }

  const { h$ } = HFind({
    store,
    params: computed(() => {
      return {
        query: {
          // active: active.value || { $ne: true },
          org: orgId.value
        }
      }
    })
  })

</script>

<style lang="scss" scoped>
  .__cg {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 450px));
    grid-template-rows: repeat(auto-fit, auto);
    grid-gap: 10px;
  }

  .__c {
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .15);
    background: white;
  }
</style>
