<template>
  <div class="_fw q-py-lg pw2">
    <div class="q-pa-sm tw-six font-1r row items-center">
      <div>
        <host-chip class="tw-six font-1r _i_i" :model-value="host"></host-chip> Service Area
      </div>
      <q-space></q-space>
      <q-btn v-if="changes" push class="_a_btn" no-caps label="Save Changes" @click="save()"></q-btn>
    </div>
    <state-areas v-model="form" @update:model-value="changes = true"></state-areas>

  </div>
</template>

<script setup>
  import StateAreas from 'components/hosts/forms/StateAreas.vue';
  import HostChip from 'components/hosts/cards/HostChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useHosts} from 'stores/hosts';

  const hostStore = useHosts();

  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  ,
    useAtcStore
  })

  const form = ref({});
  watch(host, (nv) => {
    if(nv) form.value = nv.states || {}
  }, { immediate: true })

  const changes = ref(false)

  const to = ref()
  const save = () => {
    if(to.value) clearTimeout(to.value);
    to.value = setTimeout(() => {
      hostStore.patch(host.value._id, { states: form.value })
    }, 2000);
  }

</script>

<style lang="scss" scoped>

</style>
