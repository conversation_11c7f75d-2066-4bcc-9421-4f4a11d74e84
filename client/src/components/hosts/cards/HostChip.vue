<template>
  <default-chip v-bind="{ color: 'ir-grey-2', modelValue: {...host, useAvatar: host?.avatar || host?._fastjoin?.org?.avatar }, namePath: 'dba', backupNamePath: '_fastjoin.org.name', avatarPath: 'useAvatar', useAtcStore: useAtcStore, ...$attrs }">
    <template v-slot:right>
      <slot name="right"></slot>
    </template>
  </default-chip>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';

  import {useHosts} from 'stores/hosts';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useHosts();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item:host } = idGet({
    store,
    value: computed(() => props.modelValue),
    params: ref({ runJoin: { host_org: true }})
  })
</script>

<style lang="scss" scoped>

</style>
