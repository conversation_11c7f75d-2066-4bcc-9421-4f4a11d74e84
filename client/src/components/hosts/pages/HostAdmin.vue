<template>
  <q-page>
    <div class="row justify-center pw2 pd5">
      <div class="_cent">
        <div class="font-1r tw-six q-pa-sm">
          <span v-if="host._id">{{ host.dba }}</span>
          <span v-else>Select Host Account</span>
        </div>
        <div class="q-pa-sm" v-if="host._id">
          <div class="font-1r">Your account id is
            <q-chip clickable @click="$copyTextToClipboard(host._id)" color="ir-bg2" :label="host._id"></q-chip>
          </div>
          <q-separator class="q-my-sm"></q-separator>
          <div class="font-7-8r">Paste any page url here to get a link that will tie visitors to your account</div>
          <div class="q-py-xs _fw mw700">
            <q-input placeholder="URL here" dense filled v-model="enterUrl"></q-input>
          </div>
          <q-chip color="ir-bg2" clickable @click="$copyTextToClipboard(displayUrl)">
            <q-icon color="primary" name="mdi-link" class="q-mr-sm"></q-icon>
            <span class="tw-six">{{displayUrl}}</span>
          </q-chip>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useHosts} from 'stores/hosts';
  import {$copyTextToClipboard} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';

  const hostStore = useHosts()

  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  ,
    useAtcStore
  })

  const enterUrl = ref();

  const displayUrl = computed(() => {
    if(!enterUrl.value) return 'Enter a URL';
    if(!/commoncare\.org/.test(enterUrl.value)) return 'Enter a CommonCare URL'
    if(!/^https:\/\//.test(enterUrl.value)) return 'Must use secure "https://" protocol'
    if(!/^(https?:\/\/)[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*(:\d+)?(\/[^\s]*)?$/.test(enterUrl.value)) return 'Enter a valid url'
    else {
      const urlObj = new URL(enterUrl.value)
      urlObj.searchParams.set('hostId', host.value._id)
      return urlObj.toString()
    }
  })
</script>

<style lang="scss" scoped>

</style>
