<template>
  <div class="_fw">
    <div class="row justify-center">
      <div class="_cent pd6 pw2">
        <div class="q-pa-sm tw-six font-1r">
          Your people <q-btn dense flat icon="mdi-plus" color="primary" @click="addDialog = true"></q-btn>
        </div>
        <div class="row">
          <q-tabs v-model="tab" align="left" no-caps dense indicator-color="a3">
            <q-tab v-for="(t, i) in Object.keys(tabs)" :key="`tab-${i}`" :label="tabs[t].label" :name="t"></q-tab>
          </q-tabs>
        </div>
        <div class="q-py-sm w600 mw100">
          <q-input filled v-model="search.text" dense>
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>
        <div class="__gr">
          <div class="q-pa-sm relative-position" v-for="(r, i) in r$.data" :key="`r-${i}`">

            <ref-card square :model-value="r">
              <div class="t-r-a z5">
               <menu-btn :subject="r" :btn-attrs="{ color: 'white' }" :items="tabs[tab].items"></menu-btn>
              </div>
            </ref-card>
          </div>
        </div>
      </div>
    </div>

    <common-dialog setting="small" v-model="addDialog">
      <div class="q-pa-md bg-ir-bg _fw">
        <div class="q-pa-sm tw-six font-1r">Invite sellers to join your host</div>
        <q-chip clickable @click="$copyTextToClipboard(link)" color="ir-bg2">
          <q-icon name="mdi-content-copy" class="q-mr-sm" color="a3"></q-icon>
          <input class="_common_inp" :value="link">
        </q-chip>

      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import RefCard from 'components/refs/cards/RefCard.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import MenuBtn from 'components/common/btns/MenuBtn.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useRefs} from 'stores/refs';
  import {LocalStorage} from 'symbol-auth-client';
  import {idGet} from 'src/utils/id-get';
  import {useHosts} from 'stores/hosts';
  import {HQuery} from 'src/utils/hQuery';
  import {$copyTextToClipboard, getRootDomain} from 'src/utils/global-methods';
  import {useRouter} from 'vue-router';
  import {loginPerson} from 'stores/utils/login';
  const refStore = useRefs();
  const hostStore = useHosts();

  const { login } = loginPerson();

  const router = useRouter();

  const { item:host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  ,
    useAtcStore
  })

  const addDialog = ref(false);
  const tab = ref('active');

  const tabs = computed(() => {
    return {
      'active': {
        label: 'Active',
        q: { host: host.value._id, approved: true },
        items: [
          {
            label: 'View Profile',
            icon: { name: 'mdi-account', color: 'primary' },
            on: (val) => {
             const { href } = router.resolve({ name: 'ref-page', params: { refId: val._id }})
              window.open(href, '_blank')
            }
          },
          {
            label: 'Disable affiliation',
            icon: { name: 'mdi-cancel', color: 'red' },
            confirm: 'Disable this person from acting as part of your host?',
            on: (val) => {
              refStore.patch(val._id, { disabled: true, disabledAt: new Date(), disabledBy: login.value._id })
            }
          }
        ]
      },
      'pending': {
        label: 'Pending',
        q: { host: host.value._id, approved: { $ne: true } },
        items: [
          {
            label: 'Approve affiliation',
            icon: { name: 'mdi-check-circle', color: 'green' },
            on: (val) => {
              refStore.patch(val._id, { approved: true, approvedAt: new Date(), approvedBy: login.value._id });
            }
          },
          {
            label: 'Reject affiliation',
            icon: { name: 'mdi-cancel', color: 'red' },
            confirm: 'Reject this join request?',
            on: (val) => {
              refStore.remove(val._id);
            }
          }
        ]
      },
      'disabled': {
        label: 'Disabled',
        q: { host: host.value._id, disabled: true },
        items: [
          {
            label: 'Re-enable',
            icon: { name: 'mdi-check-circle', color: 'green' },
            on: (val) => {
              refStore.patch(val._id, { disabled: false, $unset: { disabledAt: '', disabledBy: '' }})
            }
          }
        ]
      }
    }
  })


  const link = computed(() => {
    const { href } = router.resolve({ name: 'add-ref', query: { hostId: host.value._id }})
    return getRootDomain() + href;
  })

  const { search, searchQ } = HQuery({})

  const { h$:r$ } = HFind({
    store: refStore,
    pause: computed(() => !host.value._id),
    params: computed(() => {
      return {
        runJoin: { ref_person: true },
        query: { ...tabs.value[tab.value].q, ...searchQ.value }
      }
    })
  })
</script>

<style lang="scss" scoped>
  .__gr {
    grid-gap: 15px;
    width: 100%;
    padding: 20px 0;
    display: grid;
    grid-template-columns: repeat(200, auto)
  }

</style>
