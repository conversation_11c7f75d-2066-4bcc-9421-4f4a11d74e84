<template>
  <div class="_fw">
    <div class="row justify-center bg-ir-a q-py-md">
      <div class="_cent">
        <div class="q-pa-md font-1r tw-six text-ir-deep">Team:
          <q-chip dense square class="font-1r _i_i" color="ir-deep" text-color="ir-bg2">{{ team?.name }}</q-chip>
        </div>
        <div class="row items-center q-pa-md">
          <template v-for="(t, i) in panels" :key="`t-${i}`">
            <q-chip @click="$router.push({ name: $route.name, params: { ...$route.params, tab: t.tab }})" clickable
                    dense square color="transparent">
              <span :class="`${($route.params.tab || defTab) === t.tab ? 'tw-six text-accent' : 'text-a9'}`">{{ t.label }}</span>
            </q-chip>
            <div v-if="i < panels.length - 1">|</div>
          </template>
        </div>
      </div>
    </div>
    <div class="row justify-center">
      <div class="_cent">

        <q-tab-panels animated transition-prev="fade" transition-next="fade" class="_panel" :model-value="$route.params.tab || defTab">
          <q-tab-panel :name="t.tab" class="_panel" v-for="(t, i) in panels" :key="`panel-${i}`">
            <component :is="t.component" v-bind="{ modelValue: team }"></component>
          </q-tab-panel>
        </q-tab-panels>
      </div>
    </div>

  </div>
</template>

<script setup>
  import TeamMembers from 'components/hosts/team/settings/TeamMembers.vue';
  import TeamSchedule from 'components/hosts/team/settings/TeamSchedule.vue';
  import TeamAssignments from 'components/hosts/team/settings/TeamAssignments.vue';
  import TeamSettings from 'components/hosts/team/settings/TeamSettings.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  const defTab = 'members';
  import {useTeams} from 'stores/teams';
  import {idGet} from 'src/utils/id-get';
  import {watch} from 'vue';
  import {SessionStorage} from 'symbol-auth-client';

  const teamStore = useTeams();
  const { item: team } = idGet({
    store: teamStore,
    routeParamsPath: 'teamId'
  ,
    useAtcStore
  })

  watch(team, (nv) => {
    if (nv) SessionStorage.setItem('team_name', nv.name);
  }, { immediate: true });

  const panels = [
    {
      label: 'Members',
      tab: 'members',
      component: TeamMembers
    },
    {
      label: 'Schedule',
      tab: 'schedule',
      component: TeamSchedule
    },
    {
      label: 'Assignments',
      tab: 'assignments',
      component: TeamAssignments
    },
    {
      label: 'Settings',
      tab: 'settings',
      component: TeamSettings
    }
  ]
</script>

<style lang="scss" scoped>

</style>
