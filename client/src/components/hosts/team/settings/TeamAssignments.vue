<template>
  <div>

  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useTeams} from 'stores/teams';
  import {useAtcStore} from 'src/stores/atc-store';

  const teamStore = useTeams();
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: team } = idGet({
    store: teamStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

</script>

<style lang="scss" scoped>

</style>
