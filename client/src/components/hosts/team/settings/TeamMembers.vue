<template>
  <div class="_fw">
    <div class="q-pa-sm">
      <div class="row items-center q-pb-md">
        <q-tabs dense indicator-color="a3" no-caps align="left" v-model="tab">
          <q-tab v-for="(n, i) in Object.keys(navs)" :key="`nav-${i}`" :name="n"
                 :label="navs[n].label"></q-tab>
        </q-tabs>
      </div>
      <div class="_fw mw600">
        <q-input dense filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
      </div>

      <div class="__gr">
        <div class="q-pa-sm" v-for="(r, i) in r$.data" :key="`r-${i}`">
          <ref-card square :model-value="r">
            <div class="t-r-a z5">
              <menu-btn :subject="r" :btn-attrs="{ color: 'white' }" :items="navs[tab].items"></menu-btn>
            </div>
          </ref-card>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
  import RefCard from 'components/refs/cards/RefCard.vue';
  import MenuBtn from 'components/common/btns/MenuBtn.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useTeams} from 'stores/teams';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useRefs} from 'stores/refs';
  import {HQuery} from 'src/utils/hQuery';

  const teamStore = useTeams();
  const refStore = useRefs();

  const props = defineProps({
    modelValue: { required: true }
  })

  const tab = ref('refs')

  const { item: team } = idGet({
    store: teamStore,
    value: computed(() => props.modelValue),
    ref: {}
  })

  const { search, searchQ } = HQuery({})

  const navs = computed(() => {
    return {
      'refs': {
        label: 'Active',
        tab: 'refs',
        q: { _id: { $in: team.value.refs || [] } },
        items: [
          {
            label: 'Remove from team',
            icon: { name: 'mdi-cancel', color: 'red' },
            confirm: `Remove this person from ${team.value.name}?`,
            on: (val) => {
              teamStore.patch(team.value._id, { $pull: { refs: val._id }})
            }
          }
        ]
      },
      // 'invited': {
      //   label: 'Invited',
      //   tab: 'invited',
      //   q: { _id: { $in: team.value.invited || [] } },
      //   items: [
      //     {
      //       label: 'Invite to team',
      //
      //     }
      //   ]
      // },
      // 'req': {
      //   label: 'Requests',
      //   tab: 'req',
      //   q: { _id: { $in: team.value.req || [] } }
      // },
      'add': {
        label: 'Add',
        tab: 'add',
        q: { host: team.value.host, _id: { $nin: team.value.refs || [] } },
        items: [
          {
            label: 'Add to team',
            icon: { name: 'mdi-plus', color: 'primary' },
            on: (val) => {
              teamStore.patch(team.value._id, { $addToSet: { refs: val._id }})
            }
          }
        ]
      }
    }
  })

  const { h$: r$ } = HFind({
    store: refStore,
    params: computed(() => {
      const query = { ...navs.value[tab.value]?.q, $sort: { createdAt: -1 }, ...searchQ.value }
      return {
        runJoin: { ref_person: true },
        query
      }
    })
  })


</script>

<style lang="scss" scoped>
  .__gr {
    grid-gap: 15px;
    width: 100%;
    padding: 20px 0;
    display: grid;
    grid-template-columns: repeat(200, auto)
  }
</style>
