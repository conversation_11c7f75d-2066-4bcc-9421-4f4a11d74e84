<template>
  <div class="row justify-center">
    <div class="_cent pw2 pd4">
    <div class="font-1r tw-six q-pa-sm q-pb-lg">Host Permissions</div>

    <subject-capabilities :def-need="defNeed" :subject-id="hostId" :caps="caps"></subject-capabilities>
    </div>
  </div>
</template>

<script setup>

import SubjectCapabilities from 'components/capabilities/forms/SubjectCapabilities.vue';
import {useHosts} from 'stores/hosts';
import {computed} from 'vue';
import {LocalStorage} from 'symbol-auth-client';
import {defaultHierPart, defaultScheme} from 'src/utils/ucans';
import {idGet} from 'src/utils/id-get';
  import {useAtcStore} from 'src/stores/atc-store';

const hostStore = useHosts()

const hostId = computed(() => LocalStorage.getItem('host_id'))

const { item:host } = idGet({
  store: hostStore,
  value: hostId
,
    useAtcStore
  })

const wit = { scheme: defaultScheme, hierPart: defaultHierPart };
const defNeed = computed(() => [['orgs', 'WRITE'], [`orgs:${host.value.org}`, '*'], [`hosts:${hostId.value}`, '*']],
)

const caps = computed(() => {
  return {
    hostAdmin: {
      label: 'Host Admin',
      description: 'Members will have ability to manage all host settings',
      cap: {
        with: wit,
        can: { namespace: `hosts:${hostId.value}`, segments: ['*'] }
      }
    },
    refAdmin: {
      label: 'People Admin',
      description: 'Members will have ability to manage host people and teams',
      cap: {
        with: wit,
        can: { namespace: `hosts:${hostId.value}`, segments: ['refAdmin'] }
      }
    }
  }
})
</script>

<style lang="scss" scoped>

</style>
