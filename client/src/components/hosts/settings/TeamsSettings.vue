<template>
  <div class="_fw">
    <div class="row justify-center">
      <div class="_cent q-py-md pw2">
       <div class="row">
         <div class="col-12 q-pa-md">
          <div class="q-pa-sm tw-six font-1r text-ir-deep">
            <div>Support Settings</div>
            <div class="font-7-8r tw-five">These settings apply when a specific team is not assigned</div>
          </div>

           <div class="row">
             <div class="col-12 col-md-6 pw2">
               <div class="_f_l _f_chip">Public Support</div>
               <div class="_fw q-py-sm">
                 <div class="_form_grid">
                   <div class="_form_label">Team</div>
                   <div class="q-pa-sm">
                     <team-chip picker emit-value @update:model-value="autoSave('publicSupport', $event)" :host-id="host._id" v-model="form.publicSupport"></team-chip>
                   </div>
                 </div>

               </div>
             </div>
             <div class="col-12 col-md-6 pw2">
               <div class="_f_l _f_chip">Plan Support</div>
               <div class="_fw q-py-sm">
<!--                 //add plans-->
               </div>
             </div>
           </div>
         </div>
       </div>
      </div>
    </div>

  </div>
</template>

<script setup>
  import TeamChip from 'components/hosts/team/utils/TeamChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {idGet} from 'src/utils/id-get';
  import {useHosts} from 'stores/hosts';
  import {HForm, HSave} from 'src/utils/hForm';

  const hostStore = useHosts();

  const { item:host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  ,
    useAtcStore
  })

  const { form, save } = HForm({
    value: host,
    store: hostStore
  })

  const { autoSave } = HSave({
    form,
    save,
    store: hostStore
  })

</script>

<style lang="scss" scoped>
  .__c {
    padding: 20px;
    border-radius: 10px;
    border: solid 2px var(--ir-mid);
    //box-shadow: 0 2px 12px -5px var(--ir-mid);
    cursor:pointer;
    background: var(--ir-bg1);
    transition: all .2s;

    &:hover {
      background: var(--ir-bg);
    }
  }
</style>
