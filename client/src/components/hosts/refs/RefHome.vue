<template>
  <div class="_fw">
    <div class="row justify-center">
      <div class="_cent pd8 pw2">
        <div class="row">
          <div>
            <div class="font-7-8r"><span class="font-1r tw-six">{{reF.name}}</span> - affiliated with <host-chip color="ir-bg2" :model-value="reF.host"></host-chip></div>
            <q-separator class="q-my-xs"></q-separator>
            <div class="font-7-8r">{{reF.email}} | {{reF.phone?.number?.national || 'Phone Unlisted'}}</div>
          </div>
        </div>




      </div>
    </div>
  </div>
</template>

<script setup>
  import HostChip from 'components/hosts/cards/HostChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {useRefs} from 'stores/refs';

  const refStore = useRefs();

  const { item:reF } = idGet({
    store: refStore,
    routeParamsPath: 'refId'
  ,
    useAtcStore
  })
</script>

<style lang="scss" scoped>

</style>
