<template>
  <div class="_fw">
    <q-tab-panels animated class="_panel" :model-value="!$route.params.offerId">
      <q-tab-panel class="_panel" :name="true">
        <q-input class="w600 mw100" dense filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <div class="q-py-sm row items-center">
          <q-icon name="mdi-filter" color="accent" size="20px" class="q-mr-sm"></q-icon>
          <team-member-status multiple clickable picker v-model="status"></team-member-status>
        </div>
        <data-table
            v-bind="{
        searchOff: true,
        columns,
        store: offerStore,
        params,
        openItem,
        addBtnAttrs: { noCaps: true },
        addLabel: 'New Offer'
      }"
        >
          <template v-slot:form="scope">
            <host-offer-form
                @update:model-value="scope.toggleDialog(false)"
                :model-value="scope.editing"
            ></host-offer-form>
          </template>
        </data-table>
      </q-tab-panel>
      <q-tab-panel class="_panel" :name="false">
        <offer-editor :model-value="$route.params.offerId"></offer-editor>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import DataTable from 'components/common/tables/DataTable.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import FeeChip from 'components/plans/team/cards/FeeChip.vue';
  import HostOfferForm from 'components/hosts/offers/forms/HostOfferForm.vue';
  import TdChip from 'components/common/tables/TdChip.vue';
  import OfferEditor from 'components/hosts/plans/offers/OfferEditor.vue';
  import TeamMemberStatus from 'components/plans/team/cards/TeamMemberStatus.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useOffers} from 'stores/offers';
  import {computed, ref} from 'vue';
  import {useHosts} from 'stores/hosts';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {$limitStr} from 'src/utils/global-methods';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {usePlans} from 'stores/plans';
  import {useRoute, useRouter} from 'vue-router';
  const router = useRouter();
  const route = useRoute();

  const offerStore = useOffers();
  const hostStore = useHosts();
  const planStore = usePlans();

  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  ,
    useAtcStore
  })

  const openItem = (val) => {
    router.push({ ...route, params: { ...route.params, offerId: val._id}})
  }

  const { search, searchQ } = HQuery({});
  const { h$:p$ } = HFind({
    store: planStore,
    pause: computed(() => !search.value.text),
    params: computed(() => {
      return {
        ...searchQ.value
      }
    })
  })

  const idList = computed(() => search.value.text ? p$.data.map(a => a._id) : undefined)

  const status = ref([])

  const params = computed(() => {
    const query = {
      host: host.value?._id
    }
    if(status.value.length) query.status = { $in: status.value }
    if(idList.value) query.plan = {$in: idList.value }
    return {
      runJoin: { offer_plan: true },
      query
    }
  })
  const statuses = {
    'pending': { label: 'Pending', color: 'a2' },
    'canceled': { label: 'Cancelled', color: 's2' },
    'active': { label: 'Active', color: 'p2' }
  }
  const columns = computed(() => {
    return [
      {
        label: 'Plan',
        component: TdText,
        attrs: (row) => {
          return {
            col: { value: $limitStr(row._fastjoin?.plan?.name, 30, '...') }
          }
        }
      },
      {
        label: 'Sponsor',
        component: OrgChip,
        attrs: (row) => {
          return {
            modelValue: row._fastjoin?.plan?.org
          }
        }
      },
      {
        label: 'Fee',
        component: FeeChip,
        attrs: (row) => {
          return {
            modelValue: row
          }
        }
      },
      {
        label: 'Status',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              label: 'Unknown',
              ...statuses[row.status]
            }
          }
        }
      }
    ]
  })

</script>

<style lang="scss" scoped>

</style>
