<template>
  <div class="_fw">

    <data-table
        v-bind="{
          noMenu:true,
          store:planStore,
          searchAttrs: { dense: true, filled: true },
          params,
          hideAddBtn: true,
          columns,
          openItem
        }"
    >

      <template v-slot:above>
        <div class="q-py-md">
          <div>
            <div class="tw-six font-3-4r">Bid Status</div>
            <q-radio v-model="publicPlans" :val="false" label="Private"></q-radio>
            <q-radio v-model="publicPlans" :val="true" label="Public"></q-radio>
          </div>
          <q-separator vertical class="q-mx-md"></q-separator>
          <div>
            <div class="tw-six font-3-4r">Role</div>
            <role-chip picker v-model="opp"></role-chip>
          </div>
        </div>
      </template>

    </data-table>

    <common-dialog setting="right" :model-value="!!offering"
                   @update:model-value="val => val ? undefined : offering = undefined">
      <div class="_fw bg-white q-pa-md">
        <host-offer-form :role="opp" :planId="offering"></host-offer-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import DataTable from 'components/common/tables/DataTable.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import RoleChip from 'components/plans/team/cards/RoleChip.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import HostOfferForm from 'components/hosts/offers/forms/HostOfferForm.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';

  import {currentEnrolled} from 'components/plans/utils/enrollments';
  import {dollarString} from 'src/utils/global-methods';
  import {useOffers} from 'stores/offers';
  import {usePlans} from 'stores/plans';
  import {useHosts} from 'stores/hosts';


  const planStore = usePlans();
  const offerStore = useOffers();
  const hostStore = useHosts();

  const publicPlans = ref(false);
  const opp = ref('care_director');

  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  ,
    useAtcStore
  })

  const offers = ref({ total: 0, data: [] });

  watch(host, async (nv) => {
    if (nv) {
      opp.value = (nv.roles || [])[0] || 'care_director';
      if (!offers.value.total) {
        offers.value = await offerStore.find({ query: { host: nv._id, $limit: 100 } });
      }
    }
  }, { immediate: true });

  const offerPlanIds = computed(() => (offers.value.data || []).filter(a => a.role === opp.value).map(a => a.plan))

  const params = computed(() => {
    const query = {}
    if(offerPlanIds.value.length) query._id = { $nin: offerPlanIds.value };
    if (publicPlans.value) query[`rfp.${opp.value}.public`] = true;
    else query[`rfp.${opp.value}.hosts`] = { $in: [host.value._id].filter(a => !!a) };
    return {
      query
    }
  })

  const columns = computed(() => {
    return [
      {
        label: 'Plan',
        component: TdText,
        attrs: (row) => {
          return {
            col: { value: row.name }
          }
        }
      },
      {
        label: 'Sponsor',
        component: OrgChip,
        attrs: (row) => {
          return {
            modelValue: row.org
          }
        }
      },
      {
        label: 'Enrolled',
        component: TdText,
        attrs: (row) => {
          const total = currentEnrolled(row);
          return {
            col: { value: dollarString(total, '', 0) }
          }
        }
      }
    ]
  })

  const offering = ref(undefined);
  const openItem = (val) => {
    offering.value = val._id;
  }
</script>

<style lang="scss" scoped>

</style>
