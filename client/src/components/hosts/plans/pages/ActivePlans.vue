<template>
  <div class="_fw">
    <data-table
        v-bind="{
      searchAttrs: { dense: true, filled: true },
      columns,
      store: planStore,
      params,
      openItem,
      hideAddBtn: true,
      noMenu: true
      }"
    ></data-table>

  </div>
</template>

<script setup>
  import DataTable from 'components/common/tables/DataTable.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import TeamChip from 'components/hosts/team/utils/TeamChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed} from 'vue';
  import {usePlans} from 'stores/plans';
  import {dollarString} from 'src/utils/global-methods';
  import {useHosts} from 'stores/hosts';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {currentEnrolled} from 'components/plans/utils/enrollments';
  import {useRouter} from 'vue-router';

  const planStore = usePlans();
  const hostStore = useHosts();
  const router = useRouter();

  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  ,
    useAtcStore
  })

  const openItem = (val) => {
    router.push({ name: 'host-plan', params: { planId: val._id } })
  }

  const params = computed(() => {
    return {
      query: {
        [`team.${host.value._id}.id`]: host.value._id
      }
    }
  })

  // const planYear = String(new Date().getFullYear())

  const columns = computed(() => {
    return [
      {
        label: 'Sponsor',
        component: OrgChip,
        attrs: (row) => {
          return {
            modelValue: row.org
          }
        }
      },
      {
        label: 'Participants',
        component: TdText,
        attrs: (row) => {
          const total = currentEnrolled(row);
          return {
            col: { value: dollarString(total, '', 0) }
          }
        }
      },
      {
        label: 'Team',
        component: TeamChip,
        attrs: (row) => {
          return {
            modelValue: (host.value.plans || {})[row._id]?.team
          }
        }
      }
    ]
  })
</script>

<style lang="scss" scoped>

</style>
