<template>
  <div class="_fw">
    <div class="row justify-end">
      <q-tabs align="left" indicator-color="primary" v-model="tab" dense no-caps>
        <q-tab label="Summary" name="summary"></q-tab>
        <q-tab label="Application" name="app"></q-tab>
        <q-tab label="Confirm" name="confirm"></q-tab>
      </q-tabs>
    </div>
    <q-tab-panels class="_panel" animated v-model="tab" transition-next="slide-left" transition-prev="slide-right">
      <q-tab-panel class="_panel" name="summary">
        <div class="_f_l _f_chip">Person</div>
        <div class="_form_grid _f_g_r">
          <div class="_form_label">Name</div>
          <div class="q-pa-sm">
            <default-chip :model-value="person" :use-atc-store="useAtcStore"></default-chip>
          </div>
          <div class="_form_label">Email</div>
          <div class="q-pa-sm">
            <q-chip color="ir-bg2" clickable @click="$copyTextToClipboard(person.email)">
              <q-icon name="mdi-email" color="primary"></q-icon>
              <span class="q-ml-sm">{{ person.email }}</span>
            </q-chip>
          </div>
          <div class="_form_label">Phone</div>
          <div class="q-pa-sm">
            <q-chip color="ir-bg2" clickable @click="$copyTextToClipboard(person.phone.number.e164)">
              <q-icon name="mdi-phone" color="primary"></q-icon>
              <span class="q-ml-sm">{{ person.phone?.number?.national }}</span>
            </q-chip>
          </div>
        </div>
        <div class="_f_l _f_chip">Household</div>
        <div class="_form_grid _f_g_r">
          <div class="_form_label">Members</div>
          <div class="q-pa-sm">
            <household-row :model-value="household"></household-row>
          </div>
          <div class="_form_label">Location</div>
          <div class="q-px-sm _py12">
            <span v-if="enrollment.address">{{ $addressDisplay(enrollment.address) }}</span>
            <span v-else-if="person.address">{{ $addressDisplay(enrollment.address) }}</span>
            <span
                v-else-if="shop.stats?.place">{{
                shop.stats.city || 'Unknown City'
              }}, {{ shop.stats.place.state || 'Unknown State' }} - {{ shop.stats.place.zipcode || 'Unknown Zip' }}</span>
          </div>
          <div class="_form_label">Income</div>
          <div class="q-px-sm _py12">
            <span>{{ dollarString(incomes.total, '$', 0) }}</span>
          </div>
          <div class="_form_label">MAGI</div>
          <div class="q-px-sm _py12 alt-font">
            <span>{{ dollarString(incomes.magi, '$', 0) }}</span>
          </div>
          <div class="_form_label">Available PTC</div>
          <div class="q-px-sm _py12 alt-font">
            <span>{{ dollarString(shop.aptc, '$', 0) }}/mo</span>
          </div>
        </div>
        <div class="_f_l _f_chip">Coverage</div>
        <div class="__cov">
          <policy-card
              :place="place"
              :enrollment="enrollment"
              v-if="policy?.acaPlan"
              :mult="shop.mult"
              :aptc="shop.aptc"
              :model-value="policy"
              :subsidy="subsidy"
              :enrolled="[{ age: stats.age, relation: 'self' }, ...stats.people]"
          ></policy-card>
          <coverage-page
              v-else-if="policy?._id"
              limited
              :model-value="policy"
              :subsidy="subsidy"
              :enrollment="enrollment"
              :mult="shop.mult"
              :age="stats.age"
              :enrolled="[{ age: stats.age, relation: 'self' }, ...stats.people]"
          ></coverage-page>
        </div>
        <template v-if="plan._id">
          <div class="_f_l _f_chip">Enrollment</div>
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Status</div>
            <div class="q-pa-sm">
              <status-chip :model-value="enrollment.status"></status-chip>
            </div>
            <div class="_form_label">Elections</div>
            <div class="q-pa-sm">
              <income-summary v-bind="{ enrollment, person, plan }"></income-summary>
            </div>
          </div>
          <div class="_f_l _f_chip">Plan</div>
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Name</div>
            <div class="q-px-sm _py12">{{ plan.name }}</div>
            <div class="_form_label">Sponsor</div>
            <div class="q-pa-sm">
              <org-chip :model-value="plan.org"></org-chip>
            </div>
            <div class="_form_label">125 Allowance</div>
            <div class="q-px-sm _py12 alt-font">
              {{ dollarString(contributions.employer.cafe, '$', 2) }}
            </div>
            <div class="_form_label">Subsidies</div>
            <div class="q-px-sm _py12">
              <div class="row items-center" v-for="(id, i) in Object.keys(contributions.byCoverage || {})" :key="`c-${i}`">
                <q-chip color="ir-bg2">
                  <q-avatar class="q-mr-sm" v-if="byId.bySource[id]">
                    <img :src="byId.bySource[id].url">
                  </q-avatar>
                  <span>{{ contributions.byCoverage[id].name }}</span>
                </q-chip>
                <div>:&nbsp;</div>
                <div class="alt-font" v-if="(enrollment.coverages || {})[id]?.employerContribution">
                  <span v-if="enrollment.coverages[id].employerContribution.type === 'percent'">{{dollarString(enrollment.coverages[id].employerContributions[enrollment.type === 'family' ? 'family' : 'single'], '', 2)}}%</span>
                  <span v-else>{{dollarString(enrollment.coverages[id].employerContributions[enrollment.type === 'family' ? 'family' : 'single'], '$', 2)}}</span>
                </div>
                <div v-else>N/A</div>
              </div>
            </div>
          </div>
        </template>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="app">
        <universal-application v-bind="{ shop, person }"></universal-application>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="confirm">
        <confirm-enrollment :model-value="enrollment"></confirm-enrollment>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import HouseholdRow from 'components/market/household/HouseholdRow.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import StatusChip from 'components/enrollments/cards/StatusChip.vue';
  import IncomeSummary from 'components/households/cards/IncomeSummary.vue';
  import UniversalApplication from 'components/market/shop/utils/ua/UniversalApplication.vue';
  import {useAtcStore} from 'src/stores/atc-store';
  import PolicyCard from 'components/enrollments/ichra/cards/PolicyCard.vue';
  import CoveragePage from 'components/coverages/pages/CoveragePage.vue';
  import ConfirmEnrollment from 'components/hosts/enrollments/confirm/ConfirmEnrollment.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {usePlans} from 'stores/plans';
  import {useEnrollments} from 'stores/enrollments';
  import {$addressDisplay, $copyTextToClipboard, dollarString} from 'src/utils/global-methods';
  import {taxBreakdown} from 'components/households/utils/tax-tables';
  import {enrollmentContributions} from 'components/enrollments/utils';
  import {shopHousehold} from 'components/market/household/utils';
  import {shopGet} from 'components/market/utils/shop-get';
  import {manageFindUploads} from 'components/utils/uploads/file-manager';

  const planStore = usePlans();
  const eStore = useEnrollments();

  const props = defineProps({
    shopId: { required: true }
  })

  const tab = ref('summary')

  const { shop, hh, stats } = shopGet({ shop: computed(() => props.shopId)});

  const place = computed(() => shop.value.stats?.place)
  const { person, household, policy, loadPlans } = shopHousehold(shop, hh, { place})

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => shop.value.plan)
  ,
    useAtcStore
  })
  const { item: enrollment } = idGet({
    store: eStore,
    value: computed(() => shop.value.enrollment)
  ,
    useAtcStore
  })

  const incomes = computed(() => taxBreakdown(household.value, {}))

  const { contributions, c$ } = enrollmentContributions({ enrollment, plan, person, shop })

  const { byId } = manageFindUploads({ sources: c$, paths: ['carrierLogo']})

  const subsidy = computed(() => {
    return contributions.value.byCoverage[shop.value.plan_coverage]?.employer || 0
  })

  const loadId = computed(() => shop.value.policy || shop.value.coverage);
  watch(loadId, async (nv, ov) => {
    if (nv && nv !== ov) {
      // console.log('shop change', nv._id, ov?._id)
      loadPlans()
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>

  .__cov {
    padding: 10px;
    width: 100%;
    overflow-x: scroll;
  }
</style>
