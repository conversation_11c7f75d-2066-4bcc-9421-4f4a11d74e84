<template>
  <div class="_fw">

    <shops-table plan-enrollment @select="$router.push({ name: 'host-plan-shops', params: { shopId: $event._id }})" v-bind="{ params, host }"></shops-table>

    <common-dialog
        setting="right"
        :model-value="!!$route.params.shopId"
        @update:model-value="(val) => val ? '' : $router.push({ name: 'host-plan-shops' })">
      <div class="_fw q-pa-md">
        <shop-service v-if="$route.params.shopId" :shopId="$route.params.shopId"></shop-service>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import ShopsTable from 'components/market/shop/lists/ShopsTable.vue';
  import ShopService from 'components/hosts/enrollments/shops/cards/ShopService.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {idGet} from 'src/utils/id-get';
  import {useHosts} from 'stores/hosts';

  const hostStore = useHosts();

  const props = defineProps({
    statusFilter: {
      default: () => {
        return []
      }
    },
    planFilter: {
      default: () => {
        return {}
      }
    },
    activeYear: String,
    evt: String,
    personFilter: {
      default: () => {
        return {}
      }
    }
  })


  const hostId = computed(() => LocalStorage.getItem('host_id'))
  const { item: host } = idGet({
    store: hostStore,
    value: hostId
  ,
    useAtcStore
  })
  const params = computed(() => {
    const query = { $sort: { updatedAt: -1 } }
    if (props.planFilter?._id) query.plan = props.planFilter._id
    else {
      if(host.value.appDefault) query.$or = [{ host: hostId.value }, { host: { $exists: false }}]
      query.host = hostId.value
    }
    if (props.activeYear) query.planYear = props.activeYear
    if (props.evt) query.version = props.evt
    if (props.personFilter?._id) query.person = props.personFilter._id
    return {
      query
    }
  })
</script>

<style lang="scss" scoped>

</style>
