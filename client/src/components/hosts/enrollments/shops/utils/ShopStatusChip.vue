<template>
  <q-chip v-bind="{ clickable: true, color: 'ir-bg2', ...$attrs }">
    <q-avatar v-if="modelValue" :color="statuses[modelValue]?.color" size="15px"></q-avatar>
    <span class="q-ml-xs">{{modelValue ? statuses[modelValue]?.label : emptyLabel}}</span>
    <q-icon v-if="picker" class="q-ml-sm" name="mdi-menu-down"></q-icon>
    <slot name="side"></slot>
    <q-popup-proxy v-if="picker" v-model="popup">
      <div class="w300 mw100 q-pa-sm">
        <div v-if="!Object.keys(statuses).length" class="q-pa-sm font-7-8r">No status settings added. Add them in account misc settings.</div>
        <q-list separator>
          <q-item clickable @click="select(s)" v-for="(s, i) in Object.keys(statuses)" :key="`s-${i}`">
            <q-item-section avatar>
              <q-avatar size="15px" :color="statuses[s].color"></q-avatar>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{statuses[s].label}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-popup-proxy>
  </q-chip>

  <template v-if="picker && multiple">
    <q-chip v-bind="{ ...$attrs}" v-for="(s, i) in modelValue || []" :key="`s-${i}`">
      <q-avatar size="15px" :color="statuses[s]?.color"></q-avatar>
      <span class="q-mx-xs">{{statuses[s]?.label}}</span>
      <q-btn dense flat icon="mdi-close" color="red" size="sm" @click="select(s)"></q-btn>
    </q-chip>
  </template>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {useHosts} from 'stores/hosts';
  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useAtcStore} from 'src/stores/atc-store';

  const hostStore = useHosts();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    emptyLabel: { default: 'None' },
    host: { required: false },
    picker: Boolean,
    multiple: Boolean,
    modelValue: { required: true }
  })

  const popup = ref(false)

  const { item:fullHost } = idGet({
    store: hostStore,
    value: computed(() => props.host || LocalStorage.getItem('host_id'))
  ,
    useAtcStore
  })

  const statuses = computed(() => fullHost.value.shopStatuses || {})

  const select = (v) => {
    if(props.multiple){
      const arr = [...props.modelValue || []];
      const idx = arr.indexOf(v);
      if(idx > -1){
        arr.splice(idx, 1);
        emit('update:model-value', arr)
      } else {
        emit('update:model-value', [...arr, v])
        popup.value = false;
      }
    } else {
      emit('update:model-value', v);
      popup.value = false;
    }
  }
</script>

<style lang="scss" scoped>

</style>
