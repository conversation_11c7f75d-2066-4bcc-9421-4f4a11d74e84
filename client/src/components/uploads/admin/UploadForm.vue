<template>
  <div class="_fw q-pa-md">
    <div class="_form_grid">

      <div class="_form_label">File</div>
      <div class="q-px-md q-py-sm">
        <file-type-handler :file="form" :url="form?.url" height="150px" width="100px">
        </file-type-handler>
      </div>

      <div class="_form_label">Name</div>
      <div class="q-px-md q-py-sm">
        <div class="font-1r tw-five">{{ form?.originalname }}</div>
      </div>
      <div class="_form_label">Type</div>
      <div class="q-px-md q-py-sm">
        <div class="font-1r">{{ form?.info?.type }}</div>
      </div>

      <div class="_form_label">Size</div>
      <div class="q-px-md q-py-sm">
        <div class="font-1r">{{ fileSz }}</div>
      </div>

      <div class="_form_label">Usage</div>
      <div class="q-px-md q-py-sm">
        <usage-chip :model-value="form"></usage-chip>
      </div>

      <div class="_form_label">Link Expiry</div>
      <div class="q-px-md q-py-sm">
        <div class="flex items-center">
          <q-input style="width: 50px" :model-value="number" @update:model-value="inputNumber"></q-input>
          <q-chip color="white" :label="unit" icon-right="mdi-menu-down">
            <q-menu>
              <div class="w100 mw100 bg-white">
                <q-list separator>
                  <q-item v-for="(un, i) in Object.keys(units)" :key="`un-${i}`" @click="setUnit(un)" clickable>
                    <q-item-section>
                      <q-item-label>{{ un }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-menu>
          </q-chip>
        </div>
      </div>
    </div>
    <div class="row justify-end q-py-md">
      <q-btn label="save" color="black" icon-right="mdi-content-save" @click="save"></q-btn>
    </div>
  </div>
</template>

<script setup>
  import FileTypeHandler from 'components/common/uploads/file-types/fileTypeHandler.vue';
  import UsageChip from 'components/uploads/admin/UsageChip.vue';

  import {bytes} from 'src/components/common/uploads/utils'
  import {computed, ref, watch} from 'vue';
  import { useUploads  } from 'stores/uploads';

  const props = defineProps({
    modelValue: { required: false }
  });

  const fileSz = computed(() => bytes(form.value?.info?.size || 0));

  const formFn = (defs) => {
    return { ...defs }
  }

  const number = ref(0);
  const form = ref(formFn());
  const unit = ref('minutes');
  const units = computed(() => {
    const hours = 60*60;
    const days = 24*hours;
    const years = 365*24*60*60;
    return {
      seconds: 1,
      minutes: 60,
      hours,
      days,
      months:years/12,
      years
    }
  });

  const setUnit = (u) => {
    unit.value = u;
    number.value = form.value.expires / (units.value[u]);
  };
  const inputNumber = (val) => {
    number.value = val;
    form.value.expires = val * units.value[unit.value];
  }

  const save = () => {
    if(form.value.expires && form.value.expires > 60){
      useUploads().patch(props.modelValue._id, { expires: form.value.expires });
    }
  }

  const mv = computed(() => props.modelValue);
  watch(mv, (nv) => {
    form.value = formFn(nv);
    if(form.value.expires) number.value = form.value.expires / units.value[unit.value];
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
