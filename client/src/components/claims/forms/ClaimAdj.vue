<template>
  <div class="_fw">
    <div v-if="hasBeenAdj">
      <adj-chip :model-value="hasBeenAdj"></adj-chip>
    </div>
    <div class="__top alt-font">
      <q-expansion-item dark default-opened dense expand-icon="mdi-menu-down">
        <template v-slot:header>
          <q-item class="_fw">
            <q-item-section>
              <q-item-label class="text-a0 font-1r">Adjudicate Claim</q-item-label>
            </q-item-section>
          </q-item>
        </template>
        <div class="_fw __c">
          <div class="q-pb-sm">
            You are a claims administrator for {{ plan?.name || 'this plan' }}.
          </div>
          <div>Set amounts the plan will pay <b>without</b> considering deductibles and max OOP - these will be applied
            afterward
          </div>
        </div>
      </q-expansion-item>
    </div>
    <div class="row q-py-sm items-center">
      <q-chip :class="tab === 'lines' ? 'tw-six text-accent' : ''" dense color="transparent" clickable
              @click="tab='lines'" label="Line Items"></q-chip>
      <div>|</div>
      <q-chip :class="tab === 'splits' ? 'tw-six text-accent' : ''" dense color="transparent" clickable
              @click="tab='splits'" label="Payer Splits"></q-chip>
    </div>
    <q-tab-panels class="_panel" animated transition-next="jump-up" transition-prev="jump-down" v-model="tab">
      <q-tab-panel class="_panel" name="lines">
        <claims-table :visit="fullVisit" :claims="claims?.data">
<!--          <template v-slot:th-right>-->
<!--            Pay-->
<!--          </template>-->
          <template v-slot:header-right>
            <th class="__bb __h">Lowest
              <q-tooltip>Lowest price in network bundle</q-tooltip>
            </th>
            <th class="__bb __h">Pay</th>
          </template>
          <template v-slot:td-right="scope">
            <td class="__bb alt-font text-accent font-7-8r">
              {{dollarString((scope.item._pipeline?.lowestPrice || 0)/100 || 'N/A', '$', 2)}}
            </td>
            <td class="text-right __bb">
              <money-input
                  :id="`claim-${scope.index}`"
                  filled prefix="$"
                  :decimal="2"
                  dense
                  borderless
                  :model-value="((byId || {})[scope.item._id] || 0)/100"
                  @update:model-value="setById(scope.item._id, $event * 100)"
                  @focus="handleFocus(scope.index)"
              ></money-input>
            </td>
          </template>
        </claims-table>
        <div class="row justify-end items-center q-py-md">
          <div class="font-1r">Total to approve:&nbsp;&nbsp;</div>
          <div class="font-1-1-8r tw-six text-accent">{{ dollarString(payToday / 100, '$', 2) }}</div>
        </div>
        <div class="row justify-end q-pb-sm">
          <q-btn push @click="tab = 'splits'" flat no-caps>
            <span class="q-mr-sm">Payer Splits</span>
            <q-icon color="accent" name="mdi-chevron-right"></q-icon>
          </q-btn>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="splits">
        <div class="q-pa-sm tw-six font-7-8r text-grey-7">Deductible/Coinsurance Breakdown</div>
        <payment-history
            :selected="selected"
            :paid="paid"
            :enrollment="enrollment"
            :coverage="coverage"
            :visit="fullVisit"
            :claim="fullClaim"
        ></payment-history>

        <split-table
            v-bind="{
          amount,
          discounts,
          balance,
          hasBeenAdj,
          plan,
          provider,
          payToday,
         splits
        }"
        >
        </split-table>
        <div class="_form_grid _f_g_r q-py-md">
          <div class="_form_label">Balance Bill</div>
          <div class="q-pa-sm font-7-8r">
            <div v-if="!potentialBalance" class="text-italic">Balance fully adjudicated</div>
            <div v-else-if="fullVisit?.er">
              <div>🚑 This is an ER visit - federal regulations prohibit balance billing for providers for whom you
                don't have billing terms arranged (network pricing).<br><br>If this participant receives balance bills,
                advise them not to pay and reach out to your CommonCare host for help advising the provider of the law.
              </div>
            </div>
            <div v-else>
              <div>⚠️This leaves the potential for a balance bill to be sent to this participant for <b>{{dollarString(potentialBalance/100, '$', 2)}}</b>. That doesn't mean they have to pay it, but it's best to ask the provider to reduce the total to match what you're willing to pay.</div>
            </div>
          </div>
        </div>

        <div class="q-py-md row justify-end">
          <q-btn push no-caps class="_a_btn" label="Confirm" icon-right="mdi-check-circle">
            <q-popup-proxy>
              <div class="q-pa-lg bg-white">
                <div class="font-7-8r">Confirm and commit changes to plan claim</div>
                <div class="q-pt-md row justify-end">
                  <q-btn flat @click="confirm" no-caps>
                    <span class="q-mr-sm tw-six">Yes</span>
                    <q-icon name="mdi-check-circle" color="green"></q-icon>
                  </q-btn>
                </div>
              </div>
            </q-popup-proxy>
          </q-btn>
        </div>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import ClaimsTable from 'components/claims/lists/ClaimsTable.vue';
  import PaymentHistory from 'components/coverages/cards/PaymentHistory.vue';
  import SplitTable from 'components/claim-payments/cards/SplitTable.vue';
  import AdjChip from 'components/claims/cards/AdjChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useClaims} from 'stores/claims';
  import {tabHandler} from 'src/utils/tab-handler';
  import {dollarString} from 'src/utils/global-methods';
  import {loginPerson} from 'stores/utils/login';
  import {LocalStorage} from 'symbol-auth-client';
  import {useProviders} from 'stores/providers';
  const { login } = loginPerson()

  const claimStore = useClaims();
  const providerStore = useProviders();

  const emit = defineEmits(['update:claims', 'update:by-id']);
  const props = defineProps({
    plan: { required: true },
    visit: { required: false },
    claim: { required: true },
    coverage: { required: true },
    enrollment: { required: true },
    byId: Object,
    claims: Object,
    balance: Number,
    amount: Number,
    payToday:Number,
    paid:Number,
    selected: { required: false },
    hasBeenAdj: { required: true },
    splits: {
      default: () => {
        return {}
      }
    },
    discounts: {
      default: () => {
        return {}
      }
    }
  })

  const tab = ref('lines');


  const fullEnrollment = computed(() => props.enrollment);
  const fullVisit = computed(() => props.visit)
  const { item: fullClaim } = idGet({
    store: claimStore,
    value: computed(() => props.claim)
  ,
    useAtcStore
  })
  const { item: provider } = idGet({
    store: providerStore,
    value: computed(() => fullClaim.value?.provider || fullVisit.value?.provider || {,
    useAtcStore
  })
  })

  const setById = (path, total) => {
    const obj = {...props.byId || {}};
    obj[path] = total;
    emit('update:by-id', obj)
  }

  const { handleFocus } = tabHandler({ list: computed(() => props.claims?.data), prefix: 'claim' })


  const potentialBalance = computed(() => (props.balance || 0) - (props.payToday || 0))

  const confirm = async () => {
    const time = new Date();
    const fp = LocalStorage.getItem('fpId');
    const confirmOne = async (id, obj) => {
      return await claimStore.patch(id, {
        $set: {
          adj: {
            adjBy: login.value._id,
            adjAt: time,
            fp,
            enrollment: fullEnrollment.value._id,
            coverage: props.coverage?._id,
            ...obj
          }
        }
      }, { adj: true })
    }
    //reduce deductible an coinsurance by the extra amount the plan is willing to pay
    let ded = (props.splits.ded || 0) - 0;
    let coins = ded > 0 ? JSON.parse(JSON.stringify(props.splits.coins)) : Math.max(0, (props.splits.coins || 0) - props.splits.ded || 0);

    const getAdj = (clm) => {
      let b = clm.balance || 0;
      const claimDed = Math.min(b, ded);
      b -= claimDed;
      const claimCoins = Math.min(b, coins)
      b -= claimCoins;
      ded -= claimDed;
      coins -= claimCoins;
      const total = props.byId[clm._id];
      return {
        ded: claimDed,
        coins: claimCoins,
        amount: clm.amount - (clm.total - total),
        qty: clm.qty,
        total,
        preventive: clm.preventive
      };
    }
    if (fullClaim.value?._id) {
      const newClaim = await confirmOne(fullClaim.value._id, { ...getAdj(fullClaim.value) })
          .catch(err => console.error(`Error adjudicating claim - ${err.message}`))
      emit('update:claims', [newClaim])
    }
    else {
      const newClaims = await Promise.all(props.claims.data.map(a => confirmOne(a._id, getAdj(a))))
          .catch(err => console.error(`Error adjudicating claims - ${err.message}`))
      emit('update:claims', newClaims)
    }
  }
</script>

<style lang="scss" scoped>
  .__top {
    border-radius: 10px;
    background: linear-gradient(170deg, var(--q-accent), var(--q-a7));
    color: white;
    font-size: .8rem;
    font-weight: 600;

    .__c {
      padding: 0 25px 20px 25px;
    }
  }

  .__bb, .__h {
    text-align: left;
    font-weight: bold;
    border-bottom: solid .3px #999;
    padding: 3px 8px;
  }
  .__h {
    font-size: .75rem;
    color: var(--q-ir-grey-7);
  }
</style>
