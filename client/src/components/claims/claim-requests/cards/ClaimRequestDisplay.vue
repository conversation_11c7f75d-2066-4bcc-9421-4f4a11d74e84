<template>
  <div class="_fw">
    <div class="_form_grid">
      <div class="_form_label">Status</div>
      <div class="q-px-sm">
        <claim-request-status-chip :model-value="claim.status"></claim-request-status-chip>
      </div>
      <div class="_form_label">Billable<br>Item</div>
      <div class="q-px-sm flex items-center">
        <div class="tw-six text-grey-5 font-3-4r">{{ text.code }}:&nbsp;</div>
        <div class="font-3-4r">{{ text.name || 'N/A' }}</div>
      </div>
      <div class="_form_label">Provider</div>
      <div class="q-px-sm">
        <provider-chip v-if="claim.provider" :model-value="claim._fastjoin?.provider || claim.provider" empty-label="N/A"></provider-chip>
        <div v-else class="font-3-4r">N/A</div>
      </div>
      <template v-if="claim.practitioner">
        <div class="_form_label">Practitioner</div>
        <div class="q-pa-sm">
          <practitioner-chip :model-value="claim?.practitioner" empty-label="N/A"></practitioner-chip>

        </div>
      </template>
      <div class="_form_label">Charges</div>
      <div class="q-pa-sm">

        <table>
          <tr>
            <td>Amount</td>
            <td class="alt-font">{{ dollarString((claim?.amount || 0) / 100, '$', 2) }}</td>
          </tr>
          <tr>
            <td>Qty</td>
            <td class="alt-font">{{ dollarString(claim?.qty, '', 2) }}</td>
          </tr>
          <template v-if="priceObj.total !== priceObj.subtotal">
            <tr>
              <td>Subtotal</td>
              <td class="alt-font">{{ dollarString((priceObj.subtotal || 0) / 100, '$', 2) }}</td>
            </tr>
            <template v-if="claim?.taxes">
              <tr v-for="(k, i) in Object.keys(claim.taxes)" :key="`tax-${i}`">
                <td>{{ claim.taxes[k]?.name }}</td>
                <td class="alt-font">{{ dollarString((claim.taxes[k]?.amount || 0) / 100, '$', 2) }}</td>
              </tr>
            </template>

            <template v-if="claim?.fees">
              <tr v-for="(k, i) in Object.keys(claim.fees)" :key="`tax-${i}`">
                <td>{{ claim.fees[k]?.name }}</td>
                <td class="alt-font">{{ dollarString((claim.fees[k]?.amount || 0) / 100, '$', 2) }}</td>
              </tr>
            </template>

          </template>
          <tr>
            <td>Total</td>
            <td class="alt-font">{{ dollarString((priceObj.total || 0) / 100, '$', 2) }}</td>
          </tr>
          <tr>
            <td>Paid</td>
            <td class="alt-font">{{ dollarString((claim.paid?.amount || 0) / 100, '$', 2) }}</td>
          </tr>
        </table>

      </div>

      <div class="_form_label">Files</div>
      <div class="q-pa-sm">
        <div class="row items-center">
          <div class="col-shrink cursor-pointer" @click="filePrev = f" v-for="(f, i) in Object.keys(claim._fastjoin?.files?.files || {})" :key="`file-${i}`">
            <file-type-handler height="50px" width="40px" :file="claim._fastjoin.files.files[f]">
            </file-type-handler>
          </div>
        </div>
      </div>

      <template v-if="removable && claim.status === 'unopened'">
        <div class="_form_label">Delete</div>
        <div class="q-pa-sm">
          <remove-proxy-btn name="Bill Upload" label="Delete" @remove="removeCr"></remove-proxy-btn>
        </div>
      </template>
    </div>

    <common-dialog :model-value="!!filePrev" @update:model-value="val => !val ? filePrev = undefined : ''" setting="smmd">
      <file-preview :model-value="claim._fastjoin.files.files[filePrev]"></file-preview>
    </common-dialog>

  </div>
</template>

<script setup>
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import PractitionerChip from 'components/practitioners/cards/PractitionerChip.vue';
  import FilePreview from 'components/common/uploads/pages/FilePreview.vue';
  import ClaimRequestStatusChip from './ClaimRequestStatusChip.vue';
  import FileTypeHandler from 'components/common/uploads/file-types/fileTypeHandler.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {useClaimReqs} from 'stores/claim-reqs';
  import {computed, ref} from 'vue';
  import {claimPricing} from 'components/claims/utils';
  import {dollarString} from 'src/utils/global-methods';

  const crStore = useClaimReqs();

  const emit = defineEmits(['remove']);
  const props = defineProps({
    modelValue: { required: true },
    removable: Boolean
  })

  const { item: cr } = idGet({
    store: crStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })
  const claim = computed(() => {
    const { claimData = {}, _id, ...rest } = cr.value || {}
    return {
      ...rest,
      ...claimData
    }
  })

  const filePrev = ref();

  const text = computed(() => {
    const item = claim.value?._fastjoin?.med || claim.value?._fastjoin?.procedure;
    const { name, code, rxcui } = item || {}
    return { code: rxcui || code, name }
  })
  const { priceObj } = claimPricing(claim)

  const removeCr = () => {
    crStore.remove(cr.value._id)
        .catch(err => console.error(`Error removing upload: ${err.message}`));
    emit('remove')
  }
</script>

<style lang="scss" scoped>
  table {
    width: 100%;
    font-size: 1rem;
    border-collapse: collapse;

    td:first-child {
      font-size: .75rem;
      font-weight: bold;
    }

    td {
      padding: 5px 8px;
      border-bottom: solid .2px #999;
    }

    td:first-child {
      width: 20%
    }
  }
</style>
