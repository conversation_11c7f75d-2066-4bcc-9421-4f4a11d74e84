<template>
  <div class="_fw">
<!--    <div class="_f_l _f_chip">Payment Details</div>-->
    <div class="_form_grid _f_g_r">
      <div class="_form_label">Amount</div>
      <div class="q-pa-sm">
        <money-input dense input-class="tw-six alt-font font-1-1-4r" :decimal="2" prefix="$" :model-value="(form.amount || 0)/100" @update:model-value="form.amount = $event * 100"></money-input>
      </div>
      <div class="_form_label">Pay</div>
      <div class="q-pa-sm">
        <q-chip dark @click="form.amount = visitBalance" class="alt-font tw-six" color="p7" :flat="form.amount !== visitBalance" clickable :label="`Full Bill: ${dollarString((visitBalance || 0)/100, '$', 2)}`"></q-chip>
        <q-chip dark @click="form.amount = claimBalance" class="alt-font tw-six" color="p7" :flat="form.amount !== claimBalance" clickable :label="`Line Item: ${dollarString((claimBalance || 0) / 100, '$', 2)}`"></q-chip>
      </div>
    </div>
  </div>
</template>

<script setup>
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {useClaims} from 'stores/claims';
  import {useVisits} from 'stores/visits';
  import {computed, ref, watch} from 'vue';
  import {useClaimPayments} from 'stores/claimPayments';
  import {HForm} from 'src/utils/hForm';
  import {usePlans} from 'stores/plans';
  import {dollarString} from 'src/utils/global-methods';

  const claimStore = useClaims();
  const visitStore = useVisits();
  const cpStore = useClaimPayments();
  const planStore = usePlans()

  const props = defineProps({
    claim: { required: false },
    visit: { required: true },
    modelValue: { required: false }
  })

  const selectedClaim = ref({})

  const { item: fullClaim } = idGet({
    store: claimStore,
    value: computed(() => props.claim || selectedClaim.value)
  ,
    useAtcStore
  })
  const claimBalance = computed(() => {
    const { total, balance, amount, qty} = fullClaim.value || {}
    return balance || balance === 0 ? balance : total || amount * qty
  })
  const { item: fullVisit } = idGet({
    store: visitStore,
    value: computed(() => props.visit || fullClaim.value?.visit)
  ,
    useAtcStore
  })
  const visitBalance = computed(() => {
    return Math.max(fullVisit.value?.balance || 0, claimBalance.value)
  })
  const { item: cp } = idGet({
    store: cpStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })
  const { item:plan } = idGet({
    store: planStore,
    value: computed(() => fullVisit.value?.plan)
  ,
    useAtcStore
  })

  const { form, save } = HForm({
    store: cpStore,
    value: cp,
    beforeFn: (val) => {
      const { _id, plan, person, patient, provider} = fullVisit.value;
      const { org } = fullPlan.value;
      const { _id:claimId } = fullClaim.value;
      return {
        visit:_id,
        plan,
        person,
        patient,
        provider,
        org,
        claim:claimId,
        ...val
      }
    }
  })

  watch(fullClaim, (nv) => {
    if(nv?._id && !form.value.amount){
      form.value.amount = nv.balance || ((nv.amount || 0) * (nv.qty || 0) - (nv.paid?.amount || 0));
    }
  }, {immediate: true})
  watch(fullVisit, (nv) => {
    if(nv?._id && !form.value.amount){
      setTimeout(() => {
        form.value.amount = nv.balance || 0
      }, 250)
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
