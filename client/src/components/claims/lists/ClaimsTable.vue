<template>
  <div class="__ct">
    <table>
      <tr>
        <th>Code</th>
        <th>Description</th>
        <th>Billed</th>
        <th>Balance</th>
        <th v-if="$slots['th-right']">
          <slot name="th-right"></slot>
        </th>
        <slot name="header-right"></slot>
      </tr>
      <tr class="cursor-pointer" v-for="(claim, i) in useClaims" :key="`claim-${i}`" @click="emit('select', claim)">
        <td class="text-grey-7 tw-six font-3-4r">{{ claim._code }}</td>
        <td>{{ $limitStr(claim._name, 70, '...') }}</td>
        <td class="alt-font tw-six text-black">
          {{ dollarString((claim.amount || (getClaimPrice(claim).total || 0)) / 100, '$', 2) }}
        </td>
        <td class="alt-font tw-six text-p6">
          {{ dollarString((claim.balance || (getClaimPrice(claim).total || 0)) / 100, '$', 2) }}
        </td>
        <slot name="td-right" :item="claim" :index="i"></slot>
      </tr>
      <template v-if="!useClaims.length">
        <slot name="empty">
          <tr class="font-3-4r text-italic">
            <td></td>
            <td>No claims from this visit</td>
            <td></td>
          </tr>
        </slot>
      </template>
    </table>

  </div>
</template>

<script setup>
  import {HFind} from 'src/utils/hFind';
  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useClaims as useClaimStore} from 'stores/claims';
  import {useVisits} from 'stores/visits';
  import {getClaimPrice} from '../utils';
  import {$limitStr, dollarString} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';

  const claimsStore = useClaimStore();
  const visitStore = useVisits();

  const emit = defineEmits(['select']);
  const props = defineProps({
    visit: { required: true },
    ids: { type: Array },
    claims: Array,
    sync: Boolean
  })

  const { item: fullVisit } = idGet({
    store: visitStore,
    value: computed(() => props.visit)
  ,
    useAtcStore
  })
  const syncClaims = ref(false);
  watch(fullVisit, (nv) => {
    if (nv && props.sync) {
      syncClaims.value = !nv.balanceSyncedAt || ((new Date(nv.updatedAt || new Date()).getTime() - new Date(nv.balanceSyncedAt).getTime()) > (1000 * 60))
    }
  }, { immediate: true })

  const claimIds = computed(() => props.ids || fullVisit.value?.claims || []);
  const { h$ } = HFind({
    store: claimsStore,
    pause: computed(() => props.claims?.length || (!fullVisit.value || fullVisit.value._fastjoin?.claims)),
    limit: computed(() => claimIds.value.length),
    params: computed(() => {
      return {
        banking: { sync_claims_payments: syncClaims.value },
        query: { _id: { $in: claimIds.value } }
      }
    })
  })

  const getText = (claim) => {
    const item = claim?._fastjoin?.med || claim?._fastjoin?.procedure;
    const { layName, name, code, rxcui } = item || {}
    return { ...claim, _code: rxcui || code, _name: layName || name }
  }
  const useClaims = computed(() => [...(props.claims || ((fullVisit.value?._fastjoin?.claims || h$.data)))].map(a => getText(a)))


</script>

<style lang="scss" scoped>
  .__ct {
    width: 100%;

    table {
      width: 100%;
      border-collapse: collapse;

      tr {
        cursor: pointer;
        background: transparent;
        transition: all .3s;

        &:hover {
          background: var(--q-p0);
        }

        th {
          font-size: .75rem;
          font-weight: bold;
          color: var(--q-ir-grey-7);
        }

        td, th {
          text-align: left;
          padding: 5px 8px;
          border-bottom: solid .3px #999;
        }

        td:first-child {
          width: 1%;
        }

        td:last-child {
          width: 20%;
        }
      }

      tr:first-child {
        &:hover {
          background: transparent;
        }
      }

    }
  }
</style>
