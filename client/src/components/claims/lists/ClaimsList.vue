<template>
  <div class="_fw">
    <q-chip clickable @click="toggle" v-if="adding" color="transparent">
      <span v-if="!isOpen">New Line Item</span>
      <span v-else>Close</span>
      <q-icon v-if="!isOpen" name="mdi-plus" color="primary" class="q-ml-sm"></q-icon>
      <q-icon v-else name="mdi-close" color="red" class="q-ml-sm"></q-icon>
    </q-chip>

    <div class="q-pa-sm">
      <q-tab-panels class="_panel" :model-value="isOpen ? 'form' : 'list'" animated transition-next="jump-up"
                    transition-prev="jump-down">
        <q-tab-panel class="_panel" name="list">
          <claims-table :sync="sync" :visit="fullVisit" @select="setEditing"></claims-table>
        </q-tab-panel>
        <q-tab-panel class="_panel" name="form">
          <q-tab-panels animated class="_panel" :model-value="tableLayer && editing?._id ? tab : 'form'">

            <q-tab-panel class="_panel" name="table">
              <div class="row">
                <q-btn dense flat size="sm" color="accent"  @click="close">
                  <q-icon color="accent" name="mdi-chevron-left" size="25px"></q-icon>
                </q-btn>
                <q-space></q-space>
                <q-btn dense flat size="sm" color="accent" @click="tab = 'form'" icon="mdi-pencil-box"></q-btn>
              </div>
              <claim-table :sync="sync" :model-value="editing"></claim-table>
            </q-tab-panel>
            <q-tab-panel class="_panel" name="form">
              <div v-if="tableLayer" class="row">
                <q-btn v-if="editing?._id" dense flat size="sm" color="accent"  @click="tab = 'table'">
                  <q-icon color="accent" name="mdi-chevron-left" size="25px"></q-icon>
                </q-btn>
              </div>
              <claims-form
                  @close="close"
                  :visit="fullVisit"
                  :model-value="editing"
                  @update:model-value="close"
              ></claims-form>
            </q-tab-panel>

          </q-tab-panels>


        </q-tab-panel>
      </q-tab-panels>
    </div>


  </div>
</template>

<script setup>
  import ClaimsForm from 'components/claims/forms/ClaimsForm.vue';
  import ClaimsTable from 'components/claims/lists/ClaimsTable.vue';
  import ClaimTable from 'components/claims/lists/ClaimTable.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useVisits} from 'stores/visits';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useClaims} from 'stores/claims';

  const visitStore = useVisits();
  const claimStore = useClaims();

  const emit = defineEmits(['select'])
  const props = defineProps({
    tableLayer: Boolean,
    adding: Boolean,
    visit: { required: true },
    claimIn: { required: false },
    sync: Boolean
  })

  const tab = ref('table')
  const dialog = ref(false)
  const editing = ref(undefined);

  watch(() => props.claimIn, (nv) => {
    if(nv) {
      editing.value = nv;
      tab.value = 'form'
    }
  }, { immediate: true })

  const close = () => {
    dialog.value = false
    editing.value = undefined
    tab.value = 'table'
  }

  const isOpen = computed(() => dialog.value || !!editing.value);

  const toggle = () => {
    if (isOpen.value) {
      dialog.value = false;
      editing.value = undefined
    } else dialog.value = true;
  }

  const { item: fullVisit } = idGet({
    store: visitStore,
    value: computed(() => props.visit)
  ,
    useAtcStore
  })

  const setEditing = async (value) => {
    let v = claimStore.getFromStore(value._id)?.value
    if (!v) v = await claimStore.get(value._id)

    editing.value = v
    emit('select', v)
  }

</script>

<style lang="scss" scoped>

</style>
