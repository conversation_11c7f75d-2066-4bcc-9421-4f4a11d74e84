<template>
  <table>
    <tr>
      <td>Seen By</td>
      <td>
        <span v-if="!claim?.practitioner" class="text-italic">Not Listed</span>
        <practitioner-chip v-else :model-value="claim.practitioner"></practitioner-chip>
      </td>
    </tr>
    <tr>
      <td>Code</td>
      <td>{{text.code}}</td>
    </tr>
    <tr>
      <td>Name</td>
      <td>{{text.name}}</td>
    </tr>
    <tr v-if="text.layName">
      <td>Desc.</td>
      <td>{{text.layName}}</td>
    </tr>
    <tr>
      <td>Date</td>
      <td class="alt-font">{{formatDate(claim?.date, 'MM/DD/YYYY')}}</td>
    </tr>
    <tr>
      <td>Amount</td>
      <td class="alt-font">{{ dollarString((claim?.amount || 0) / 100, '$', 2) }}</td>
    </tr>
    <tr>
      <td>Qty</td>
      <td class="alt-font">{{ dollarString(claim?.qty, '', 2) }}</td>
    </tr>
    <template v-if="claim?.taxes">
      <tr v-for="(k, i) in Object.keys(claim.taxes)" :key="`tax-${i}`">
        <td>{{ claim.taxes[k]?.name }}</td>
        <td class="alt-font">{{ dollarString((claim.taxes[k]?.amount || 0) / 100, '$', 2) }}</td>
      </tr>
    </template>
    <template v-if="claim?.fees">
      <tr v-for="(k, i) in Object.keys(claim.fees)" :key="`tax-${i}`">
        <td>{{ claim.fees[k]?.name }}</td>
        <td class="alt-font">{{ dollarString((claim.fees[k]?.amount || 0) / 100, '$', 2) }}</td>
      </tr>
    </template>
    <tr class="tw-six text-accent">
      <td>Total</td>
      <td class="alt-font">{{ dollarString((priceObj.total || 0) / 100, '$', 2) }}</td>
    </tr>
    <tr class="tw-six text-primary">
      <td>Paid</td>
      <td class="alt-font">{{ dollarString((claim.paid?.amount || 0) / 100, '$', 2) }}</td>
    </tr>
  </table>
</template>

<script setup>
  import PractitionerChip from 'components/practitioners/cards/PractitionerChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useClaims} from 'stores/claims';
  import {dollarString} from 'src/utils/global-methods';
  import {formatDate} from 'src/utils/date-utils';
  import {claimPricing} from 'components/claims/utils';

  const store = useClaims();
  const props = defineProps({
    modelValue: { required: true}
  })

  const { item:claim } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const { priceObj } = claimPricing(claim)

  const text = computed(() => {
    const item = claim.value?._fastjoin?.med || claim.value?._fastjoin?.procedure;
    const { name, code, rxcui, layName } = item || {}
    return { code: rxcui || code, name, layName }
  })
</script>

<style lang="scss" scoped>
  table {
    width: 100%;
    border-collapse: collapse;

    tr {
      td:first-child {
        font-size: .8rem;
        font-weight: bold;
        color: #999;
      }
      td {
        padding: 5px 10px;
        border-bottom: solid .3px #999;
      }
    }
  }
</style>
