<template>
  <div class="_fw">
    <div class="row">
      <div class="col-12 col-md-6 q-pa-sm">
        <div class="__c">
          <div class="__t">Provider</div>
          <provider-item :model-value="claim.provider"></provider-item>

          <template v-if="practitioner?._id">
            <div class="q-pa-sm tw-six">Seen By:</div>
            <practitioner-item :model-value="practitioner"></practitioner-item>
          </template>

        </div>
      </div>
      <div class="col-12 col-md-6 q-pa-sm">
        <div class="__c">
          <div class="__t">Claim Details</div>
          <claim-table :model-value="claim"></claim-table>
        </div>
        <div class="__c" v-if="otherClaims.total">
          <div class="__t">Related Claims</div>
          <claim-table v-for="(oc, i) in otherClaims.data" :key="`oc-${i}`" :model-value="oc"></claim-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';
  import PractitionerItem from 'components/practitioners/cards/PractitionerItem.vue';
  import ClaimTable from 'components/claims/lists/ClaimTable.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {useClaims} from 'stores/claims';
  import {computed} from 'vue';
  import {useRoute} from 'vue-router';
  import {useVisits} from 'stores/visits';
  import {HFind} from 'src/utils/hFind';

  import {usePractitioners} from 'stores/practitioners';

  const route = useRoute();
  const claimStore = useClaims();
  const visitStore = useVisits();
  const practitionerStore = usePractitioners()

  const { item: claim } = idGet({
    store: claimStore,
    value: computed(() => route.params.claimId)
  ,
    useAtcStore
  })
  const { item: visit } = idGet({
    store: visitStore,
    value: computed(() => claim.value?.visit)
  ,
    useAtcStore
  })
  const { item: practitioner } = idGet({
    store: practitionerStore,
    value: computed(() => claim.value?.practitioner)
  ,
    useAtcStore
  })

  const { h$: otherClaims } = HFind({
    store: claimStore,
    pause: computed(() => (visit.value?.claims || []).length < 2),
    limit: computed(() => visit.value?.claims?.length),
    params: computed(() => {
      return {
        query: {
          _id: { $ne: claim.value?._id, $in: visit.value?.claims || [] }
        }
      }
    })
  })
</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 15px;
    box-shadow: 0 2px 12px -4px #999;
    padding: 30px 10px 20px 10px;
    position: relative;
    margin: 30px 0;
    background: white;

    .__t {
      position: absolute;
      top: 0;
      left: 5%;
      transform: translate(0, -30%);
      border-radius: 6px;
      background: linear-gradient(170deg, var(--q-p7), var(--q-primary));
      color: white;
      font-weight: 600;
      padding: 6px 8px;
    }
  }
</style>
