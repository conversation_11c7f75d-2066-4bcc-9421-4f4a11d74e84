<template>
  <div class="_fw">
    <div class="q-pa-sm font-3-4r tw-six">Adjudication Record</div>
    <div class="_form_grid">
      <div class="_form_label">By</div>
      <div class="q-pa-sm">
        <default-chip :model-value="by?._fastjoin?.owner || by" :use-atc-store="useAtcStore"></default-chip>
      </div>
      <div class="_form_label">At</div>
      <div class="q-pa-sm">
        <q-chip color="transparent" class="tw-six">{{ $ago(mv.adjAt) }}</q-chip>
      </div>
      <div class="_form_label">Preventive</div>
      <div class="q-pa-sm">
        <q-checkbox :model-value="mv.preventive" :label="mv.preventive ? 'Yes' : 'No'"></q-checkbox>
      </div>
      <div class="_form_label">Total Approved</div>
      <div class="q-pa-sm">
        <q-chip color="transparent" class="tw-six">
          {{ dollarString((mv.total || 0)/100, '$', 0) }}
        </q-chip>
      </div>
      <div class="_form_label">Deductible</div>
      <div class="q-pa-sm">
        <q-chip color="transparent" class="tw-six">
          {{ dollarString((mv.ded || 0)/100, '$', 0) }}
        </q-chip>
      </div>
      <div class="_form_label">Coinsurance</div>
      <div class="q-pa-sm">
        <q-chip color="transparent" class="tw-six">
          {{ dollarString((mv.coins || 0)/100, '$', 0) }}
        </q-chip>
      </div>
      <div class="_form_label">Coverage</div>
      <div class="q-pa-sm">
        <coverage-item :model-value="mv.coverage">
          <template v-slot:side></template>
        </coverage-item>
      </div>
      <div class="_form_label">Notes</div>
      <div class="q-pa-sm">
        <div class="font-7-8r">{{mv.notes || 'N/A'}}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import CoverageItem from 'components/coverages/cards/CoverageItem.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {$ago, dollarString} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';
  import {useLogins} from 'stores/logins';

  const loginStore = useLogins();

  const props = defineProps({
    modelValue: Object
  })

  const mv = computed(() => props.modelValue || {});

  const { item:by } = idGet({
    store: loginStore,
    value: computed(() => mv.value?.adjBy),
    params: ref({ login_person: true ,
    useAtcStore
  })
  })

</script>

<style lang="scss" scoped>

</style>
