<template>
  <div class="__cc">
    <div class="__r text-grey-8 font-3-4r">{{ text.code }}</div>
    <div class="font-7-8r __r __b">{{ $limitStr(text.name, 100, '...') }}</div>
    <div class="font-1r tw-six text-p6 __r">{{ dollarString(priceObj.total / 100, '$', 2) }}</div>
    <q-tooltip>
      <claims-card class="tw-six" :model-value="claim"></claims-card>
    </q-tooltip>
  </div>
</template>

<script setup>
  import ClaimsCard from './ClaimsCard.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useClaims} from 'stores/claims';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {claimPricing} from 'components/claims/utils';
  import {$limitStr, dollarString} from 'src/utils/global-methods';

  const store = useClaims();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: claim } = idGet({
    store,
    value: computed(() => props.modelValue),
    refreshWhen: computed(() => !props.modelValue?._fastjoin?.procedure && !props.modelValue?._fastjoin?.med)
  ,
    useAtcStore
  })

  const text = computed(() => {
    const item = claim.value?._fastjoin?.med || claim.value?._fastjoin?.procedure;
    const { layName, name, code, rxcui } = item || {}
    return { code: rxcui || code, name: layName || name }
  })

  const { priceObj } = claimPricing(claim)

</script>

<style lang="scss" scoped>
  .__cc {
    display: grid;
    grid-template-rows: auto;
    grid-template-columns: auto 1fr auto;
    align-items: center;
  }

  .__r {
    padding: 5px 8px;
  }

  .__b {
    border-left: solid .3px #999;
    border-bottom: solid .3px #999;
  }
</style>
