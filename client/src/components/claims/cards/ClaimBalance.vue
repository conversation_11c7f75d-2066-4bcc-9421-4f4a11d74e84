<template>
  <q-chip v-bind="{ class: 'alt-font', color: 'transparent', ...$attrs }">
    <q-icon name="mdi-currency-usd" color="primary"></q-icon>
    <span class="tw-six">{{dollarString(priceObj.total / 100, '', 2)}}</span>
    <q-tooltip>
      <claims-card dark class="tw-six" :model-value="claim"></claims-card>
    </q-tooltip>
  </q-chip>
</template>

<script setup>
  import ClaimsCard from './ClaimsCard.vue';
  import {useClaims} from 'stores/claims';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {claimPricing} from 'components/claims/utils';
  import {dollarString} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useClaims();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: claim } = idGet({
    store,
    value: computed(() => props.modelValue),
    refreshWhen: computed(() => !props.modelValue?._fastjoin?.procedure && !props.modelValue?._fastjoin?.med)
  ,
    useAtcStore
  })

  const { priceObj } = claimPricing(claim)

</script>

<style lang="scss" scoped>

</style>
