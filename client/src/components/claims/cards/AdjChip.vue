<template>
  <q-chip v-bind="{color: 'transparent', clickable: true, ...$attrs}">
    <span class="font-3-4r">
      Adjudicated {{ $ago(mv.adjAt) }} by {{by?._fastjoin?.owner?.name || by?.name}}
    </span>

    <q-popup-proxy>
      <div class="w400 mw100 q-pa-md bg-white">
        <div class="row _fw no-wrap __top">
          <q-chip clickable @click="active = i" v-for="(entry, i) in history || []" :key="`historical-${i}`">
            <span class="font-3-4r">{{$ago(entry.adjAt)}}</span>
          </q-chip>
        </div>
        <div class="row justify-end q-py-xs" v-if="active > -1">
          <q-btn size="sm" color="red" icon="mdi-close" @click="active = -1"></q-btn>
        </div>

        <adj-history :model-value="mv"></adj-history>
      </div>
    </q-popup-proxy>
  </q-chip>
</template>

<script setup>
  import AdjHistory from 'components/claims/cards/AdjHistory.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {$ago} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useLogins} from 'stores/logins';

  const loginStore = useLogins();

  const props = defineProps({
    modelValue: Object,
    history: Array
  })

  const active = ref(-1);

  const mv = computed(() => {
    if(active.value > -1) return props.history[active.value] || {};
    else return props.modelValue || {}
  })

  const { item:by } = idGet({
    store: loginStore,
    value: computed(() => props.modelValue?.adjBy),
    params: ref({ login_person: true ,
    useAtcStore
  })
  })
</script>

<style lang="scss" scoped>
.__top {
  overflow-x:scroll;
}
</style>
