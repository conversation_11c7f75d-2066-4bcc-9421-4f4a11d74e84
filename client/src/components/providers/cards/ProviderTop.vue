<template>
  <div class="__top text-ir-textp">
    <div class="row justify-center">
      <div class="_cent">
        <div class="flex items-center" v-if="p$.total">
          <div class="tw-six font-1-1-8r">{{ provider.dba || provider.name || 'Select Provider' }}</div>
          <q-btn dense flat icon="mdi-menu-down" color="s12"></q-btn>
          <q-popup-proxy :model-value="!dialogOff" @update:model-value="dialogOff = !$event">
            <div class="w300 mw100 bg-white q-pa-sm">
              <q-list>
                <q-item clickable @click="$router.push({ name: 'add-provider' })">
                  <q-item-section avatar>
                    <q-icon name="mdi-plus" color="primary"></q-icon>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>New Account</q-item-label>
                  </q-item-section>
                </q-item>
                <default-item
                    @update:model-value="setProvider(p._id)"
                    v-for="(p, i) in p$.data"
                    :key="`p-${i}`"
                    :model-value="p"
                    name-path="name"
                    backup-name-path="legalName"
                    :use-atc-store="useAtcStore"
                ></default-item>
              </q-list>
            </div>
          </q-popup-proxy>
        </div>
        <div v-else>
          <q-chip text-color="white" color="transparent" clickable @click="openSetup">
            <span class="tw-six q-mr-sm">Setup your provider account</span>
            <q-icon name="mdi-cog"></q-icon>
          </q-chip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useOrgs} from 'stores/orgs';
  import {useAtcStore} from 'src/stores/atc-store';
  import {HFind} from 'src/utils/hFind';
  import {useProviders} from 'stores/providers';
  import {useRouter} from 'vue-router';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const router = useRouter();

  const orgStore = useOrgs();
  const providerStore = useProviders();
  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const emit = defineEmits(['update:providerId']);
  const { item: org } = idGet({
    store: orgStore,
    value:getOrgId
  ,
    useAtcStore
  })

  const dialogOff = ref(true);
  const { h$: p$ } = HFind({
    store: providerStore,
    limit: computed(() => dialogOff.value ? 1 : 10),
    params: computed(() => {
      return {
        runJoin: { provider_avatar: true },
        query: {
          org: org.value._id
        }
      }
    })
  })
  const providerId = computed(() => LocalStorage.getItem('provider_id') || p$.data[0]?._id)

  const { item: provider } = idGet({
    store: providerStore,
    value: providerId
  ,
    useAtcStore
  })

  const setProvider = (id) => {
    if (id) {
      LocalStorage.setItem('provider_id', id);
      emit('update:providerId', id)
    }
  }


  const openSetup = () => {
    router.push({ name: 'provider-setup'})
  }

</script>

<style lang="scss" scoped>
  .__top {
    width: 100%;
    padding: 2vh 3vw;
    background: var(--q-ir-grey-2);
    color: var(--q-s12);
  }
</style>
