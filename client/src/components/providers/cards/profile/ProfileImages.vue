<template>
  <div class="_fw">
    <div class="_form_grid">
      <div class="_form_label">Logo</div>
      <div class="q-pa-sm">
        <image-select-or-upload :model-value="provider.avatar" @update:model-value="addAvatar"></image-select-or-upload>
      </div>
      <div class="_form_label">Images</div>
      <div class="q-pa-sm">
        <image-form height="30px" width="130px" @update:model-value="addImage" multiple :model-value="provider.images" :use-atc-store="useAtcStore"></image-form>
        <div class="row items-center q-pt-sm" v-if="!reloading">
          <div class="__img" v-for="(img, i) in provider.images || []" :key="`image-${i}`">
            <q-btn class="t-r-a" size="xs" flat round color="red" icon="mdi-close" dark @click="remove(i)"></q-btn>
            <q-img fit="cover" class="_fa" :src="getFile({ obj: provider, path: ['images'], index: i })"></q-img>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import ImageForm from 'components/common/uploads/images/ImageForm.vue';

  import {computed, ref} from 'vue';
  import {getFile} from 'src/utils/fs-utils';
  import {useProviders} from 'stores/providers';
  import ImageSelectOrUpload from 'components/common/uploads/images/ImageSelectOrUpload.vue';
  import {useAtcStore} from 'src/stores/atc-store';
  const pStore = useProviders();

  const props = defineProps({
    modelValue: { required: true }
  })

  const provider = computed(() => props.modelValue || {});
  const reloading = ref(false);

  const remove = (i) => {
    const { _id, images = [] } = provider.value;
    const list = [...images];
    list.splice(i, 1);
    pStore.patchInStore(_id, { images: list })
    pStore.patch(_id, { images: list })
  }
  const addAvatar = (val) => {
    const id = provider.value._id;
    pStore.patchInStore(id, { avatar: val });
    pStore.patch(id, { avatar: val })
  }
  const addImage = async (val) => {
    const { _id, images = [] } = provider.value;
    const list = [...val, ...images]
    pStore.patchInStore(_id, { images: list });
    reloading.value = true;
    await pStore.patch(_id, { images: list })
    reloading.value = false;
  }
</script>

<style lang="scss" scoped>
.__img {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 5px;
  padding: 2.5px;
}
</style>
