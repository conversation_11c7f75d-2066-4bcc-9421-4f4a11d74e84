<template>
  <q-item @click="dialog = !dialog" v-bind="{ clickable: true, dense: true, ...$attrs}">
    <slot name="avatar">
      <q-item-section avatar>
        <q-icon name="mdi-plus" color="primary"></q-icon>
      </q-item-section>
    </slot>
    <q-item-section>
      <q-item-label>{{label}}</q-item-label>
    </q-item-section>
    <slot name="side"></slot>

    <common-dialog setting="large" v-model="dialog">
      <provider-search :model-value="provider" @update:model-value="emitUp"></provider-search>
    </common-dialog>
  </q-item>
</template>

<script setup>
  import ProviderSearch from 'components/providers/forms/ProviderSearch.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';

  import {useProviders} from 'stores/providers';
  const store = useProviders();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    label: { default: 'Add Provider', type: String },
    emitValue: Boolean
  })

  const { item:provider } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const dialog = ref(false);
  const emitUp = (val) => {
    if(props.emitValue) emit('update:model-value', val._id);
    else emit('update:model-value', val);
  }
</script>

<style lang="scss" scoped>

</style>
