<template>
  <div class="_fw">
    <div class="__t">{{pb?.name}}</div>
    <div class="__s">{{$limitStr(pb?.description, 50, '...')}}</div>
    <q-separator class="q-my-xs"></q-separator>
    <provider-chip :model-value="pb.provider"></provider-chip>
    <q-chip v-if="pb?.price" color="p1">
      <span class="alt-font">{{dollarString((pb.price || 0)/100, '$', 2)}}</span>
    </q-chip>
  </div>
</template>

<script setup>
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {useBundles} from 'stores/bundles';
  import {computed} from 'vue';
  import {$limitStr, dollarString} from 'src/utils/global-methods';

  const store = useBundles();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item:pb } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

</script>

<style lang="scss" scoped>
  .__t {
    font-weight: 600;
    font-size: 1rem;
    color: #424242
  }
  .__s {
    font-size: .8rem;
    color: #424242;
  }

</style>
