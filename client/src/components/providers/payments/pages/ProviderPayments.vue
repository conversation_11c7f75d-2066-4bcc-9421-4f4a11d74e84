<template>
  <q-page>
    <div class="q-py-md">
      <org-top title="Payments Settings" :org="org"></org-top>
    </div>
    <div class="row justify-center">
      <div class="_cent pd2 pw1">
        <div class="q-pb-md q-px-sm font-1r">
          Accept card and ach payments directly on CommonCare
        </div>
        <div class="row">
          <div class="col-12 col-md-6 q-pa-sm">
            <div class="__c">
              <div class="__title">Card Payments</div>
                <div class="_form_grid">
                  <div class="_form_label">Fees</div>
                  <div class="q-pa-sm">
                    <span class="tw-six text-primary font-1-1-4r">3% + 30&cent;</span>
                  </div>
                  <div class="_form_label">Status</div>
                  <div class="q-pa-sm">

                    <div v-if="loading" class="q-pa-md">
                      <q-spinner size="30px" color="primary"></q-spinner>
                    </div>
                    <div v-else-if="cardStatus === 'active'">
                      <q-chip color="transparent">
                        <q-icon color="green" name="mdi-check-circle"></q-icon>
                        <span class="q-ml-sm">Card Payments Active</span>
                      </q-chip>
                    </div>
                    <div v-else-if="cardStatus === 'pending'">
                      <q-chip color="transparent">
                        <q-icon color="accent" name="mdi-dots-horizontal"></q-icon>
                        <span class="q-ml-sm">Card Payments Approval Pending</span>
                      </q-chip>
                    </div>
                    <div v-else>
                      <q-chip color="transparent" clickable @click="requestCardPayments">
                        <q-icon name="mdi-hand" color="primary"></q-icon>
                        <span class="q-ml-sm">Activate Card Payments</span>
                      </q-chip>
                    </div>
                  </div>
                </div>

            </div>
          </div>
          <div class="col-12 col-md-6 q-pa-sm">
            <div class="__c">
              <div class="__title">Bank Payments</div>

              <div class="q-pa-sm">
                <div class="_form_grid">
                  <div class="_form_label">Fees</div>
                  <div class="q-pa-sm">
                    <table>
                      <tr>
                        <td>Inbound ACH</td>
                        <td>Free</td>
                      </tr>
                      <tr>
                        <td>Inbound Wire</td>
                        <td>$2</td>
                      </tr>
                      <tr>
                        <td>Outbound Transfers</td>
                        <td>50&cent;</td>
                      </tr>
                      <tr>
                        <td>Outbound Domestic Wires</td>
                        <td>$10</td>
                      </tr>
                      <tr>
                        <td>ACH Returns</td>
                        <td>$5</td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
              <q-tab-panels animated v-if="acctList.length" class="_panel" v-model="defTab">
                <q-tab-panel class="_panel" name="list">
                  <q-item-label header>Default ACH Account
                    <q-btn flat size="sm" class="q-mb-xs" color="primary" icon="mdi-plus"
                           @click="defTab = 'form'"></q-btn>
                  </q-item-label>
                  <account-item v-if="provider.payment_settings.default_ach"
                                :model-value="provider.payment_settings.default_ach">
                    <template v-slot:side>
                      <q-btn dense flat size="sm" name="mdi-pencil-box" @click="defTab = 'form'"></q-btn>
                    </template>
                  </account-item>
                </q-tab-panel>
                <q-tab-panel class="_panel" name="form">
                  <div class="row">
                    <q-btn dense flat icon="mdi-chevron-left" color="primary" @click="defTab = 'list'"></q-btn>
                  </div>
                  <q-item-label header>Set Default ACH Account</q-item-label>
                  <bank-account-list
                      :query="{ status: 'succeeded' }"
                      no-edit
                      :org="org"
                      @update:model-value="setDefault"></bank-account-list>
                </q-tab-panel>
              </q-tab-panels>

              <q-tab-panels animated class="_panel" v-model="listTab">
                <q-tab-panel class="_panel" name="list">
                  <q-list separator>
                    <q-item-label header>Authorized to accept ACH
                      <q-btn flat size="sm" class="q-mb-xs" color="primary" icon="mdi-plus"
                             @click="listTab = 'form'"></q-btn>
                    </q-item-label>
                    <account-item v-for="(act, i) in authorized.data" :key="`act-${i}`" :model-value="act">
                      <template v-slot:side>
                        <remove-proxy-btn icon="mdi-close" remove-label="Stop accepting payments into account?"
                                      @remove="removeFromList(act)"></remove-proxy-btn>
                      </template>
                    </account-item>
                  </q-list>
                </q-tab-panel>
                <q-tab-panel class="_panel" name="form">
                  <div class="row">
                    <q-btn dense flat icon="mdi-chevron-left" color="primary" @click="listTab = 'list'"></q-btn>
                  </div>
                  <q-item-label header>Authorize accounts to receive payments</q-item-label>

                  <q-list separator>
                    <add-item @click="addDialog = true" label="New"></add-item>
                    <account-item clickable @click="addToList(act)" v-for="(act, i) in available.data"
                                  :key="`acct-${i}`" :model-value="act">
                    </account-item>
                  </q-list>
                </q-tab-panel>
              </q-tab-panels>

            </div>
          </div>
        </div>
      </div>
    </div>

    <common-dialog setting="right" v-model="addDialog">
      <div class="_fw q-pa-md bg-white">
        <business-account-form @update:model-value="newAcct" :org="org"></business-account-form>
      </div>
    </common-dialog>
  </q-page>
</template>

<script setup>
  import OrgTop from 'layouts/components/OrgTop.vue';
  import BankAccountList from 'components/accounts/lists/BankAccountList.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import AccountItem from 'components/accounts/cards/AccountItem.vue';
  import AddItem from 'components/common/buttons/AddItem.vue';
  import BusinessAccountForm from 'components/accounts/forms/BusinessAccountForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {computed, ref, watch} from 'vue';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {useBanking} from 'stores/banking';
  import {$errNotify} from 'src/utils/global-methods';
  import {useProviders} from 'stores/providers';
  import {HForm} from 'src/utils/hForm';
  import {HFind} from 'src/utils/hFind';
  import {useBankAccounts} from 'stores/bank-accounts';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const orgStore = useOrgs();
  const bankStore = useBanking();
  const pStore = useProviders();
  const acctStore = useBankAccounts();

  const { item: org } = idGet({
    store: orgStore,
    value: getOrgId
  ,
    useAtcStore
  })
  const providerId = computed(() => LocalStorage.getItem('provider_id'))
  const { item: provider } = idGet({
    store: pStore,
    value: providerId
  ,
    useAtcStore
  })
  const addDialog = ref(false);

  const accountId = computed(() => org.value?.treasury?.id);

  const moov_account = ref();
  const loading = ref(false);

  watch(accountId, async (nv, ov) => {
    if (nv && nv !== ov) {
      loading.value = true;
      moov_account.value = await bankStore.get(nv, { banking: { stripe: { method: 'get_account', args: [] } } })
      if (moov_account.value) SessionStorage.setItem('moov_account', moov_account.value.id)
      loading.value = false;
    }
  }, { immediate: true })

  const requestCardPayments = async () => {
    loading.value = true;
    const obj = { capabilities: { card_payments: { requested: true } } }
    moov_account.value = await bankStore.get(accountId.value, {
      banking: {
        stripe: {
          method: 'account_update',
          args: [obj]
        }
      }
    })
        .catch(err => {
          $errNotify('Error requesting card payments - try Banking Settings > Compliance for full details')
          console.error(`Error requesting card payments: ${err.message}`);
          return moov_account.value
        })
    loading.value = false;
  }

  const cardStatus = computed(() => moov_account.value?.capabilities?.card_payments);

  watch(cardStatus, (nv) => {
    setTimeout(() => {
      if (nv && provider.value && provider.value.payment_settings?.card_payments !== nv) {
        pStore.patch(providerId.value, { $set: { ['payment_settings.card_payments']: nv } })
      }
    }, 2000)
  }, { immediate: true })

  const { form } = HForm({
    store: pStore,
    value: provider
  })

  const defTab = ref('list');
  const listTab = ref('list');

  const acctList = computed(() => [...provider.value?.payment_settings?.ach_payments || []])
  const acctIds = computed(() => Object.keys(org.value?.accounts || {}))
  const addToList = async (val) => {
    const list = [...acctList.value];
    if (!list.includes(val._id)) {
      const patchObj = { $set: { ['payment_settings.ach_payments']: list } }
      list.push(val._id);
      if (!provider.value.payment_settings?.default_ach) patchObj.$set['payment_settings.default_ach'] = val._id
      await pStore.patch(provider.value._id, patchObj)
    }
    listTab.value = 'list'
  }
  const removeFromList = async (val) => {
    const list = [...acctList.value];
    const idx = list.indexOf(val._id);
    list.splice(idx, 1);
    const patchObj = { $set: {} };
    if (provider.value.payment_settings.default_ach === val._id) {
      if (list.length) patchObj.$set['payment_settings.default_ach'] = list[0];
      else patchObj.$unset = { ['payment_settings.default_ach']: '' }
    }
    if (!list.length) {
      delete patchObj.$set;
      patchObj.$unset = { ...patchObj.$unset, ['payment_settings.ach_payments']: '' }
    } else patchObj.$set = { ...patchObj.$set, ['payment_settings.ach_payments']: list }
    await pStore.patch(provider.value._id, patchObj)
    listTab.value = 'list'
  }
  const setDefault = async (val) => {
    const patchObj = { $set: { ['payment_settings.default_ach']: val._id } };
    const list = [...acctList.value];

    if (!list.length) patchObj.$set['payment_settings.ach_payments'] = [val._id]
    else if (!list.includes(val._id)) patchObj.$set['payment_settings.ach_payments'] = [...list, val._id]
    await pStore.patch(provider.value._id, patchObj)
    defTab.value = 'list'
  }

  const newAcct = (val) => {
    addDialog.value = false;
    addToList(val);
  }

  const { h$: a$ } = HFind({
    store: acctStore,
    limit: computed(() => acctIds.value.length),
    params: computed(() => {
      return {
        query: {
          status: 'succeeded',
          _id: { $in: acctIds.value }
        }
      }
    })
  })

  const available = computed(() => acctStore.findInStore({ query: { _id: { $nin: acctList.value }, status: 'succeeded' } }));
  const authorized = computed(() => acctStore.findInStore({ query: { _id: { $in: acctList.value }, status: 'succeeded' } }));

</script>

<style lang="scss" scoped>
  .__c {
    padding: 40px min(15px, 2vw) 20px min(15px, 2vw);
    border-radius: 20px;
    box-shadow: 0 2px 18px -6px #999;
    background: white;
    margin: 30px 0;
    position: relative;

    .__title {
      position: absolute;
      top: 0;
      left: 2.5%;
      width: 95%;
      transform: translate(0, -30%);
      border-radius: 6px;
      background: linear-gradient(-184deg, var(--q-s1), transparent);
      //box-shadow: 0 2px 2px rgba(0,0,0,.1);
      //background: linear-gradient(180deg, var(--q-s1), white);
      color: var(--q-s9);
      font-weight: 600;
      padding: 6px 8px;
      font-size: 1rem;
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;

    tr {
      td {
        border-bottom: solid .3px #999;
        padding: 6px 10px;
      }
      td:last-child {
        font-weight: bold;
        color: var(--q-primary);
      }
    }
    tr:last-child {
      td {
        border-bottom: none;
      }
    }

  }
</style>
