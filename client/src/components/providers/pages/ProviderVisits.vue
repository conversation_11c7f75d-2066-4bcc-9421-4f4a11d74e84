<template>
  <q-page class="bg-grey-1">
    <provider-top></provider-top>
    <div class="row justify-center">
      <div class="_xsent pd4 pw1">
        <div class="_fw q-py-sm row items-center">
          <q-icon class="q-mr-xs" name="mdi-filter" size="18px" color="accent"></q-icon>
          <provider-patient-picker v-model="pplFilter" :provider="provider"></provider-patient-picker>
          <visit-status-chip v-model="statusFilter" multiple picker></visit-status-chip>
        </div>
        <div class="row q-pb-sm no-wrap">
          <inline-date
              class="q-mx-xs"
              :input-attrs="{ filled: true, dense: true, label: 'From Date' }"
              v-model="dates.from"
          >
            <template v-slot:append>
              <q-btn dense flat size="sm" v-if="dates.from" color="red" icon="mdi-close" @click="dates.from = undefined"></q-btn>
            </template>
          </inline-date>
          <inline-date
              class="q-mx-xs"
              :input-attrs="{ filled: true, dense: true, label: 'To Date' }"
              v-model="dates.to"
          >
            <template v-slot:append>
              <q-btn dense flat size="sm" v-if="dates.to" color="red" icon="mdi-close" @click="dates.to = undefined"></q-btn>
            </template>
          </inline-date>
        </div>
        <div v-for="(v, i) in v$.data" :key="`v-${i}`" class="__c">
          <visit-card
              @click="$router.push({ name: 'visit-page', params: { visitId: v._id }})"
              :model-value="v">
            <template v-slot:top-right>
              <q-space></q-space>
              <member-chip :model-value="v.patient"></member-chip>
            </template>
          </visit-card>
        </div>
        <div class="row justify-end q-py-md">
          <q-pagination
              @update:model-value="v$.toPage($event)"
              :model-value="pagination.currentPage"
              :min="1"
              :max="pagination.pageCount"
              direction-links
              boundary-numbers
          ></q-pagination>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import VisitCard from 'components/care/visits/cards/VisitCard.vue';
  import MemberChip from 'components/households/cards/MemberChip.vue';
  import ProviderPatientPicker from 'components/providers/lists/ProviderPatientPicker.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {HFind} from 'src/utils/hFind';
  import {useVisits} from 'stores/visits';
  import {idGet} from 'src/utils/id-get';
  import {useProviders} from 'stores/providers';
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import VisitStatusChip from 'components/care/visits/cards/VisitStatusChip.vue';
  import ProviderTop from 'components/providers/cards/ProviderTop.vue';

  const visitStore = useVisits();
  const pStore = useProviders();

  const providerId = computed(() => LocalStorage.getItem('provider_id'));
  const { item:provider } = idGet({
    store: pStore,
    value: providerId
  ,
    useAtcStore
  })

  const pplFilter = ref();
  const dates = ref({ from: '', to: '' })
  const statusFilter = ref([]);

  const query = computed(() => {
    const q = {
      $sort: { date: -1 },
      provider: providerId.value
    }
    if (pplFilter.value) q.patient = pplFilter.value._id
    if (dates.value.from) q.date = { $gte: new Date(dates.value.from) };
    if (dates.value.to) q.date = { ...q.date, $lte: new Date(dates.value.to) };
    if(statusFilter.value.length) q.status = { $in: statusFilter.value }
    return q
  })

  const { h$:v$, pagination } = HFind({
    store: visitStore,
    pause: computed(() => !providerId.value),
    limit: ref(10),
    params: computed(() => {
      return {
        query: query.value,
      }
    })
  })

</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 12px;
    box-shadow: 0 2px 5px -3px #999;
    padding: 25px 2vw;
    margin: 15px 0;
    background: white;
    cursor:pointer;
    transition: all .4s ease;
    transform: none;

    &:hover {
      background: linear-gradient(100deg, var(--q-p0), white);
      box-shadow: 0 4px 12px -6px #999;
      transform: translate(0, -5px);
    }
  }
</style>
