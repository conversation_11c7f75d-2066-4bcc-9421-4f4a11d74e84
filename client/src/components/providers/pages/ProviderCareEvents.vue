<template>
  <q-page class="_fw">
    <div class="row justify-center">
      <div class="_cent pd4 pw1">
        <care-event-table  :params="params"></care-event-table>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import CareEventTable from 'components/care/tables/CareEventTable.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useProviders} from 'stores/providers';

  const pStore = useProviders();
  const { item: provider } = idGet({
    store: pStore,
    value: computed(() => LocalStorage.getItem('provider_id'))
  ,
    useAtcStore
  })

  const params = computed(() => {
    const query = { providers: { $in: [provider.value?._id] } }
    return {
      query
    }
  })
</script>

<style lang="scss" scoped>

</style>
