<template>
  <div class="_fw">
    <div class="row">
      <div class="col-12 col-md-6 q-pa-sm">
        <div class="__c">
          <div class="__title">Info</div>
          <profile-info :model-value="provider"></profile-info>
        </div>
        <div class="__c">
          <div class="__title">Practice Classes</div>
          <profile-classes :model-value="provider"></profile-classes>
        </div>
        <div class="__c">
          <div class="__title">Practitioners</div>
          <provider-practitioners adding :model-value="provider"></provider-practitioners>
        </div>
      </div>
      <div class="col-12 col-md-6 q-pa-sm">
        <div class="__c">
          <div class="__title">Images</div>
          <profile-images :model-value="provider"></profile-images>
        </div>
        <div class="__c">
          <div class="__title">Locations</div>
          <provider-locations :model-value="provider"></provider-locations>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
  import ProfileImages from 'components/providers/cards/profile/ProfileImages.vue';
  import ProfileInfo from 'components/providers/cards/profile/ProfileInfo.vue';
  import ProfileClasses from 'components/providers/cards/profile/ProfileClasses.vue';
  import ProviderPractitioners from 'components/providers/cards/profile/ProviderPractitioners.vue';
  import ProviderLocations from 'components/providers/cards/profile/ProviderLocations.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useProviders} from 'stores/providers';


  const pStore = useProviders();

  const { item: provider } = idGet({
    store: pStore,
    value: computed(() => LocalStorage.getItem('provider_id')),
    params: ref({
      runJoin: {
        provider_avatar: true,
        provider_videos: true,
        provider_images: true
      }
    })
  })

</script>

<style lang="scss" scoped>
  .__c {
    padding: 40px min(15px, 2vw) 20px min(15px, 2vw);
    border-radius: 20px;
    box-shadow: 0 2px 4px var(--ir-light);
    background: white;
    margin: 30px 0;
    position: relative;

    .__title {
      position: absolute;
      top: 0;
      left: 2.5%;
      width: 95%;
      transform: translate(0, -30%);
      border-radius: 6px;
      background: linear-gradient(-184deg, var(--ir-bg2), transparent);
      //box-shadow: 0 2px 2px rgba(0,0,0,.1);
      //background: linear-gradient(180deg, var(--q-s1), white);
      color: var(--q-s9);
      font-weight: 600;
      padding: 6px 8px;
      font-size: 1rem;
    }
  }
</style>
