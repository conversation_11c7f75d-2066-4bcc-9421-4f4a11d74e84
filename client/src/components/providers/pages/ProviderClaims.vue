<template>
  <q-page class="bg-grey-1">
    <provider-top></provider-top>
    <div class="row justify-center">
      <div class="_cent pw1">

        <claims-full-table :query="query">
          <template v-slot:filters>

            <div class="row items-center q-pt-sm">
              <q-icon class="q-mr-xs" name="mdi-filter" size="18px" color="accent"></q-icon>
              <provider-patient-picker v-model="pplFilter" :provider="provider"></provider-patient-picker>
              <status-chip color="transparent" picker v-model="statusFilter" multiple></status-chip>

            </div>
            <div class="_fw q-py-sm row items-center">

              <div class="row q-pb-sm no-wrap">
                <inline-date
                    class="q-mx-xs"
                    :input-attrs="{ filled: true, dense: true, label: 'From Date' }"
                    v-model="dates.from"
                >
                  <template v-slot:append>
                    <q-btn dense flat size="sm" v-if="dates.from" color="red" icon="mdi-close"
                           @click="dates.from = undefined"></q-btn>
                  </template>
                </inline-date>
                <inline-date
                    class="q-mx-xs"
                    :input-attrs="{ filled: true, dense: true, label: 'To Date' }"
                    v-model="dates.to"
                >
                  <template v-slot:append>
                    <q-btn dense flat size="sm" v-if="dates.to" color="red" icon="mdi-close"
                           @click="dates.to = undefined"></q-btn>
                  </template>
                </inline-date>
              </div>
            </div>

          </template>
          <template v-slot:header-right>
            <q-th align="left">Payment</q-th>
          </template>
          <template v-slot:body-right="scope">
            <q-td>
              <claim-payment-chip :model-value="scope.row"></claim-payment-chip>
            </q-td>
          </template>
        </claims-full-table>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import ProviderTop from 'components/providers/cards/ProviderTop.vue';
  import ProviderPatientPicker from 'components/providers/lists/ProviderPatientPicker.vue';
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import ClaimsFullTable from 'components/claims/cards/ClaimsFullTable.vue';
  import StatusChip from 'components/care/cards/StatusChip.vue';
  import ClaimPaymentChip from 'components/claim-payments/forms/ClaimPaymentChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {idGet} from 'src/utils/id-get';
  import {useProviders} from 'stores/providers';

  const pStore = useProviders();

  const pplFilter = ref();
  const dates = ref({ from: '', to: '' })
  const statusFilter = ref([]);
  const providerId = computed(() => LocalStorage.getItem('provider_id'));
  const { item: provider } = idGet({
    store: pStore,
    value: providerId
  ,
    useAtcStore
  })
  const query = computed(() => {
    const q = {
      $sort: { date: -1 },
      provider: providerId.value
    }
    if (pplFilter.value) q.patient = pplFilter.value._id
    if (dates.value.from) q.date = { $gte: new Date(dates.value.from) };
    if (dates.value.to) q.date = { ...q.date, $lte: new Date(dates.value.to) };
    if (statusFilter.value.length) q.status = { $in: statusFilter.value }
    return q
  })
</script>

<style lang="scss" scoped>

</style>
