<template>
  <q-page class="_bg_s0">
    <provider-top v-if="p$.data?.length"></provider-top>
    <div class="row justify-center">
      <div class="_cent pd4 pw2">
        <template v-if="p$.data.length">
          <router-view></router-view>
        </template>
        <template v-else>
          <create-provider></create-provider>
        </template>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import CreateProvider from 'components/providers/pages/CreateProvider.vue';
  import ProviderTop from 'components/providers/cards/ProviderTop.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, watch} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useOrgs} from 'stores/orgs';
  import {HFind} from 'src/utils/hFind';
  import {useProviders} from 'stores/providers';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const orgStore = useOrgs()
  const providerStore = useProviders();
  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const { item: org } = idGet({
    store: orgStore,
    value: getOrgId
  ,
    useAtcStore
  })

  const { h$:p$ } = HFind({
    store: providerStore,
    pause: computed(() => !org.value._id),
    params: computed(() => {
      return {
        query: {
          org: org.value._id
        }
      }
    })
  })

  watch(() => p$.data[0], (nv) => {
    if(nv && !LocalStorage.getItem('provider_id')){
      LocalStorage.setItem('provider_id', nv._id);
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
