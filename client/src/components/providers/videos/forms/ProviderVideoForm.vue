<template>
  <div class="_fw">
    <q-tab-panels class="_panel" v-model="tab" animated transition-prev="jump-down" transition-next="jump-up">
      <q-tab-panel class="_panel" name="list">
        <q-list separator>
          <q-item-label header v-if="!keyOptions?.length">No Video Options Available</q-item-label>
          <q-item v-for="(k, i) in keyOptions || []" :key="`k-${i}`" clickable @click="select(k)">
            <q-item-section>
              <q-item-label class="tw-six text-grey-7">
                {{ keyName(k) }}
              </q-item-label>
              <q-item-label caption>
                {{ (nameMap[path] || {})[k]?.description || '' }}
              </q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-icon name="mdi-video-box" :color="videoObj[k] ? 'primary' : 'grey-4'"></q-icon>
            </q-item-section>
          </q-item>
        </q-list>
      </q-tab-panel>
      <q-tab-panel  class="_panel" name="form">
        <div class="row justify-end">
          <q-btn size="sm" dense flat icon="mdi-close" color="red" @click="tab = 'list'"></q-btn>
        </div>
        <div class="__vid q-ma-sm" v-if="videoObj[active]?.uploadId">
          <div class="t-r-a">
            <remove-proxy-btn name="Video" dense size="sm" color="red" :flat="false" @remove="remove(active)"></remove-proxy-btn>
          </div>
          <q-video ratio="1.7777" :src="videoObj[active].url"></q-video>
        </div>
        <video-form
            v-else
            :existingIds="provider?.allVideos || []"
            @remove="remove(active)"
            @update:model-value="setVideo(active, $event)"
            :display-url="videoObj[active]?.url"
        ></video-form>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import VideoForm from 'components/common/uploads/video/VideoForm.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {HFind} from 'src/utils/hFind';
  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useProviders} from 'stores/providers';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';
  import {useUploads} from 'stores/uploads';

  const pStore = useProviders();
  const uploadStore = useUploads();

  const props = defineProps({
    store: { required: false },
    provider: { required: true },
    path: { type: String, required: true },
    keyOptions: Array
  })

  const { item: fullProvider } = idGet({
    store: pStore,
    value: computed(() => props.provider)
  ,
    useAtcStore
  })

  const tab = ref('list');
  const active = ref('');

  const select = (val) => {
    active.value = val;
    tab.value = 'form'
  }

  const videoObj = ref({})

  const setObj = async (p) => {
    videoObj.value = {...(p.videos || {})[props.path]};
    const keys = Object.keys(videoObj.value);
    const idList = [];
    for(let i = 0; i < keys.length; i++){
      const id = videoObj.value[keys[i]]?.uploadId;
      if(!uploadStore.getFromStore(id)?.value) idList.push(id)
    }
    await uploadStore.find({ query: { _id: { $in: idList }}})
        .catch(err => console.error(`Error fetching uploads: ${err.message}`));
    for(let i = 0; i < keys.length; i++){
      const ul = uploadStore.getFromStore(videoObj.value[keys[i]].uploadId)
      if(ul.value) videoObj.value[keys[i]].url = ul.value.url
    }
  }
  watch(fullProvider, (nv, ov) => {
    if(nv && nv._id !== ov?._id) setObj(nv)
  }, { immediate: true })

  const idKeys = computed(() => Object.keys(videoObj.value))
  const { h$ } = props.store ?  HFind({
    store: props.store,
    pause: computed(() => !props.store),
    limit: computed(() => idKeys.value.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: idKeys.value }
        }
      }
    })
  }) : { h$: { data: [] }}

  const nameMap = {
    'general': {
      description: 'Videos for introducing yourself to CommonCare groups and participants',
      keys: {
        'patient_landing': {
          label: 'Patient Landing',
          description: 'For patients who search for your services'
        },
        'group_landing': {
          label: 'Group Landing',
          description: 'For groups who search for your services'
        }
      }
    },
    'memberships': {
      description: 'Video for groups and participants exploring your membership based care plans'
    },
    'bundles': {
      description: 'Video for people considering your posted care pricing'
    }
  }
  const keyName = (k) => {
    if (props.store) {
      const val = props.store.getFromStore(k).value;
      if (val) return { label: val.name || k };
      return { label: k };
    }
    return (nameMap[props.path] || {})[k]?.label || k.split('_').map(a => $capitalizeFirstLetter(a)).join(' ');
  }

  const remove = (k) => {
    const patchObj = { $unset: { [`videos.${props.path}.${k}`]: '' }}
    pStore.patch(fullProvider.value._id, patchObj)
        .catch(err => console.error(`Error removing video: ${err.message}`))
    delete videoObj.value[k]
  }
  const setVideo = async (k, val) => {
    const patchObj = { $set: { [`videos.${props.path}.${k}`]: val }, $addToSet: { allVideos: val.uploadId }}
    const id = fullProvider.value._id;
    pStore.patchInStore(id, patchObj);
    await pStore.patch(id, patchObj);
    const upload = await uploadStore.get(val.uploadId)
    videoObj.value[k] = {...videoObj.value[k], url: upload.url }
    tab.value = 'list'
  }
</script>

<style lang="scss" scoped>
  .__vid {
    position: relative;
    height: 180px;
    width: 320px;
    max-width: 95vw;
    max-height: calc(95vw * .5);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, .15);
  }
</style>
