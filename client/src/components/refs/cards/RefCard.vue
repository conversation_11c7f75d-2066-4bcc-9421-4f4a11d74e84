<template>
  <div class="__ref_card" :style="{borderRadius: square ? '7px' : '50%'}">
    <q-img fit="cover" :src="img" class="_fa"></q-img>
    <div v-if="!noName && square" class="__label">{{reF.name}}</div>
    <slot name="default"></slot>
  </div>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {useRefs} from 'stores/refs';
  import {computed, ref} from 'vue';
  import {useAtcStore} from 'src/stores/atc-store';
  const refStore = useRefs();

  const defImg = 'https://cdn.quasar.dev/img/material.png'

  const props = defineProps({
    modelValue: { required: true },
    noName: Boolean,
    square: Boolean
  })

  const { item:reF } = idGet({
    store: refStore,
    value: computed(() => props.modelValue),
    refreshOn: (val) => !val._fastjoin?.person && !val._fastjoin?.org,
    params: ref({ runJoin: { ref_person: true }}),
    def: {}
  })

  const img = computed(() =>  reF.value._fastjoin?.files?.avatar?.url || defImg)

</script>

<style lang="scss" scoped>

  .__ref_card {
    width: 120px;
    height: 120px;
    max-height: 100%;
    max-width: 100%;
    border-radius: 10px;
    box-shadow: 0 2px 6px var(--ir-light);
    background-size: cover;
    background-position: top;
    position: relative;
    overflow: hidden;

    .__label {
      color: white;
      background: rgba(0,0,0,.6);
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      padding: 5px 10px;
      border-radius: 0 0 10px 10px;
      font-weight: 600;
    }
  }
</style>
