<template>
  <div class="_fw">
    <template v-if="!profile._id">
    <div v-if="reF._id || host._id">
      <div class="text-center font-7-8r text-ir-deep tw-six _fw q-pb-sm">You are invited to join</div>
      <div class="_fw q-pb-sm" v-if="host._id">
        <div class="__item">
          <default-avatar :model-value="host" :use-atc-store="useAtcStore"></default-avatar>
          <div class="__name">{{ host.dba }}</div>
        </div>
      </div>

      <div class="_fw __by" v-if="reF._id">
        <div class="font-7-8r tw-six text-ir-deep q-pa-sm">By: </div>
        <div class="__item">
          <div class="h40 w40 br5 _oh">
            <ref-card no-name :model-value="reF"></ref-card>
          </div>
          <div class="__name">{{ reF.name }}</div>
        </div>
      </div>

    </div>
    <div v-else>

    </div>

    <div class="q-pa-sm _fw" v-if="!isAuthenticated">
      <trad-login></trad-login>
    </div>
    <div v-else>
      <div class="_fw q-py-sm _form_grid">
        <div class="_form_label">Name</div>
        <div class="q-pa-sm">
          <q-input class="_inp" borderless dense v-model="form.name"></q-input>
        </div>

        <div class="_form_label">Email</div>
        <div class="q-pa-sm">
          <email-field class="_inp" borderless dense v-model="form.email"></email-field>
        </div>

        <div class="_form_label">Phone</div>
        <div class="q-pa-sm">
          <phone-input class="_inp" v-model="form.phone" :input-attrs="{ borderless: true, dense: true, placeholder: '', hideBottomSpace: true }"></phone-input>
        </div>
      </div>
    </div>
    <div class="q-pa-md _fw">
      <q-btn class="_pl_btn tw-six" no-caps push @click="submit">
        <span v-if="host._id" class="q-mr-sm">Accept Invite</span>
        <span v-else class="q-mr-sm">Create Seller Profile</span>
        <q-spinner v-if="loading" color="white"></q-spinner>
        <q-icon v-else name="mdi-check-circle"></q-icon>
      </q-btn>
    </div>
    </template>
    <q-slide-transition>
      <div class="_fw" v-if="profile._id">
        <div class="_fw q-py-sm" v-if="host._id">
          <div class="__item">
            <default-avatar :model-value="host" :use-atc-store="useAtcStore"></default-avatar>
            <div class="__name">{{ host.dba }}</div>
          </div>
          <div class="q-pt-sm font-1r">You are a member of {{host.dba}}</div>
        </div>
        <q-separator class="q-my-sm"></q-separator>

        <div class="__item">
          <div class="h50 w50 br5 _oh">
            <ref-card no-name :model-value="profile"></ref-card>
          </div>
          <div class="__name">{{ profile.name }}</div>
        </div>
        <q-separator class="q-my-sm"></q-separator>
        <div class="q-pa-sm tw-six font-1r">Invite others</div>
        <q-chip clickable @click="$copyTextToClipboard(myLink)" color="ir-bg2">
          <q-icon name="mdi-content-copy" class="q-mr-sm" color="a3"></q-icon>
          <input class="_common_inp" :value="myLink">
        </q-chip>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import RefCard from 'components/refs/cards/RefCard.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import TradLogin from 'components/auth/traditional/TradLogin.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';

  import {computed, ref, watch} from 'vue';
  import {useAtcStore} from 'src/stores/atc-store';
  import {useRoute, useRouter} from 'vue-router';
  import {LocalStorage} from 'symbol-auth-client';
  import {idGet} from 'src/utils/id-get';
  import {useHosts} from 'stores/hosts';
  import {useRefs} from 'stores/refs';
  import {loginPerson} from 'stores/utils/login';
  import {HFind} from 'src/utils/hFind';
  import {HForm} from 'src/utils/hForm';
  import {$copyTextToClipboard, getRootDomain} from 'src/utils/global-methods';

  const { isAuthenticated, person } = loginPerson()

  const route = useRoute();
  const router = useRouter()

  const hostStore = useHosts();
  const refStore = useRefs();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    dark: Boolean
  })

  const hostId = computed(() => {
    if (route.query.hostId) return route.query.hostId;
    return LocalStorage.getItem('host_id')
  })

  const refId = computed(() => {
    if (route.query.refId) return route.query.refId;
    return LocalStorage.getItem('ref_id');
  })

  const { item: host } = idGet({
    store: hostStore,
    value: hostId,

    useAtcStore
  })

  const { item: reF } = idGet({
    store: refStore,
    value: refId
  ,
    useAtcStore
  })

  const loading = ref(false);

  const { form, save } = HForm({
    store: refStore,
    params: ref({ runJoin: { ref_person: true }}),
    beforeFn: (val) => {
      loading.value = true;
      val.person = person.value._id;
      if(!val.host) val.host = host.value._id
      return val
    },
    afterFn: (val) => {
      emit('update:model-value', val);
    }
  })

  const { h$:r$ } = HFind({
    store:  refStore,
    limit: ref(10),
    pause: computed(() => !isAuthenticated.value),
    params: computed(() => {
      return {
        runJoin: { ref_person: true },
        query: { person: person.value._id, host: host.value._id }
      }
    })
  })

  const profile = ref({});
  const myLink = computed(() => {
    const { href } = router.resolve({ name: route.name, query: { hostId: host.value._id, refId: profile.value._id }})
    return getRootDomain() + href;
  })
  const submit = async () => {
    loading.value = true;
    let rf
    if(!r$.total) rf = await save()
    else rf = r$.data[0];
    loading.value = false;
    profile.value = rf;
    if(reF.value._id && !rf.ref && rf._id !== reF.value._id) await refStore.patch(rf._id, { ref: reF.value._id }, { runJoin: { ref_person: true }})
  }

  const match = computed(() => {
    if(!host.value._id) return r$.data.filter(a => !a.host)[0];
    return r$.data.filter(a => a.host === host.value._id)[0]
  })

  watch(match, (nv) => {
    if(nv) profile.value = nv;
  }, { immediate: true })

  watch(person, (nv) => {
    if(nv){
      if(nv.name) form.value.name = nv.name;
      if(nv.email) form.value.email = nv.email;
      if(nv.phone) form.value.phone = nv.phone;
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

  .__item {
    display: grid;
    grid-template-columns: auto 1fr;
    width: 100%;
    align-items: center;
    color: var(--ir-deep);
  }

  .__name {
    padding: 5px 10px;
    font-size: 1.1rem;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%
  }

  .__by {
    display: grid;
    grid-template-columns: auto auto 1fr;
    align-items: center;
  }
</style>
