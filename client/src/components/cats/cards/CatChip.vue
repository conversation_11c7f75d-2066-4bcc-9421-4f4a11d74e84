<template>
  <q-chip v-bind="{ color: 'ir-bg2', clickable: true, ...$attrs }">
    <slot name="icon" v-bind="{ cat }">
      <q-icon class="q-mr-xs" color="accent" name="mdi-folder"></q-icon>
    </slot>
    <span>{{ cat.name || emptyLabel }}</span>
    <slot name="side" :cat="cat"></slot>
  </q-chip>
</template>

<script setup>

  import {useCats} from 'stores/cats';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useAtcStore} from 'src/stores/atc-store';

  const catStore = useCats()

  const props = defineProps({
    emptyLabel: { default: '' },
    modelValue: { required: true },
  })

  const { item: cat } = idGet({
    store: catStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

</script>

<style lang="scss" scoped>

</style>
