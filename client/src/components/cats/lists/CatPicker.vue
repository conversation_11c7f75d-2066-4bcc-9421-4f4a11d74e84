<template>
  <q-chip v-bind="{ color: 'ir-bg2', clickable: true, ...$attrs }">
    <slot name="icon" v-bind="{ cat }">
      <q-icon class="q-mr-xs" color="accent" name="mdi-folder"></q-icon>
    </slot>
    <span>{{multiple ? 'Select Category' : cat.name || emptyLabel}}</span>
    <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>

    <q-popup-proxy v-model="popup">
      <div class="w300 mw100 q-pa-sm bg-white">
        <q-input dense flat filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <q-list separator>
          <q-item v-for="(c, i) in c$.data" :key="`c-${i}`" clickable @click="select(c)">
            <q-item-section>
              <q-item-label>{{c.name}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-popup-proxy>
  </q-chip>
  <template v-if="multiple">
    <cat-chip v-for="(c, i) in modelValue || []" :key="`chosen-${i}`" :model-value="c">
      <template v-slot:side="scope">
        <q-btn dense flat size="sm" class="q-ml-xs" color="red" icon="mdi-close" @click="select(scope.cat)"></q-btn>
      </template>
    </cat-chip>
  </template>
</template>

<script setup>
  import CatChip from 'components/cats/cards/CatChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useCats} from 'stores/cats';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';

  const catStore = useCats()

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    emptyLabel: { default: 'Select Category' },
    modelValue: { required: true },
    multiple: Boolean,
    emitValue: Boolean
  })

  const popup = ref(false)

  const { item: cat } = idGet({
    store: catStore,
    value: computed(() => props.multiple ? {} : props.modelValue)
  })

  const { search, searchQ } = HQuery({})
  const { h$:c$ } = HFind({
    store: catStore,
    limit: ref(10),
    pause: computed(() => !popup.value),
    params: computed(() => {
      const query = searchQ.value;
      // if(props.multiple && props.modelValue) query._id = { $nin: (props.modelValue || []).map(a => props.emitValue ? a : a._id)}
      return {
        query
      }
    })
  })

  const select = (val) => {
    if (props.multiple) {
      const list = [...props.modelValue || []]
      if (props.emitValue) {
        const idx = list.indexOf(val._id);
        if (idx > -1) list.splice(idx, 1);
        else list.push(val._id);
      } else {
        const idx = list.map(a => a._id).indexOf(val._id);
        if (idx > -1) list.splice(idx, 1);
        else list.push(val);
      }
      emit('update:model-value', list)
    } else if (props.emitValue) emit('update:model-value', val._id);
    else emit('update:model-value', val);
    popup.value =false;
  }
</script>

<style lang="scss" scoped>

</style>
