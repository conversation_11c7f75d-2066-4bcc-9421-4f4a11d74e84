<template>
  <q-page class="pd5">
    <div class="row justify-center">
      <div class="_xsent q-pa-sm">
        <div class="__dc q-pa-md">
          <drop-card always-open :model-value="drop"></drop-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import DropCard from 'src/components/drops/cards/DropCard.vue';
  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useAtcStore} from 'src/stores/atc-store';

  import { useRoute } from 'vue-router';
  const route = useRoute();

  import { useDrops } from 'src/stores/drops';
  const store = useDrops();

  const dropValue = computed(() => {
    return route.params.id
  })

  const { item:drop } = idGet({
    store,
    value: dropValue
  ,
    useAtcStore
  })
</script>

<style lang="scss" scoped>

</style>
