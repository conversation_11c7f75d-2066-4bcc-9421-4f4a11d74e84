<template>
  <div class="_fw relative-position" @dblclick="goTo(drop._id)">
    <div class="row items-center q-pb-sm">
      <div class="font-3-4r text-weight-bold">By:</div>
      <default-chip :chip-attrs="{size: 'sm', color: 'white', class: 'text-weight-medium'}" v-if="!drop?.anonymous"
                    :model-value="person" :use-atc-store="useAtcStore"></default-chip>
      <q-chip class="text-weight-medium" color="white" size="sm" v-else>Anonymous</q-chip>
      <q-chip
          size="sm"
          v-if="drop?.class"
          color="white"
          class="text-weight-medium"
      >
        <q-avatar :color="clss[drop.class].color"></q-avatar>
        <span class="q-ml-xs">{{ clss[drop.class].label }}</span>
      </q-chip>

      <q-space></q-space>
      <div class="flex items-center">
        <div v-if="!expand" class="font-3-4r alt-font">Score: <b>{{ drop?.voteCount || 0 }}</b></div>
        <vote-button
            v-if="expand"
            :user-id="login?._id"
            :store="store"
            :model-value="drop"
        ></vote-button>

        <q-separator vertical class="q-ml-sm"></q-separator>
        <q-btn icon="mdi-dots-vertical" color="black" size="sm" dense flat>
          <q-menu>
            <q-list separator dense>
              <q-item clickable @click="goTo(drop?._id)">
                <q-item-section>
                  <q-item-label>Open</q-item-label>
                </q-item-section>
                <q-item-section>
                  <q-icon name="mdi-open-in-new"></q-icon>
                </q-item-section>
              </q-item>
              <q-item v-if="canEdit?.ok" clickable @click="dialog = true">
                <q-item-section>
                  <q-item-label>Edit</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon name="mdi-pencil-box"></q-icon>
                </q-item-section>
              </q-item>
              <q-item clickable v-if="canEdit?.ok" @click.stop="removeD = true">
                <q-item-section>
                  <q-item-label>Delete</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon name="mdi-delete"></q-icon>
                </q-item-section>

              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
    </div>


    <div class="row">

      <div class="font-1-1-8r text-weight-bold q-py-sm">
        <div>{{ $limitStr(drop?.title, 200, '...') }}</div>
      </div>
    </div>


    <q-slide-transition>
      <div class="_fw" v-if="expand">
        <div>
          <span v-html="drop?.body"></span>
          <span class="font-3-4r text-ir-grey-7" v-if="drop?.editMap?.body">(edited)</span>
        </div>
        <div class="q-py-md">
          <q-separator class="q-my-xs"></q-separator>

          <template v-if="login">
            <div class="text-weight-bold font-3-4r text-right q-px-md">Reply to this question</div>
            <div class="flex items-center">

              <thread-form
                  :parent="{ id: drop?._id, idService: 'drops' }"
              ></thread-form>

            </div>
          </template>

          <div class="q-pa-xs" v-if="drop?.topAnswer">
            <thread-card :model-value="drop.topAnswer">
              <template v-slot:after-name="scope">
                <div v-if="scope.thread?._id === (drop?.topAnswer || '***')" class="font-2-3r text-weight-bold q-pl-md text-right">
                  <q-icon size="12px" name="mdi-star"></q-icon>Top Answer
                  <q-tooltip>Asker picked as top answer</q-tooltip>
                </div>
              </template>
            </thread-card>
          </div>
          <show-threads :menu-add-ons="menuAddOns" :threads="(drop?.threads || []).filter(a => a !== drop?.topAnswer)">
          </show-threads>

        </div>
      </div>
    </q-slide-transition>

    <div class="row items-center">
      <div class="col-12">
        <div class="row">
          <q-btn dense flat icon="mdi-label-outline"></q-btn>
          <q-badge color="ir-grey-3" class="text-weight-medium text-black q-mr-xs" v-for="(tag, i) in drop?.tags || []"
                   :key="`tag-${i}`" :label="tag"></q-badge>
        </div>
      </div>
    </div>

    <div class="q-py-sm row justify-center" v-if="!alwaysOpen">
      <q-btn
          size="sm"
          flat
          bg-color="ir-grey-2"
          text-color="black"
          rounded
          :icon-right="`mdi-chevron-${expand ? 'up' : 'down'}`"
          no-caps
          @click="expand = !expand"
          :label="expand ? 'Hide Full Question' : 'View Full Question'"
      ></q-btn>
    </div>


    <div class="row items-end">
      <div class="col-9">
        <q-chip color="white" size="sm" class="text-weight-medium">
          Asked {{ $ago(drop?.createdAt) }} - Updated {{ $ago(drop?.editMap?.body) }}
        </q-chip>
      </div>
      <div class="col-3">
        <div class="row justify-end">
          <div class="text-weight-medium flex items-center">
            <q-btn dense flat icon="mdi-chat-outline"></q-btn>
            <div class="font-3-4r">{{ $possiblyPlural('Repl', drop?.threads || [], 'y', 'ies') }}</div>
          </div>
        </div>
      </div>
    </div>

    <common-dialog v-model="dialog" setting="right">
      <div class="_fw q-pa-md">
        <drops-form :model-value="drop"></drops-form>
      </div>
    </common-dialog>

  <common-dialog setting="small" v-model="removeD">
    <remove-dialog :item="drop" :store="store" :model-value="true"></remove-dialog>
  </common-dialog>

  </div>
</template>

<script setup>
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import DropsForm from 'src/components/drops/forms/DropsForm.vue';
  import RemoveDialog from 'src/components/common/dialogs/RemoveDialog.vue';
  import DefaultChip from 'src/components/common/avatars/DefaultChip.vue';
  import ShowThreads from 'src/components/threads/cards/ShowThreads.vue';
  import VoteButton from 'src/components/common/buttons/VoteButton.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useDrops, classes} from 'src/stores/drops';

  const store = useDrops();
  import {idGet} from 'src/utils/id-get';
  import {ref, computed, watch, onMounted} from 'vue';
  import {$limitStr, $ago, $possiblyPlural, $errNotify, $successNotify} from 'src/utils/global-methods';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  import {canUOrCreated} from 'src/utils/ucans/client-auth';


  import {loginPerson} from 'src/stores/utils/login';
  const { person, login } = loginPerson()
  import ThreadForm from 'src/components/threads/forms/ThreadForm.vue';
  import ThreadCard from 'src/components/threads/cards/ThreadCard.vue';

  const props = defineProps({
    modelValue: { required: true },
    alwaysOpen: Boolean
  });

  const menuAddOns = ref([
    {
      icon: 'mdi-check-circle-outline',
      label: 'Pick as Best Answer',
      show: () => login.value?._id === drop.value?.createdBy?.login,
      click: async (thread) => {
        if(login.value?._id === drop.value?.createdBy?.login){
          await store.patch(drop.value._id, { topAnswer: thread._id })
              .catch(err => $errNotify(err.message));
          $successNotify('Best Answer Selected')
        }
      }
    }
  ])

  const dialog = ref(false);
  const removeD = ref(false);
  const canEdit = ref({ ok: false });

  const expand = ref(false);

  const clss = computed(() => classes());

  const { item: drop } = idGet({
    store,
    value: computed(() => props.modelValue),
    params: computed(() => {
      return { loginId: login.value?._id }
    })
  })

  const goTo = (id) => {
   const href = router.resolve({ name: 'drop', params: { id }}).href;
    window.open(href, '_blank');
  }

  watch(drop, async (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      canEdit.value = await canUOrCreated(nv.createdBy?.login, { requiredCapabilities: [['drops', 'DELETE']] })
    }
  }, { immediate: true })

  onMounted(() => {
    if(props.alwaysOpen) expand.value = true;
  });

</script>

<style lang="scss" scoped>

</style>
