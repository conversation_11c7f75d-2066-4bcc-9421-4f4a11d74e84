<template>
  <div class="_fw q-py-md">
    <div class="_form_grid">
      <div class="_form_label">Practice State</div>
      <div class="q-pa-sm">
        <div class="row items-center">
          <state-slide-chip
              outline
              picker
              :model-value="_get(form, 'license_states[0]')"
              @update:model-value="addState">
            <template v-slot:label>
              <span class="q-mx-xs">License States</span>
            </template>
          </state-slide-chip>
          <state-chip color="white" v-for="st in form.license_states" :key="`st-${st}`" removable
                      icon-remove="mdi-close" @remove="addState(st)" :model-value="st"></state-chip>
        </div>
      </div>
      <div class="_form_label">Name</div>
      <div class="q-pa-sm">
        <q-slide-transition>
          <div class="_fw" v-if="!adding && !!chosen">
            <practitioner-item :model-value="chosen">
              <template v-slot:side>
                <q-item-section side>
                  <q-btn dense flat icon="mdi-close" color="red" @click="chosen = undefined, form = formFn()"></q-btn>
                </q-item-section>
              </template>
            </practitioner-item>
          </div>
        </q-slide-transition>
        <template v-if="adding || !chosen">
          <div class="row items-center">
            <div class="col-6 q-pa-xs">
              <q-input
                  @update:model-value="runSearch"
                  placeholder="Enter First Name..."
                  v-model="form.firstName"
              ></q-input>
            </div>
            <div class="col-6 q-pa-xs">
              <q-input
                  @update:model-value="runSearch"
                  placeholder="Enter Last Name..."
                  v-model="form.lastName"
              ></q-input>
            </div>
          </div>
        </template>
        <div class="_fw row q-py-md" v-if="!adding">
          <q-btn
              v-if="form.firstName && form.lastName"
              :disable="!form.firstName?.length > 1 || !form.lastName?.length > 1"
              push
              :label="`Add ${form.firstName || ''} ${form.lastName || ''}`"
              no-caps
              class="tw-six _s_btn"
              @click="save"
          ></q-btn>
        </div>
      </div>
      <template v-if="adding">
        <div class="_form_label">NPI</div>
        <div class="q-pa-sm">
          <npi-input
              v-model="form.npi"
              @selected="setItem($event)"
              @update:model-value="autoSave('npi')"
          ></npi-input>
        </div>
        <div class="_form_label">Profile Picture</div>
        <div class="q-pa-sm">
          <image-select-or-upload
              v-model="form.avatar"
              @update:model-value="autoSave('avatar')"
          ></image-select-or-upload>

        </div>
        <div class="_form_label">Specialty</div>
        <div class="q-pa-sm">
          <provider-specialty-picker
              @update:model-value="autoSave('taxonomy3')"
              v-model="form.taxonomy3"
          ></provider-specialty-picker>
        </div>
        <template v-if="!form._id">
          <div class="_form_label"></div>
          <div class="q-pa-md row justify-end">
            <q-btn @click="save" class="_p_btn" push label="Save Practitioner"></q-btn>
          </div>
        </template>
        <template v-else-if="form.person">
          <div class="_form_label"></div>
          <div class="q-pa-md row justify-end">
            <q-btn push glossy color="primary" @click="emit('update:model-value', form)" label="Select Practitioner"></q-btn>
          </div>
        </template>
      </template>
      <div class="_form_label">
        Matches
      </div>
      <div class="q-pa-sm">
        <q-list separator v-if="suggestions.data?.length">
          <q-item-label header>Likely Matches</q-item-label>
          <practitioner-item
              v-for="(p, i) in suggestions.data" :key="`p-${i}`"
              @click="setItem(p)"
              clickable
              :model-value="p"
          ></practitioner-item>
        </q-list>
        <q-item-label header v-else>Likely Matches</q-item-label>
      </div>
    </div>
  </div>
</template>

<script setup>
  import StateChip from 'components/common/geo/pickers/StateChip.vue';
  import PractitionerItem from 'components/practitioners/cards/PractitionerItem.vue';
  import StateSlideChip from 'components/common/geo/pickers/StateSlideChip.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {idGet} from 'src/utils/id-get';
  import {usePractitioners} from 'stores/practitioners';
  import {computed, ref, watch} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {getStateCode} from 'components/common/geo/data/states';

  import {storeToRefs} from 'pinia';
  import {useEnvStore} from 'stores/env';
  import {useAtcStore} from 'src/stores/atc-store';
  import {_get} from 'symbol-syntax-utils';
  import ProviderSpecialtyPicker from 'components/providers/forms/ProviderSpecialtyPicker.vue';
  import NpiInput from 'components/practitioners/forms/NpiInput.vue';
  import ImageSelectOrUpload from 'components/common/uploads/images/ImageSelectOrUpload.vue';

  const envStore = useEnvStore()
  const { region } = storeToRefs(envStore);

  const store = usePractitioners();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false },
    adding: Boolean,
    providerId: { required: false }
  })
  const { item: doc } = idGet({
    store,
    value: computed(() => props.modelValue),
    useAtcStore
  })
  const formFn = (defs) => {
    return {
      firstName: '',
      lastName: '',
      ...defs
    }
  }
  const { form, save } = HForm({
    value: doc,
    validate: true,
    formFn,
    vOpts: computed(() => {
      return {
        'firstName': { name: 'First Name', v: ['gt:1'] },
        'lastName': { name: 'Last Name', v: ['gt:1'] }
      }
    }),
    notify: false,
    beforeFn: (val) => {
      const id = props.providerId
      if (id && !(val.providers || {})[id]) {
        val.providers = { ...val.providers, [id]: { id } }
      }
      return val;
    },
    afterFn: (val) => {
      emit('update:model-value', val)
    },
    store
  })

  const { autoSave } = HSave({ form, store, save, pause: computed(() => !form.value._id) })

  const chosen = ref(undefined);

  const setItem = (doc) => {
    const { _id, ...rest } = doc;
    if (!props.adding) form.value = Object.assign({}, rest);
    else form.value = { ...form.value, ...doc }
    chosen.value = doc;
    if(form.value._id) {
      emit('update:model-value', form.value)
    } else save()
  }

  const search = computed(() => {
    return {
      text: `${form.value.firstName || ''} ${form.value.lastName || ''}`,
      keys: ['firstName', 'lastName']
    }
  })


  const { searchQ } = HQuery({ search })

  const suggestions = ref({ total: 0, data: [] });

  const loading = ref(false);
  const to = ref(undefined);
  const runSearch = async () => {
    if (!loading.value) {
      loading.value = true;
      const _search = {
        npi: {}
      }
      let run;
      const query = { ...searchQ.value };
      if (form.value.firstName?.length > 1) {
        run = true
        _search.npi.firstName = form.value.firstName + '*';
      }
      if (form.value.lastName?.length > 1) {
        run = true
        _search.npi.lastName = form.value.lastName + '*';
      }
      if (form.value.license_states?.length) {
        _search.npi.license_state = form.value.license_states[0];
        query.license_states = { $in: form.value.license_states };
      }
      if (run) {
        suggestions.value = await store.find({ query, _search })
            .catch(err => {
              console.error(`Error finding suggestions: ${err.value}`);
              loading.value = false;
            })
        loading.value = false;
      } else loading.value = false;
    } else {
      if (to.value) clearTimeout(to.value);
      to.value = setTimeout(() => {
        loading.value = false;
        runSearch();
      }, 2000);
    }
  }

  const addState = (val) => {
    const idx = form.value.license_states?.indexOf(val);
    if (idx > -1) form.value.license_states.splice(idx, 1);
    else if(form.value.license_states) form.value.license_states.unshift(val)
    else form.value.license_states = [val];
    runSearch();
  }

  watch(region, nv => {
    if (nv && !form.value?.license_states?.length) form.value.license_states = [getStateCode(nv)];
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
