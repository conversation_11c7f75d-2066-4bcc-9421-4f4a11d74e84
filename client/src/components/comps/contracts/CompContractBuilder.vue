<template>
  <div class="_fw">
    <q-tab-panels class="_panel" animated :model-value="!!(editing || adding)">
      <q-tab-panel class="_panel" :name="false">

        <div class="q-pa-sm tw-six font-1r flex items-center">
          <div>Employment Agreements</div>
          <q-btn dense flat icon="mdi-plus" color="primary" @click="adding = true"></q-btn>
        </div>

        <div class="q-pa-sm _fw mw500">
          <q-input dense filled v-model="search.text">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>
        <div class="q-pa-sm row items-center">
          <q-icon class="q-mr-sm" name="mdi-filter" color="primary"></q-icon>
          <q-checkbox v-model="templates" label="Templates Only"></q-checkbox>
          <div class="q-ml-xs">|</div>
          <q-radio v-model="publicTemplates" :val="true" label="Public Examples"></q-radio>
          <q-radio v-model="publicTemplates" :val="false" label="Your Agreements"></q-radio>
          <q-chip v-if="!templates" clickable color="ir-bg2">
            <span v-if="personFilter">{{personFilter.name}}</span>
            <span v-else>Search By Person</span>
            <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
            <q-popup-proxy v-model="pplDialog">
              <div class="w400 mw100 q-pa-sm bg-white">
                <q-input dense filled v-model="pplSearch.text"></q-input>
                <q-list separator>
                  <q-item v-for="(prsn, i) in p$.data" :key="`prsn-${i}`" clickable @click="setPersonFilter(prsn)">
                    <q-item-section>
                      <q-item-label>{{prsn.name}}</q-item-label>
                      <q-item-label caption>{{prsn.email}}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-popup-proxy>
          </q-chip>
        </div>
        <div class="row q-py-md">
          <div class="col-12 col-md-3 q-pa-sm" v-for="(c, i) in a$.data" :key="`c-${i}`">
            <div class="__c relative-position">
              <div class="t-r">
                <q-btn dense flat icon="mdi-dots-vertical" color="accent" size="sm">
                  <q-menu>
                    <div class="w200 mw100 q-pa-sm bg-white">
                      <q-list separator>
                        <q-item clickable @click="copy(c)">

                          <q-item-section>
                            <q-item-label>Copy Contract</q-item-label>
                          </q-item-section>
                          <q-item-section side>
                            <q-icon name="mdi-content-copy" color="primary"></q-icon>
                          </q-item-section>
                        </q-item>
                        <remove-item name="Contract" @remove="remove(c)"></remove-item>
                      </q-list>
                    </div>
                  </q-menu>
                </q-btn>
              </div>
              <contract-card @click="setEditing(c)" :model-value="c"></contract-card>
            </div>
          </div>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" :name="true">
        <div class="row q-pb-sm">
          <q-btn dense flat icon="mdi-close" color="red" @click="close"></q-btn>
        </div>
        <contract-builder
          :model-value="editing"
            v-model:search="compSearch.text"
            subject-service="cams"
            :subject-options="c$.data"
            :aka="org?.dba || org?.name"
        ></contract-builder>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import ContractBuilder from 'components/contracts/forms/ContractBuilder.vue';
  import RemoveItem from 'components/common/buttons/RemoveItem.vue';
  import ContractCard from 'components/contracts/cards/ContractCard.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {useComps} from 'stores/comps';
  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {useContracts} from 'stores/contracts';

  import {copyContract} from 'components/contracts/utils/copy';
  import {useCams} from 'stores/cams';
  import {$errNotify} from 'src/utils/global-methods';
  import {usePpls} from 'stores/ppls';

  const compStore = useComps();
  const camsStore = useCams();
  const orgStore = useOrgs();
  const contractStore = useContracts();
  const pplStore = usePpls();

  const props = defineProps({
    orgId: { required: true }
  })

  const editing = ref(undefined);
  const adding = ref(false);

  const setEditing = (c) => {
    editing.value = c;
  }
  const close = () => {
    editing.value = undefined;
    adding.value = false;
  }
  const copy = async (val) => {
    const copied = copyContract(val);
    await contractStore.create({ ...copied, name: `Copy of ${val.name}` });
  }
  const remove = (val) => {
    contractStore.remove(val._id);
  }

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => props.orgId)
  ,
    useAtcStore
  })

  const { search: compSearch, searchQ: compSearchQ } = HQuery({})

  const { h$: c$ } = HFind({
    store: compStore,
    pause: computed(() => !org.value._id),
    params: computed(() => {
      return {
        query: {
          ...compSearchQ.value,
          org: org.value._id
        }
      }
    })
  })

  const personFilter = ref(undefined);
  const personCamsId = ref(undefined);

  const setPersonFilter = async (p) => {
    if(p._id) {
      personFilter.value = p;
      const cams = await camsStore.find({ person: p._id, $limit: 1, org: org.value._id })
          .catch(err => {
            $errNotify('Error filtering by person, try again')
            console.error(err.message);
          })
      if(cams?.total) personCamsId.value = cams.data[0]._id;
    } else {
      personFilter.value = undefined;
      personCamsId.value = undefined;
    }
  }

  const publicTemplates = ref(false);
  const templates = ref(true);
  const pplDialog = ref(false);

  const { search: pplSearch, searchQ: pplSearchQ } = HQuery({})
  const { h$:p$ } = HFind({
    store: pplStore,
    limit: ref(10),
    pause: computed(() => templates.value || !pplDialog.value),
    params: computed(() => {
      return {
        query: {
          cams: { $exists: true },
          inOrgs: { $in: [org.value._id] },
          ...pplSearchQ.value
        }
      }
    })
  })

  const { search, searchQ } = HQuery({})
  const { h$: a$ } = HFind({
    store: contractStore,
    params: computed(() => {
      const query = {
        ...searchQ.value,
        owner: org.value._id,
        subjectService: 'cams'
      }
      if(publicTemplates.value) query.public = true;
      if(templates.value) query.template = true
      else if(personCamsId.value) query.subject = personCamsId.value;
      return {
        query
      }
    })
  })
</script>

<style lang="scss" scoped>
  .__c {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 2px 8px -2px #999;
    padding: 30px 20px;
    cursor: pointer;
    transition: all .2s;

    &:hover {
      box-shadow: 0 4px 16px -8px #999;
    }
  }
</style>
