<template>
  <q-chip
      v-bind="{
    ...$attrs
      }"
  >

  </q-chip>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useComps} from 'src/stores/comps';
  import {useAtcStore} from 'src/stores/atc-store';

  const props = defineProps({
    modelValue: { required: true }
  })

  const store = useComps();

  const { item: comp } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })
</script>

<style lang="scss" scoped>

</style>
