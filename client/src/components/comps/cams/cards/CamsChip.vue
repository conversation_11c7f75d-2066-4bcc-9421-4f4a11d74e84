<template>
  <pay-chip
      @click="dialog = true"
      v-bind="{
    clickable: true,
    modelValue: cam,
    interval: useInterval,
    ...$attrs
      }"
  >

  </pay-chip>
  <common-dialog v-model="dialog" setting="right">
    <div class="_fw bg-white q-pa-md">
      <cams-assign :person-id="personId" :org-id="orgId" :model-value="cam"></cams-assign>
    </div>
  </common-dialog>
</template>

<script setup>
  import PayChip from 'src/components/comps/cards/PayChip.vue';
  import CamsAssign from 'src/components/comps/cams/cards/CamsAssign.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useCams} from 'src/stores/cams';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  const store = useCams();

  const props = defineProps({
    orgId: { required: true },
    personId: { required: false },
    modelValue: { required: false },
    interval: String
  })

  const dialog = ref(false);
  const fetched = ref({ total: 0, data: [] });
  const value = computed(() => {
    if (props.modelValue) return props.modelValue;
    else return fetched.value.data[0];
  })

  const { item: cam } = idGet({
    value,
    store
  ,
    useAtcStore
  })

  const useInterval = computed(() => props.interval || cam.value?.interval)

  watch(() => props.personId, async (nv, ov) => {
    if (nv && props.orgId && !props.modelValue) {
      // console.log('are we getting it?', props.orgId, nv);
      fetched.value = await store.find({
        query: {
          org: props.orgId,
          person: nv
        }
      })
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
