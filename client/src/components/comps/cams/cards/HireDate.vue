<template>
  <q-chip
      v-bind="{
    color: 'white',
    icon: 'mdi-calendar',
    label: formatDate(cam?.hireDate, 'MM/DD/YYYY'),
    ...$attrs
      }"
  >
    <q-popup-proxy v-if="edit">
      <div class="w300 mw100 bg-white">
        <q-date
            :model-value="cam?.hireDate"
            @update:model-value="setHire"
        ></q-date>
      </div>
    </q-popup-proxy>
  </q-chip>
</template>

<script setup>
  import {formatDate} from 'src/utils/date-utils';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useCams} from 'stores/cams';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useCams();
  const emit = defineEmits(['update:date'])
  const props = defineProps({
    modelValue: { required: true },
    edit: Boolean,
    emitUp: <PERSON>olean
  })

  const { item:cam } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })

  const setHire = async (val) => {
    if(props.emitUp) emit('update:date', val);
    else {
      store.patchInStore(cam.value._id, { hireDate: val });
      await store.patch(cam.value._id, { hireDate: val });
    }
  }
</script>

<style lang="scss" scoped>

</style>
