<template>
  <q-chip v-bind="{ color: 'transparent', ...$attrs}" @click="$emit('update:model-value', person)">
    <slot name="avatar">
      <q-avatar size="15px" :color="bg"></q-avatar>
    </slot>
    <span class="q-mx-xs text-uppercase font-3-4r">{{usePerson?.gender || 'Unknown'}}<span>&nbsp;({{age}})</span></span>
    <slot name="side"></slot>
  </q-chip>
</template>

<script setup>

  import {computed, ref} from 'vue';
  import { dateDiff } from 'src/utils/date-utils';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {useAtcStore} from 'src/stores/atc-store';

  const pplsStore = usePpls();

  const props = defineProps({
    modelValue: { required: true },
    isPerson: <PERSON>ole<PERSON>,
  })

  const { item:person } = idGet({
    value: computed(() => props.modelValue),
    store: pplsStore
  ,
    useAtcStore
  })

  const images = ref({
    male: {
      0: 'light-blue',
      1: 'light-blue',
      2: 'light-blue'
    },
    female: {
      0: 'pink-2',
      1: 'pink-2',
      2: 'pink-2'
    }
  });

  const convertPerson = () => {
    const { name } = person.value || { name: '' };
    const ns = name?.split(' ') || [];
    return { lastName: ns[ns.length - 1], firstName: ns[0], ...person.value };
  }

  const usePerson = computed(() => {
    return convertPerson()
  })


  const age = computed(() => {
    const { dob } = { dob: new Date(), ...usePerson.value };
    return Math.floor((dateDiff(new Date(), dob, 'months') || 0)/12);
  })


  const bg = computed(() => {
    const gender = usePerson.value?.gender;
    if(gender) {
      const a = age.value || age.value === 0 ? age.value : 30;
      const key = a < 2 ? 0 : a < 19 ? 1 : 2;
      return images.value[gender][key];
    } else return 'ir-grey-4';
  })
</script>

<style lang="scss" scoped>

</style>
