<template>
  <div class="_fw">
    <div class="_form_grid">
      <!--      FIRST NAME-->
      <div class="_form_label">Name</div>
      <div class="row">
        <div class="col-6 q-pa-sm">
          <q-input v-bind="inputAttrs" label="First Name..." v-model="form.firstName"
                   @update:model-value="autoSave('firstName')"></q-input>
        </div>
        <div class="col-6 q-pa-sm">
          <q-input v-bind="inputAttrs" label="Last Name" v-model="form.lastName"
                   @update:model-value="autoSave('lastName')"></q-input>
        </div>
      </div>
      <!--      DOB-->
      <div class="_form_label">Date of Birth</div>
      <div class="q-pa-sm">
        <dob-input v-bind="inputAttrs" v-model="form.dob" @update:model-value="autoSave('dob')"></dob-input>
      </div>
      <!--      SSN-->
      <template v-if="!limited">
        <div class="_form_label">Tax ID</div>
        <div class="q-pa-sm">
          <ssn-input v-bind="inputAttrs" v-model="form.ssn" @update:model-value="autoSave('ssn')"></ssn-input>
        </div>
      </template>
      <!--      GENDER-->
      <div class="_form_label">Gender</div>
      <div class="q-pa-sm">
        <gender-picker v-model="form.gender" @update:model-value="autoSave('gender')" size="24px"></gender-picker>
      </div>

      <template v-if="!form._id">
        <div class="_form_label"></div>
        <div class="q-px-sm q-py-lg">
          <q-btn push color="primary" class="tw-six" @click="save()">
            <span>Save Member</span>
            <q-icon class="q-ml-sm" color="p10" name="mdi-content-save"></q-icon>
          </q-btn>
        </div>
      </template>


      <slot name="bottom" :form="form"></slot>
    </div>
  </div>
</template>

<script setup>
  import DobInput from 'components/common/input/DobInput.vue';
  import SsnInput from 'components/common/input/SsnInput.vue';
  import GenderPicker from 'components/market/household/GenderPicker.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  const pplsStore = usePpls();
  const hhStore = useHouseholds();

  import {computed, ref, watch} from 'vue';
  import {usePpls} from 'stores/ppls';
  import {useHouseholds} from 'stores/households';
  import {idGet} from 'src/utils/id-get';
  import {_get} from 'symbol-syntax-utils';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    limited: Boolean,
    enrollmentId: { required: false },
    household: { required: false },
    modelValue: Object,
    specialChange: Boolean,
    inputAttrs: {
      default: () => {
        return {
          borderless: true,
          class: '_inp'
        }
      }
    }
  })

  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => props.household || props.modelValue?.household)
  ,
    useAtcStore
  })

  const formFn = (defs) => {
    return {
      lastName: undefined,
      firstName: undefined,
      dob: undefined,
      ssn: undefined,
      gender: undefined,
      cleanupFlag: true,
      ...defs
    }
  }

  const form = ref(formFn());
  const patchObj = ref({});
  const saveTo = ref();

  const save = async (patchObj) => {
    if (form.value)
        // console.log('save top', patchObj);
      if (Object.keys(patchObj || {}).length && form.value.firstName && form.value.lastName) {
        if (form.value?._id) {
          pplsStore.patchInStore(form.value._id, patchObj)
          const f = await pplsStore.patch(form.value._id, patchObj, { special_change: props.specialChange ? '*' : undefined });
          if (f) form.value = formFn(f);
        } else {
          const person = await pplsStore.create(patchObj);
          form.value = formFn(person)
        }
      }
    /** update household */

    // console.log('run patch?', form.value._id)
    if (form.value._id && form.value._id !== hh.value.person) {
      // console.log('run patch', form.value._id)

      await hhStore.patch(hh.value._id, { $set: { [`members.${form.value._id}`]: {} } }, {
        special_change: props.specialChange ? '*' : undefined,
        runJoin: { hh_members: true }
      })
    }
    emit('update:model-value', form.value);
  }

  const autoSave = (path) => {
    if (saveTo.value) clearTimeout(saveTo.value);
    patchObj.value[path] = _get(form.value, path);
    saveTo.value = setTimeout(() => {
      save(patchObj.value)
    }, 1500)
  }

  watch(() => props.modelValue, (nv) => {
    if (nv) {
      form.value = formFn(nv);
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
