<template>
  <div class="_fw">
    <div class="_form_grid _f_g_r">
      <div class="_form_label">Type</div>
      <div class="q-pa-sm">
        <issue-type :model-value="issue?.type"></issue-type>
      </div>
      <div class="_form_label">By</div>
      <div class="q-pa-sm">
        <default-chip
            :chip-attrs="{ color: 'transparent' }"
            :model-value="issue?.by"
            :store="pplsStore"
            :use-atc-store="useAtcStore"
        >
          <template v-slot:menu="scope">
            <q-menu @update:modelValue="setMenuPerson($event, scope.item)">
              <div class="w300 mw100 bg-white q-pa-md">
                <div class="tw-six">{{ person?.name }}</div>
                <div class="row items-center q-py-xs">
                  <q-icon color="primary" name="mdi-email" class="q-mr-sm"></q-icon>
                  <div>{{ person?.email }}</div>
                </div>
                <div class="row items-center q-py-xs">
                  <q-icon color="primary" name="mdi-phone" class="q-mr-sm"></q-icon>
                  <div>{{ person?.phone?.number?.national }}</div>
                </div>

                <q-separator class="q-my-sm"></q-separator>
                <default-chip v-for="(org, i) in o$.data" :key="`org-${i}`" :model-value="org" :use-atc-store="useAtcStore"></default-chip>
              </div>
            </q-menu>
          </template>
        </default-chip>
      </div>
      <div class="_form_label">Company</div>
      <div class="q-pa-sm">
        <org-select-chip emit-value :model-value="issue?.org" @update:model-value="update($event, 'org')"></org-select-chip>
      </div>
      <div class="_form_label">Date</div>
      <div class="q-pa-sm">
        <q-chip color="transparent">{{ $ago(issue?.createdAt) }}</q-chip>
      </div>
      <div class="_form_label">Category</div>
      <div class="q-pa-sm">
        <span class="q-mx-sm">{{ issue?.category }}</span>
      </div>
      <div class="_form_label">Message</div>
      <div class="q-pa-sm">
        <div class="q-pa-sm bg-ir-grey-1 br5">{{ issue?.message }}</div>
      </div>
      <div class="_form_label">Channel</div>
      <div class="q-pa-sm">
        <issue-channel
            color="transparent"
            :model-value="issue?.channel" :editing="editing" @update:model-value="update($event, 'channel')"
        ></issue-channel>
      </div>
      <div class="_form_label">Assigned</div>
      <div class="q-pa-sm">
        <default-chip :model-value="manualAssigned?._id ? manualAssigned : assigned"
                      :chip-attrs="{ color: 'transparent', iconRight: editing ? 'mdi-menu-down' : undefined}"
                      :use-atc-store="useAtcStore">
          <template v-slot:menu>
            <q-menu v-if="editing">
              <div class="w300 mw100 bg-white q-pa-md">
                <people-list emit-value @update:modelValue="assign" :model-value="issue.assigned"></people-list>
              </div>
            </q-menu>
          </template>
        </default-chip>
      </div>

      <div class="_form_label">Status</div>
      <div class="q-pa-sm">
        <issue-status
            color="transparent"
            :model-value="issue?.status"
            :editing="editing"
            @update:model-value="update($event, 'status')"
        ></issue-status>
      </div>
      <template v-if="issue?.status === 'resolved'">
        <div class="_form_label">Resolved On</div>
        <div class="q-pa-sm">
          <q-chip color="transparent">{{ formatDate(issue?.resolvedAt, 'MMM DD, YYYY h:mm a') }}</q-chip>
        </div>
      </template>
      <div class="_form_label">Treasury Complaint</div>
      <div class="q-pa-sm">
        <q-checkbox :disable="!editing" :model-value="!!issue.treasuryComplaint"
                    @update:modelValue="setTC"></q-checkbox>
      </div>
    </div>

  </div>
</template>

<script setup>
  import IssueType from 'components/issues/cards/IssueType.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import PeopleList from 'components/ppls/lists/PeopleList.vue';
  import IssueStatus from 'components/issues/cards/IssueStatus.vue';
  import IssueChannel from 'components/issues/cards/IssueChannel.vue';

  import {idGet} from 'src/utils/id-get';
  import {useAtcStore} from 'src/stores/atc-store';
  import {computed, ref} from 'vue';
  import {useIssues} from 'stores/issues';
  import {HFind} from 'src/utils/hFind';
  import {useOrgs} from 'stores/orgs';
  import {usePpls} from 'stores/ppls';
  import {$ago} from 'src/utils/global-methods';
  import {formatDate} from 'src/utils/date-utils';
  import OrgSelectChip from 'components/orgs/lists/OrgSelectChip.vue';

  const store = useIssues();
  const orgStore = useOrgs();
  const pplsStore = usePpls();
  const props = defineProps({
    modelValue: { required: true },
    editing: Boolean
  })

  const { item: issue } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const { item: assigned } = idGet({
    store: pplsStore,
    value: computed(() => issue.value.assigned)
  ,
    useAtcStore
  })

  const menuPerson = ref(undefined);
  const person = computed(() => {
    if (menuPerson.value?._fastjoin?.owner) return menuPerson.value._fastjoin.owner
    return menuPerson.value;
  });
  const setMenuPerson = (val, p) => {
    if (val) menuPerson.value = p;
    else menuPerson.value = undefined
  }
  const update = async (val, path) => {
    const obj = { $set: { [path]: val } }
    store.patchInStore(issue.value._id, obj)
    await store.patch(issue.value._id, obj)
  }
  const setTC = (val) => {
    const obj = { $set: { treasuryComplaint: val } }
    store.patchInStore(issue.value._id, obj)
    store.patch(issue.value._id, obj)
  }

  const manualAssigned = ref({});
  const assign = async (p) => {
    const obj = p ? { assigned: p, assignedAt: new Date() } : { $unset: { assigned: '' } }
    if (issue.value?.assigned) {
      obj.$addToSet = { assignedHistory: { id: issue.value.assigned, at: issue.value.assignedAt } }
    }
    store.patchInStore(issue.value._id, { assigned: p })
    store.patch(issue.value._id, obj)
    if (p) manualAssigned.value = await pplsStore.get(p);
  }

  const { h$: o$ } = HFind({
    store: orgStore,
    pause: computed(() => !person.value),
    params: computed(() => {
      return {
        query: {
          _id: { $in: (person.value?.inOrgs || []).filter(a => !!a) }
        }
      }
    })
  })

</script>

<style lang="scss" scoped>
  table {
    border-collapse: collapse;
    width: 100%;

    tr {
      td {
        padding: 5px 10px;
        border-bottom: solid .3px #999;
        text-align: center;
      }
    }
  }
</style>
