<template>
  <q-page class="flex flex-center" style="min-height: 80vh;">
    <q-slide-transition>
      <div v-if="hideResume || !responses.total">
        <q-card flat class="bg-transparent q-pb-xl" style="height: 100%; width: 800px; max-width: 100%">
<!--          <div class="row q-mt-md">-->
<!--            <div class="text-weight-bold text-xs text-mb-sm">-->
<!--              <div class="cursor-pointer text-ir-mid" style="display: flex; align-items: center"-->
<!--                   @click="formShow === 'slider' ? formShow = 'stack' : formShow = 'slider'">-->
<!--                <q-icon class="q-mr-xs"-->
<!--                        :name="formShow === 'slider' ? 'mdi-stack-exchange' : 'mdi-page-next-outline'"></q-icon>-->
<!--                <div>{{ formShow === 'slider' ? 'stack view' : 'slider view' }}</div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->

          <form-slider
            :preview-mode="previewMode"
            :display-in="formShow"
            @finish="formDone"
            :tab-in="tab" @tab="tab = $event"
            :form-in="form"
            :response-in="resume"
          />
        </q-card>
      </div>
    </q-slide-transition>
    <q-slide-transition>
      <div v-show="!hideResume && responses.total" style="width: 100%" class="q-pa-md">
        <div class="row justify-center">
          <div class="w500 mw100 q-pa-md text-center">
            <div class="text-sm tw-six">You have started this form before - would you like to resume?</div>

            <div class="row justify-center q-pt-lg">
              <div class="q-px-md">
                <q-btn flat no-caps @click="hideResume = true">
                  <span class="q-mr-sm">No</span>
                  <q-icon color="red" name="mdi-close"></q-icon>
                </q-btn>
              </div>
              <div class="q-px-md">
                <q-btn flat no-caps @click="resume = responses.data[0]">
                  <span class="q-mr-sm">Yes</span>
                  <q-icon color="green" name="mdi-check"></q-icon>
                </q-btn>
              </div>
            </div>
          </div>
        </div>
      </div>
    </q-slide-transition>
  </q-page>
</template>

<script setup>
  // import { models } from 'feathers-vuex';
  import FormSlider from '../cards/FormSlider.vue';
  import {idGet} from 'src/utils/id-get';
  import {useFbs} from 'stores/fbs';
  import {computed, ref, watch} from 'vue';
  import {useRoute} from 'vue-router';
  import {LocalStorage} from 'symbol-auth-client';
  import {fakeId} from 'src/utils/global-methods';
  import {useFbRes} from 'stores/fb-res';
  import {loginPerson} from 'stores/utils/login';
  // import LinkCard from '../../components/common/atoms/LinkCard';
  import {useAtcStore} from 'src/stores/atc-store';

  const { person } = loginPerson();

  const fbStore = useFbs();
  const fbrStore = useFbRes();
  const route = useRoute();

  const props = defineProps({
    modelValue: { required: false },
    previewMode: Boolean
  })

  const formId = computed(() => props.modelValue || route.params.formId)

  const { item:form } = idGet({
    store: fbStore,
    value: formId
  ,
    useAtcStore
  })

  const formShow = ref('slider')
  const tab = ref(0)
  const done = ref(false)
  const hideResume = ref(false)
  const responses = ref({})
  const resume = ref()

  const resumeQuery = computed(() => {
    const id = formId.value
    const query = { form: id, $limit: 1 };
    if(person.value._id) query.person = person.value._id;
    else query.fingerprints = { $in: [LocalStorage.getItem('fpId') || fakeId] };
    return query;
  })

  const formDone = () => {
    done.value = true;
  }

  watch(formId, async (nv, ov) => {
    if(nv && nv !== ov){
      const res = await fbrStore.find({
        query: resumeQuery.value
      })
      if(res) responses.value = res;
    }
  }, { immediate: true })

</script>
