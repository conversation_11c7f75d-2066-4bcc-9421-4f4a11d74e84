<template>
  <div class="fill_size q-pa-md" style="overflow-y: scroll">
    <div class="row items-center q-my-md text-sm text-mb-md text-weight-medium">Create {{ exit ? 'Exit' : 'Welcome' }}
      Screen
      <q-space/>
      <q-btn icon="mdi-eye" flat size="sm" color="primary" label="Preview Changes" @click="saveAll"/>
    </div>
    <div class="col-12 q-gutter-sm">
      <q-input @update:model-value="saveAll" input-class="text-xs text-mb-sm q-my-sm" outlined hide-bottom-space
               :label="exit ? 'Exit' : 'Welcome' + ' Title'" v-model="title"></q-input>

      <q-editor class="comment-rich-editor"
                v-model="message"
                @update:model-value="addMessage"
                :model-value="message"
                :definitions="{save: {
                                        tip: 'Add message',
                                        icon: 'send',
                                        handler: addMessage,
                                        color: 'blue',
                                      }
                                    }"
                :toolbar="[...editorToolbar, ['upload', 'save']]"/>

    </div>

    <q-expansion-item expand-separator expand-icon="mdi-menu-down">
      <template v-slot:header>
        <q-item>
          <q-item-section avatar>
            <q-btn round push color="primary" icon="mdi-image-area"></q-btn>
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-xs text-mb-sm">Add {{ exit ? 'Exit' : 'Welcome' }} Image</q-item-label>
            <q-item-label class="text-xxs text-mb-xs text-accent">
              {{ image ? 'Image Added' : '' }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </template>
      <q-separator/>
      <div class="row justify-center q-my-md">
        <image-form v-model="image" @update:model-value="saveAll" :use-atc-store="useAtcStore"></image-form>
      </div>
    </q-expansion-item>

    <q-expansion-item expand-separator expand-icon="mdi-menu-down">
      <template v-slot:header>
        <q-item>
          <q-item-section avatar>
            <q-btn round push color="primary" icon="mdi-video-vintage"></q-btn>
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-xs text-mb-sm">Add {{ exit ? 'Exit' : 'Welcome' }} Video</q-item-label>
            <q-item-label class="text-xxs text-mb-xs text-accent">
              {{ videos && videos.length ? videos.length + ' Videos Added' : '' }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </template>
      <q-separator/>

      <div class="row justify-center q-my-md">
        <div class="w600 mw100 q-pa-lg">
          <video-form :model-value="(videos || [])[0]" @update:model-value="videos[0] = $event;saveAll()"></video-form>
        </div>
      </div>
    </q-expansion-item>

    <q-expansion-item expand-separator expand-icon="mdi-menu-down">
      <template v-slot:header>
        <q-item>
          <q-item-section avatar>
            <q-btn round push color="primary" icon="mdi-download"></q-btn>
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-xs text-mb-sm">Add Files For Download</q-item-label>
            <q-item-label class="text-xxs text-mb-xs text-accent">
              {{ files && files.length ? files.length + ' Files Added' : '' }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </template>
      <q-separator/>

      <div class="q-my-md">
        <div class="row">
          <div class="col-12 q-px-lg q-py-sm">
            <div class="row justify-center">
              <file-uploader-chip :upload="upload"></file-uploader-chip>
            </div>
          </div>
          <div class="col-12 q-pa-sm">
            <div style="width: 100%; overflow-x: scroll"
                 :class="`row no-wrap ${files.length === 1 ? 'justify-center' : ''}`">
              <q-card flat v-for="(file, i) in files" :key="`file-${i}`" class="q-ma-xs">
                <q-btn size="sm" class="t-l" dense push color="dark" icon="mdi-close" @click="files.splice(i, 1)"/>
                <file-preview nameOn :file="file"></file-preview>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </q-expansion-item>

    <div class="row justify-end">
      <q-btn icon="mdi-eye" push color="primary" dense @click="saveAll">
        <q-tooltip content-class="bg-dark text-light text-xxs text-mb-xs">preview Changes</q-tooltip>
      </q-btn>
    </div>
  </div>
</template>

<script setup>
  import FileUploaderChip from 'components/common/uploads/files/FileUploaderChip.vue';
  import FilePreview from 'components/common/uploads/files/FilePreview.vue';
  import ImageForm from 'components/common/uploads/images/ImageForm.vue';
  import VideoForm from 'components/common/uploads/video/VideoForm.vue';

  import {ref, watch} from 'vue';
  import { editorToolbar } from '../utils/editor';
  import {storjUpload} from 'components/common/uploads/services';
  import {useAtcStore} from 'src/stores/atc-store';

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    exit: Boolean,
    videosIn: Array,
    filesIn: Array,
    imageIn: Object,
    messageIn: String,
    titleIn: String
  })


  const title = ref('');
  const message = ref('');
  const files = ref([])
  const videos = ref([])
  const image = ref()

  watch(() => props.titleIn, (nv) => {
    if (nv) title.value = nv
  }, { immediate: true })

  watch(() => props.messageIn, (nv) => {
    if (nv) message.value = nv
  }, { immediate: true })

  watch(() => props.videosIn, (nv) => {
    if (nv) videos.value = nv
  }, { immediate: true })

  watch(() => props.filesIn, (nv) => {
    if (nv) files.value = nv
  }, { immediate: true })

  watch(() => props.imageIn, (nv) => {
    if (nv) image.value = nv
  }, { immediate: true })

  const saveTo = ref()
  const saveAll = () => {
    if(saveTo.value) clearTimeout(saveTo.value)
    saveTo.value = setTimeout(() => {
      const obj = {
        image: image.value,
        files: files.value,
        videos: videos.value,
        message: message.value,
        title: title.value
      }
      emit('update:model-value', obj);
    }, 1000)

  }
  const addMessage = (msg) => {
    message.value = msg;
    saveAll();
  }

  const addFile = (val) => {
    if (Array.isArray(val)) {
      for (const file of val) {
        this.files.push(file);
      }
    } else this.files.push(val)
    saveAll();
  }

  const uploadEmit = (evt, args) => {
    const obj = {
      'update:model-value': () => {
       addFile(args)
      }
    };
    if(obj[evt]) obj[evt]()
    else console.log('no evt handler for event ', evt);
  };

  const upload = storjUpload(undefined, { emit: uploadEmit })


</script>
