
import { getAge} from '../../services/enrollments/utils/index.js';

const highest = Number.POSITIVE_INFINITY;

export const graduation: { [status: string]: { [key: number]: number } } = {
    s: { // Single Filers
        0: 11925,
        1: 48475,
        2: 103350,
        3: 197300,
        4: 250525,
        5: 626350,
        6: highest
    },
    ms: { // Married Filing Separately
        0: 11925,
        1: 48475,
        2: 103350,
        3: 197300,
        4: 250525,
        5: 375800,
        6: highest
    },
    hh: { // Head of Household
        0: 17000,
        1: 64850,
        2: 103350,
        3: 197300,
        4: 250500,
        5: 626350,
        6: highest
    },
    mj: { // Married Filing Jointly
        0: 23850,
        1: 96950,
        2: 206700,
        3: 394600,
        4: 501050,
        5: 751600,
        6: highest
    }
};
export const standardDeduction = {
    s: 14600,
    ms: 14600,
    hh: 21900,
    mj: 29200
}

export const taxRates:{ [key:number]: number } = {
    0: .1,
    1: .12,
    2: .22,
    3: .24,
    4: .32,
    5: .35,
    6: .37
}


type NumObj = {[key:string]:number};
export const childTaxCredit = ({ magi }: NumObj, household:any):number => {
    const limit = household?.filingAs === 'mj' ? 400000 : 200000;
    let total = 0;
    if(magi <= limit){
        for(const k in household.members || {}){
            const member = household.members[k];
            if(member.dob && member.dependent){
                const eoyAge = member.age || member.dob ? getAge(member.dob) : 16;
                if(eoyAge < 17) total += 2000;
            }
        }
    }
    return total;
}

export const earnedIncomeCredit = ({ agi, investment }:NumObj, household:any) => {
    if(investment > 11000) return 0;
    const deps = Object.keys(household.members || {}).filter(a => household.members[a].dependent).length || 0;
    //limits [allOthers, marriedFJointly, amount];
    const limits:{[key:number]:[number,number,number]} = {
        0: [18591,25511,632],
        1: [49084,56004,4213],
        2:[55768,62688,6960],
        3:[59899,66819,7830]
    }
    const idx = household?.filingAs === 'mj' ? 1 : 0;
    const arr = limits[deps] || [0,0,0];
    if(arr[idx] <= agi) return 0;
    else return arr[2];
}

type QuickOptions = {income:number, filing_as?:'s'|'ms'|'hh'|'mj', hh_members?: { [key:string]: { dob?:string, age?:number, dependent?:boolean } } & any, pretax_ee?:number, pretax_er?:number, passive_inc?:number }
export const quickTaxTotal = ({
                                  income,
                                  hh_members,
                                  pretax_ee,
                                  // pretax_er,
                                  // passive_inc,
                                  filing_as
                              }:QuickOptions) => {
    const fa = filing_as || 's'
    const ee = pretax_ee || 0;
    // const er = pretax_er || 0;
    // const pyTax = getPayrollTax(income - ee, 0, { filingAs: fa })
    const pyTax = 0;
    const sd = standardDeduction[fa];
    const agi = income - ee - sd
    let tax = 0;
    let last = 0;
    const brackets:{[key:number|string]: number} = graduation[fa] as { [key:number|string]: number }
    for(const k in Object.keys(brackets)){
        const bracket = brackets[k];
        if(agi >= bracket){
            tax += ((bracket - last) * taxRates[k])
            last = bracket;
        } else {
            tax += ((agi - last) * taxRates[k]);
            break;
        }
    }
    if(tax < 0) tax = 0;
    const incomeObj = { agi, magi:agi, investment: 0 };
    let adjustMembers = {}
    let credits: { [key: string]: number }|any = []
    if(Array.isArray(hh_members)) {
        adjustMembers = [...hh_members || []].map(a => {
            if (a.relation === 'child' || (a.age || 18) < 18) a.dependent = true;
            return a
        })
        credits = {
            child_tax_credit: childTaxCredit(incomeObj, {members: adjustMembers} as any),
            earned_income_credit: earnedIncomeCredit(incomeObj, {members: adjustMembers} as any)
        };
    }
    let adjustedTax = tax;
    let creditTotal = 0;
    for(const cred in credits){
        creditTotal += (credits[cred] || 0);
    }
    adjustedTax -= creditTotal;
    return { magi:agi, agi, adjustedTax, tax, payrollTax:pyTax, credits, creditTotal, rate: adjustedTax / income }
}
