import {fastJoin} from 'feathers-hooks-common';
import {_get, _set, stringifyPath} from './dash-utils.js';
import {AnyObj} from "./types.js";
import {HookContext} from "../declarations.js"
import {CoreCall, NullableId} from 'feathers-ucan';

export type Through = {
    [key: string]: {
        service?: string,
        servicePath?: string,
        params?: AnyObj,
        through?: AnyObj,
        find?:boolean,
        q?:(item:any) => AnyObj,
        joinParent?:string
    }
}
export type GetJoinOptions = {
    name?: string,
    service?: string,
    servicePath?: string,
    herePath: string | string[],
    getId?: string,
    joinPath?: string | string[],
    params?: AnyObj,
    through?: Through,
    errMessage?: string,
    passError?: boolean,
    therePath?: string,
    joinFn?: <Item, JoinItem>(item: Item, joinItem: JoinItem) => void
    throwErr?: boolean,
    coreOpts?:any
}

export const getJoin = (
    {
        name,
        service,
        therePath,
        servicePath,
        herePath,
        getId,
        joinPath,
        through,
        params = {},
        errMessage,
        passError = true,
        joinFn,
        throwErr,
        coreOpts
    }: GetJoinOptions): (c: HookContext) => Promise<HookContext> => {
    return async (context: HookContext): Promise<HookContext> => {
        if(context.params.core.skipJoins) return context;
        const hPath = stringifyPath(herePath);
        const stringHPath = hPath.split('/*').join('');
        let jPath = joinPath ? joinPath : stringHPath;
        let errorMessage = errMessage ? errMessage : `could not join ${service} ${hPath}`;
        if (_get(context.params, `joining.${hPath}`)) return context;
        context.params.joining = _set(context.params.joining, hPath, true);
        const config = {
            joins: {
                // eslint-disable-next-line no-unused-vars
                [stringHPath]: ($select: any) => async (item: any, context: HookContext) => {
                    let item_id = getId ? getId : _get(item, hPath);
                    let s = service ? service : _get(item, servicePath || '') as string;
                    if (item_id && s) {
                        // console.log('get in get join', context.type, ' - ', context.method, ' - ', context.path);

                        let customParams = {};
                        if ($select) {
                            customParams = _set(customParams, 'query.$select', $select);
                        }

                        // console.log('getting join item', item_id, herePath, item[herePath]);
                        let joinItem = await new CoreCall(s, context, coreOpts).get(item_id as NullableId, {
                            ...customParams,
                            core: context.params.core, ...params
                        })
                            .catch(err => {
                                let msg = errorMessage + passError ? ` ${err.message}` : '';
                                // console.error(msg);
                                if (throwErr) throw new Error(msg);
                                return;
                            });
                        if (through) {
                            let ts = s;
                            const layer = async (path: any, thru: any) => {
                                if(thru.joinParent) item._fastjoin = _set(item._fastjoin, thru.joinParent, joinItem)
                                if(thru.find){
                                    const data = await new CoreCall(thru.service, context).find({ query: thru.q(joinItem), ...thru.params})
                                        .catch(err => {
                                            let msg = errorMessage + passError ? ` ${err.message}` : '';
                                            console.error(`Error in getJoin through loop: ${msg}`);
                                            if (throwErr) throw new Error(msg);
                                            return;
                                        })
                                    if(data) joinItem = data.data
                                } else {
                                    const nextId = _get(joinItem, [path])
                                    ts = thru.service || _get(joinItem, thru.servicePath);
                                    if (!ts || !nextId) return;
                                    joinItem = await new CoreCall(ts, context, coreOpts).get(nextId as any, {admin_pass: true, ...thru.params || {}})
                                        .catch(err => {
                                            let msg = errorMessage + passError ? ` ${err.message}` : '';
                                            console.error(`Error in getJoin through loop: ${msg}`);
                                            if (throwErr) throw new Error(msg);
                                            return;
                                        })
                                    if (thru.through) await Promise.all(Object.keys(thru.through).map(a => layer(a, thru.through[a])));
                                }
                            }
                            if (!ts) return;
                            await Promise.all(Object.keys(through).map(a => layer(a, through[a])))
                        }
                        // console.log('get join item', s);
                        if (joinFn) {
                            joinFn<typeof item, typeof joinItem>(item, joinItem);
                        } else {
                            if (!Array.isArray(jPath)) jPath = [jPath];
                            item._fastjoin = _set(item._fastjoin || {}, jPath, joinItem)
                        }
                    }
                },
            },
        };
        return fastJoin(config as any)(context as any) as any;
    }
};

export type FindJoinOptions = {
    name?: string,
    service: string,
    therePath: string,
    servicePath?: string,
    limit?: number,
    herePath: string | string[],
    findIds?: Array<string>,
    joinPath?: string | string[],
    params?: AnyObj,
    errMessage?: string,
    passError?: boolean,
    joinFn?: <Item, JoinItem>(item: Item, joinItems: Array<JoinItem>) => void,
    query?: AnyObj,
    queryFn?: <Data>(item: Data) => AnyObj
    throwErr?: boolean,
    resultPath?: string,
    coreOpts?:any
}

export const findJoin = (
    {
        name,
        service,
        servicePath,
        herePath,
        findIds,
        therePath = '_id',
        joinPath,
        limit = 25,
        params = {},
        errMessage,
        passError = true,
        throwErr,
        query,
        queryFn,
        joinFn,
        coreOpts,
        resultPath = 'data',
    }: FindJoinOptions): (c: HookContext) => Promise<HookContext> => {
    return async (context: HookContext): Promise<HookContext> => {
        if(context.params.core.skipJoins) return context;
        const hPath = stringifyPath(herePath);
        if (_get(context.params, `joining.${hPath}`)) return context;
        context.params.joining = _set(context.params.joining, hPath, true);

        let jPath = joinPath ? Array.isArray(joinPath) ? joinPath : [joinPath] : herePath;
        let errorMessage = errMessage ? errMessage : `could not join ${service} ${hPath}`;
        const config = {
            joins: {
                // eslint-disable-next-line no-unused-vars
                [hPath]: ($select: any, customParams = {}) => async (item: any, context: HookContext) => {
                    let joinService = service ? service : _get(item, servicePath || '');
                    if (joinService) {
                        let q = query ? query : queryFn ? queryFn<typeof item>(item) : {};

                        if (!Object.keys(q || {})?.length) {
                            let item_ids = findIds ? findIds : _get(item, hPath);
                            if (item_ids) q = {[therePath]: {$in: item_ids}};
                        }

                        if (q) {
                            customParams = {
                                $client_join: {
                                    ...customParams,
                                },
                                query: {
                                    $limit: limit,
                                    ...q
                                },
                            };
                            if ($select) {
                                _set(customParams, 'query.$select', $select);
                            }

                            let joinItems = await new CoreCall(joinService as any, context, coreOpts).find({
                                ...customParams,
                                core: context.params.core,
                                admin_pass: true,
                                ...params
                            })
                                .catch((err: any) => {
                                    let msg = errorMessage + passError ? ` ${err.message}` : '';
                                    console.error(msg);
                                    if (throwErr) throw new Error(msg);
                                });

                            // let result = resultPath ? _get(joinItems, resultPath) : joinItems;

                            if (joinFn) {
                                joinFn<typeof item, typeof joinItems>(item, joinItems);
                            } else {
                                if (!Array.isArray(jPath)) jPath = [jPath];
                                item._fastjoin = _set(item._fastjoin, jPath, joinItems?.data);
                            }
                        }
                    }
                },
            },
        };
        return fastJoin(
            config as any
        )(context as any) as any;
    }
};

