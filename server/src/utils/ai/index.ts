import axios from 'axios';
import {Readable} from 'stream';
import OpenAi from 'openai';
import {HookContext} from '../../declarations.js';
import {File, FormData} from 'formdata-node';
import {FormDataEncoder} from 'form-data-encoder';
import {CoreCall} from 'feathers-ucan';


export const clearVectorStore = (vectorStoreId) => {
    return async (context: HookContext) => {
        const {key, org} = context.app.get('openai');
        const openai = new OpenAi({apiKey: key, organization: org})

        const files = await openai.vectorStores.files.list(vectorStoreId)
            .catch(err => {
                console.log(`Error listing vector store files: ${err.message}`)
                return {data: []}
            })
        for (const file of files.data) {
            await openai.vectorStores.files.del(vectorStoreId, file.id as any)
                .catch(err => {
                    console.log(`Error deleting old  vector file id ${file.id}: ${err.message}`)
                })
        }
        return context;
    }

}

export const removeVectorStore = (vectorStoreId) => {
    return async (context: HookContext) => {
        const {key, org} = context.app.get('openai');
        const openai = new OpenAi({apiKey: key, organization: org})
        await clearVectorStore(vectorStoreId)(context)
            .catch(err => {
                console.error(`Could not clear files when removing vector store: ${vectorStoreId} - ${err.message}`)
            })
        await openai.vectorStores.del(vectorStoreId)
            .catch(err => {
                throw new Error(`Error deleting vector store: ${err.message}`)
            })
        return context;
    }
}
type VectorStoreOptions = {
    taskName: string,
    storeName: string,
    fileName: string,
    mimeType: string,
    buffer: Buffer,
    vectorConfig: { id: string, fileIds: string[], updatedAt: any } & any,
    returnReadable?: boolean
}
export const addFileToVectorStore = async (context: HookContext, {
    mimeType,
    buffer,
    vectorConfig,
    taskName,
    storeName,
    fileName,
    returnReadable
}: VectorStoreOptions) => {
    const {key, org} = context.app.get('openai');
    const openai = new OpenAi({apiKey: key, organization: org})

    const f = new File([buffer], `${fileName}`, {type: mimeType});
    const frm = new FormData()
    frm.append('file', f)
    frm.append('purpose', 'assistants')
    const encoder = new FormDataEncoder(frm as any);
    const readable = Readable.from(encoder.encode())
    const docsRes: any = await axios.post(
        'https://api.openai.com/v1/files',
        readable,
        {
            headers: {
                'Authorization': `Bearer ${key}`,
                ...encoder.headers
            }
        }).catch(err => {
        console.log(`Error uploading doc for vector store ${taskName}: ${err.message}`)
        throw new Error(`Error uploading ${taskName}: ${err.message}`)
    })
    const docsFile = docsRes.data;
    const {id} = vectorConfig || {};
    let vectorStoreId = id;
    if (vectorStoreId) {
        await clearVectorStore(vectorStoreId)(context);
    }
    if (!vectorStoreId) {
        const res = await openai.vectorStores.create({
            name: String(storeName)
        })
            .catch(async err => {
                console.error(`Error initializing vector store for shop: ${err.message}`);
                await openai.files.del(docsFile.id)
                    .catch(err => {
                        console.log(`Error deleting old ${taskName} vector file id after vector create fail ${docsFile.id}: ${err.message}`)
                    })
                throw new Error(`Error initializing vector store for ${taskName}: ${err.message}`)
            })
        vectorStoreId = res.id;
    }
    /** add file to vector store */
    await openai.vectorStores.files.create(vectorStoreId, {
        file_id: docsFile.id
    })
        .catch(err => {
            console.error(`Could not add plan docs file to vector store: ${err.message}`)
        })

    const obj:any = {
        vectorStoreId,
        file: docsFile
    }
    if (returnReadable) obj.readable = readable
    return obj

}


type SaveChatOptions = {
    chatId?: string,
    subjectId: string,
    chat_session?: string,
    chat: { question: string, answer: string, annotations: any }
}
export const saveChatHistory = ({chatId, subjectId, chat_session, chat}: SaveChatOptions) => {
    return async (context: HookContext) => {
        if (context.params.login?.owner) {
            if (!chatId) {
                const chats = await new CoreCall('ai-chats', context).find({
                    query: {
                        $limit: 1,
                        chatId: `${context.params.login.owner}|${subjectId}|shop_chat`
                    }
                })
                if (!chats.data?.length) {
                    const created = await new CoreCall('ai-chats', context).create({
                        person: context.params.login.owner,
                        subject: context.result._id,
                        chatName: 'shop_chat'
                    }, {admin_pass: true, skip_hooks: true})
                        .catch(err => console.log(`Could not create ai chat history: ${context.params.login.owner} - ${err.message}`))
                    chatId = created?._id;
                } else chatId = chats.data[0]._id
            }
            if (chatId) await new CoreCall('ai-chats', context).patch(chatId, {
                $push: {
                    chats: {
                        $position: 0,
                        $slice: 50,
                        $each: [{
                            session: chat_session || new Date().getTime().toString(),
                            createdAt: new Date(),
                            chatName: 'shop_chat',
                            subject: context.result._id,
                            person: context.params.login.owner,
                            ...chat
                        }]
                    }
                }
            }, {admin_pass: true, skip_hooks: true})
                .catch(err => console.log(`Could not update plan doc ai chat history: ${context.params.login.owner} - ${err.message}`))

        }
        return context;
    }
}
