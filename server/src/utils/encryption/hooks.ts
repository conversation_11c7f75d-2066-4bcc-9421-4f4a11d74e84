import {HookContext} from '../../declarations.js';
import {symmetricEncrypt} from './symmetric.js';
import {_set, _get} from 'symbol-ucan';
import {AnyObj} from 'feathers-ucan';
import { toString } from 'uint8arrays';

export const encryptFields = (fields: Array<string>) => {
    return (context: HookContext): HookContext => {
        const {field_key} = (context.app.get('encryption') || {field_key: ''}) as any;
        const {encrypt} = symmetricEncrypt(field_key);
        const tryOne = (v:any) => {
            if (typeof v !== 'string') v = JSON.stringify(v);
            let encrypted = v;
            try {
                encrypted = toString(encrypt(v as string), 'hex')
            } catch (e) {
                encrypted = undefined;
            }
            return encrypted;
        }
        const prepTry = (v:any) => {
            if(Array.isArray(v)) return v.map(a => tryOne(a));
            else return tryOne(v);
        }
        for (const field of fields) {
            // check for field in data and encrypt it if found
            let val = _get(context.data, field);
            if (val) context.data[field] = prepTry(val);
            else {
                // else check for $set at field
                val = _get(context.data, `$set.${field}`);
                if(val) context.data.$set[field] = prepTry(val);
                else {
                    // else check for $addToSet
                    val = _get(context.data, `$addToSet.${field}`)
                    if(val) {
                        if((val as any)['$each']) context.data.$addToSet[field].$each = prepTry((val as any)['$each']);
                        else context.data.$addToSet[field] = prepTry(val);
                    }
                }
            }
        }
        return context;
    }
}

export const fieldDecrypt = (v:any, context:HookContext) => {
    if(!v) return v;
    const {field_key} = (context.app.get('encryption') || {field_key: ''}) as any;
    const {decrypt} = symmetricEncrypt(field_key);
    let decrypted: any = v;
    try {
        decrypted = decrypt(v, true);
    } catch (e) {
        if (!decrypted) decrypted = v;
    }
    return decrypted;
}

export const decryptFields = (fields: Array<string>) => {
    return (context: HookContext): HookContext => {

        for (const field of fields) {
            const v = _get(context.result, field) as string;
            if (v) {
                const decrypted = fieldDecrypt(v, context);
                context.result = _set(context.result, field, decrypted);
            }
        }
        return context;
    }
}
export const decryptMultiFields = (fields: Array<string>) => {
    return (context: HookContext): HookContext => {
        const {field_key} = (context.app.get('encryption') || {field_key: ''}) as any;
        const {decrypt} = symmetricEncrypt(field_key);
        const decryptOne = (data: AnyObj) => {
            for (const field of fields) {
                const v = _get(data, field) as string;
                if (v) {
                    let decrypted: any = v;
                    try {
                        decrypted = decrypt(v, true);
                    } catch (e) {
                        if (!decrypted) decrypted = v;
                    } finally {
                        data = _set(data, field, decrypted);
                    }
                }
            }
            return data;
        }
        context.result.data = context.result.data.map((a:any) => decryptOne(a))
        return context;
    }
}

export const encryptedFields = (fields: Array<string>) => {
    return (context: HookContext): HookContext => {
        const defFn = (ctx:any) => ctx;
        const map = {
            'before': {
                'create': encryptFields(fields),
                'patch': encryptFields(fields),
                'update': encryptFields(fields),
                //TODO: add query resolvers for encrypted field search
                'get': defFn,
                'find': defFn,
                'remove': defFn
            },
            'after': {
                'create': decryptFields(fields),
                'patch': decryptFields(fields),
                'update': decryptFields(fields),
                'get': decryptFields(fields),
                'find': decryptMultiFields(fields),
                'remove': defFn
            },
            'error': {
                'create': defFn,
                'update': defFn,
                'patch': defFn,
                'get': defFn,
                'find': defFn,
                'remove': defFn
            }
        }
        return (map as any)[context.type as any][context.method as any](context);
    }
}

export const deepEncrypt = (mainKey:string, keys:Array<string>) => {
    return (context: HookContext): HookContext => {
        const {$set} = context.data;
        const mk = context.data[mainKey];

        const runEncrypt = (val:any, encrypt:any) => {
            if(!val) return val;
            if(typeof val === 'string') return toString(encrypt(val), 'hex');
            else return toString(encrypt(JSON.stringify(val)), 'hex');
        }

        if (mk) {
            const {field_key} = context.app.get('encryption');
            const {encrypt} = symmetricEncrypt(field_key);

            for (const id in mk) {
                for (const k in mk[id] || {}) {
                    if (keys.includes(k)) context.data.members[id][k] = runEncrypt(mk[id][k], encrypt)
                }
            }
        }
        if ($set) {
            const {field_key} = context.app.get('encryption');
            const {encrypt} = symmetricEncrypt(field_key);

            for (const k in $set) {
                if (k.includes(mainKey)) {
                    const spl = k.split('.');
                    if (spl[1]) {
                        if (spl[2] && keys.includes(spl[2])) context.data.$set[k] = runEncrypt($set[k], encrypt)
                    } else {
                        for (const p in $set[k] || {}) {
                            if (keys.includes(p)) context.data.$set[k][p] = runEncrypt($set[k][p], encrypt)
                        }
                    }
                }
            }
        }
        return context;
    }
}

export const deepDecrypt = (mainKey:string, keys:Array<string>) => {
    return (context:HookContext):HookContext => {
        if ((context.result || {})[mainKey]) {
            const {field_key} = (context.app.get('encryption') || {field_key: ''}) as any;
            const {decrypt} = symmetricEncrypt(field_key);
            for (const id in context.result[mainKey]) {
                for (const k in context.result[mainKey][id] || {}) {
                    if (keys.includes(k)) {
                        const v = context.result[mainKey][id][k]
                        if (v) context.result[mainKey][id][k] = decrypt(v, true);
                    }
                }
            }
        }
        return context;
    }
}

export const handleDeepEncrypt = (mainKey:string, keys:Array<string>) => {
    return (context:HookContext):HookContext => {
        const encrypt = deepEncrypt(mainKey, keys);
        const decrypt = deepDecrypt(mainKey, keys);
        const def = (ctx:any) => ctx;
        const defObj = {
            get: def,
            find: def,
            create: def,
            patch: def,
            remove: def
        }
        const action = {
            'before': {
                ...defObj,
                create: encrypt,
                patch: encrypt,
                update: encrypt,
            },
            'after': {
                ...defObj,
                create: decrypt,
                patch: decrypt,
                update: decrypt
            },
            'error': {
                ...defObj
            }
        }
        return (action as any)[context.type as any][context.method as any](context);
    }
}
