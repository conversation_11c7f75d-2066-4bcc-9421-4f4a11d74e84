import {_get, _set, _isequal,stringifyPath} from './dash-utils.js';
import { dataContains } from './common/checks.js';
import {HookContext} from '../declarations.js';
import {AnyObj, CoreCall, NullableId, setExists} from 'feathers-ucan';
import { loadExists } from 'feathers-ucan';

export const logChange = (limit?:number):(c:HookContext) => HookContext => {
    return (context:HookContext):HookContext =>
    {
        if(context.params.skip_hooks) return context;
        if (context.type === 'before' && ['create', 'patch', 'update', 'delete'].includes(context.method)) {
            // console.log('here!!', context.method);
            const paths = context.method === 'create' ? ['createdBy', 'updatedBy'] : ['patch', 'update', 'remove'].includes(context.method) ? ['updatedBy'] : [];

            const addChange = (path: 'createdBy' | 'updatedBy'): void => {
                context.data[`${path.split('By')[0]}At`] = new Date();
                if (!context.data[path]) context.data[path] = {
                    login: _get(context.params, 'login._id', undefined) || undefined,
                    fingerprint: _get(context.params, 'core.fp', undefined) || undefined,
                    origin: _get(context.params, 'originalOrigin', undefined) || undefined,
                    longtail: _get(context.params, 'core.ltail', undefined) || undefined
                };
                else if (typeof context.data[path] === 'string') context.data[path] = JSON.parse(context.data[path])
            };

            const changeMethods = {
                createdBy: () => addChange('createdBy'),
                updatedBy: () => {
                    addChange('updatedBy')
                    if(context.method !== 'create') {
                        if(context.data.updatedByHistory) delete context.data.updatedByHistory;
                        context.data.$push = { updatedByHistory: { $each: [{...context.data.updatedBy, updatedAt: context.data.updatedAt }], $position: 0, $slice: 200 }, ...context.data.$push }
                    }
                }
            };
            paths.forEach(a => changeMethods[a as ('createdBy' | 'updatedBy')]());
        }
        return context;
    }
};

// ****changeLog property
//[key:string]: {
//   history: [{
//         data: oldVal,
//         updatedAt: Date,
//         updatedBy: {
//                 login: id
//             }
//   }]
// }

export const logHistory = (fields: Array<string | Array<string>>, existing?:AnyObj): (c: HookContext) => Promise<HookContext> => {
    return async (context: HookContext): Promise<HookContext> => {
        try {
            let {loopProtect} = context.params.core;
            if (context.id && !_get(loopProtect, String(context.id))) {
                context.params.core = _set(context.params.core, ['loopProtect', String(context.id)], true);
                if (!existing) existing = await loadExists(context);
                context = setExists(context, existing);
                let changeLog: any = {};
                let changed = false;
                if (existing?.changeLog) changeLog = await new CoreCall('change-logs', context, {skipJoins: true}).get(existing?.changeLog)
                    .catch(err => {
                        console.error(`Error getting change log for ${context.path} ${context.id}: ${err.message}`);
                        return {}
                    })
                else changed = true;
                let logPatch: any = {service: context.path, recordId: context.id};
                const getPath = (path: string | Array<string>): Array<string> => {
                    if (Array.isArray(path)) return path;
                    else return [path];
                }
                const pathExists = (path: string | Array<string>) => {
                    const p = Array.isArray(path) ? path : [path]
                    return dataContains(p.map(a => [a]))(context);
                };
                const archiveField = async (field: string | Array<string>) => {
                    const path = getPath(field);
                    const oldVal = _get(existing, path);
                    const newVal = _get(context.data, path, undefined) as any;
                    const logPath = stringifyPath(field);
                    if (!_isequal(oldVal, newVal)) {
                        changed = true;
                        const historyObj = {
                            data: oldVal,
                            updatedAt: new Date().toString(),
                            updatedBy: {login: context.params.login?._id}
                        }
                        if ((_get(changeLog, [logPath, 'history'], []) || []).length) {
                            logPatch = _set(logPatch, [logPath, 'history'], [...changeLog[logPath].history, historyObj])
                        } else {
                            logPatch[logPath as any] = {
                                history: [historyObj]
                            }
                        }
                        context.data = _set(context.data, ['editMap', ...path], new Date().toString())
                    }
                }
                fields.filter(a => pathExists(getPath(a))).map(a => archiveField(a));
                if (changed) {
                    if (changeLog._id) {
                        changeLog = await new CoreCall('change-logs', context, {skipJoins: true}).patch(changeLog._id, logPatch);
                    } else changeLog = await new CoreCall('change-logs', context, {skipJoins: true}).create(logPatch);
                }
                if (changeLog._id) context.data.changeLog = changeLog._id;
            }
        } catch (err:any) {
            console.error(`Error logging change: ${err.message}`)
        }
        return context;
    }
}

export const excludeFields = (context:HookContext) => {
    if(context.params.runJoin?.updated_by_history) return context;
    context.params.mongodb = { ...context.params.mongodb, projection: { updatedByHistory: 0, ...context.params.runJoin?.projection } }
    return context;
}



