import {HookContext} from '../../declarations.js';
import {CoreCall, NullableId} from 'feathers-ucan';

export declare type VoteConfig = {
    up: string,
    down: string,
    userUp:string,
    userDown:string,
    count: string
}
const defaultSettings:VoteConfig = {
    up: 'upVotes',
    down: 'downVotes',
    userUp: 'userUpVotes',
    userDown: 'userDownVotes',
    count: 'voteCount'
}

const counter = <T>(record:T, config:VoteConfig):number => {
    return ((record as any)[config.up as any] as Array<string> || []).length - ((record as any)[config.down as any] as Array<string> || []).length
}

const countVotes = <T = any>(record:T, config: VoteConfig, context:HookContext):T => {
    const voteCount = counter(record, config)

    if(context.type === 'before') context.data.voteCount = voteCount;
    else {
        const loginId = context.params.login?._id;
        (record as any)[config.count as any] = voteCount;
        if (((record as any)[config.up as any] as Array<string> || []).includes(loginId)) (record as any)[config.userUp as any] = true
        else if (((record as any)[config.down as any] as Array<string> || []).includes(loginId)) (record as any)[config.userDown as any] = true
    }
    return record;
}

export const patchVotes = (settings:Partial<VoteConfig>):(context:HookContext) => HookContext => {
    return (context:HookContext):HookContext => {

        const config = { ...defaultSettings, ...settings };
        if(context.type === 'before') {
            const loginId = context.params.login?._id;
            const upVote = (active?: boolean) => {
                if (active) {
                    if (context.data.$addToSet) context.data.$addToSet[config.up] = loginId;
                    else context.data['$addToSet'] = {[config.up]: loginId}
                }
                if (context.data.$pull) context.data.$pull[config.down] = loginId;
                else context.data.$pull = {[config.down]: loginId};
            }
            const downVote = (active?: boolean) => {
                if (active) {
                    if (context.data.$addToSet) context.data.$addToSet[config.down] = loginId;
                    else context.data['$addToSet'] = {[config.down]: loginId}
                }
                if (context.data.$pull) context.data.$pull[config.up] = loginId;
                else context.data.$pull = {[config.up]: loginId};
            }
            //Add to set if user upvoted
            const upVoted = context.data[config.userUp];
            if (upVoted) {
                upVote(true);
            } else if (upVoted === false) {
                //pull if user removed upvote explicitly
                downVote()
            }
            const downVoted = context.data[config.userDown];
            //Add to set if user downvoted
            if (downVoted) {
                downVote(true)
            } else if (downVoted === false) {
                //pull if user removed downvote explicitly
                upVote()
            }
            Object.keys(config).forEach(key => {
                delete context.data[(config as any)[key as any] as any];
            })
        } else if(context.type === 'after') {
            const c = counter(context.result, config);
            context.result.voteCount = c
            new CoreCall(context.path, context, { skipJoins: true })._patch(context.id as NullableId, { voteCount: c }, { skip_hooks: true, admin_pass: true})
        }
        return context;
    }
}

export const handleVotes = (settings?: Partial<VoteConfig>):(c:HookContext) => HookContext => {
    return (context:HookContext):HookContext => {
        const config = { ...defaultSettings, ...settings };

        if(context.method === 'patch') return patchVotes(config)(context);
        //HAVE TO PASS PARAMS FOR VOTE
        else return context;
    }
}
