import { getClientIp, getClientIpFromXForwardedFor, addSessionFp } from './ip-utils.js';
import {HookContext} from '../declarations.js';
import {ObjectId} from 'mongodb';

export { getClientIp, getClientIpFromXForwardedFor, addSessionFp };
export * from './file-join.js';
export * from './change-log.js';
export * from './sanitize/index.js';
export * from './fast-join.js';
export * from './dash-utils.js';
export * from './relate/index.js';
export * from './schemas/index.js';
export * from './common/checks.js';
export * from './encryption/index.js'
export * from './core/methods.js';
export * from './types.js';
export * from './geo/index.js'
export * from './notifications/index.js';
export * from './common/states.js'
export * from './ai/index.js'

export const fakeId = '123456781234567812345678'
export const dollarString = (val: string | number, symbol = '$', dec?: number, def = 'N/A'): string => {
    const v = typeof val !== 'number' ? parseFloat(val) : val;
    if (isNaN(v)) return def;
    // console.log('dollar string', val, v)
    const decimal = dec || dec === 0 ? dec : 2;
    const valDec = v.toFixed(decimal);
    return (symbol) + valDec.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

export const maybeObjectId = (val:any) => {
    if(typeof val === 'string') return ObjectId.createFromHexString(val);
    return val;
}
export const idConvert = (path:string) => {
    return async (context:HookContext) => {
        const v = (context.params.query || {})[path]
        if(v){
            if(typeof v === 'string') {
                context.params.query[path] = ObjectId.createFromHexString(v)
                return context;
            }
            if(v.$in) {
                context.params.query[path].$in = v.$in.map((a:any) => maybeObjectId(a))
            }
            if(v.$nin) {
                context.params.query[path].$nin = v.$nin.map((a:any) => maybeObjectId(a))
            }
            if(v.$eq){
                context.params.query[path] = maybeObjectId(v.$eq);
            }
            if(v.$ne){
                context.params.query[path] = { $ne: maybeObjectId(v.$ne) };
            }
        }
        return context;
    }
}

export const errHook = (noThrow?:boolean) => {
    return (context:HookContext) => {
        console.log(`Error on ${context.path} - ${context.method}: ${context.error.message}`);
        if(noThrow) return context;
        else throw new Error(context.error.data[0].message)
    }
}
