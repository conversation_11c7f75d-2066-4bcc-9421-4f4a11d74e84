import {HookContext} from '../../../declarations.js';

import puppeteer from 'puppeteer';
import fs from 'fs';
import {fakeId} from '../../../utils/index.js';

export const generatePdf = async (context: HookContext) => {
    const file = context.params.file
    try {
        const html = file.buffer.toString('utf8');

        const browser = await puppeteer.launch();
        const page = await browser.newPage();
        await page.setContent(html, {waitUntil: 'networkidle0'});
        const pdfBuffer: any = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {top: '40px', bottom: '40px', left: '40px', right: '40px'}
        });

        await browser.close();
        context.result = {_id: fakeId, buffer: pdfBuffer};
    } catch (err) {
        console.error('PDF generation failed:', err);
        throw new Error('PDF generation failed');

    }
    return context;
}
