// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import {hooks as schemaHooks} from '@feathersjs/schema'
// import uploadMiddleware from '../../middleware/upload/index.js';
import {handleUpload, handleDelete, handleGet, handleFind} from './hooks/index.js';

import {
    uploadsDataValidator,
    uploadsPatchValidator,
    uploadsQueryValidator,
    uploadsResolver,
    uploadsExternalResolver,
    uploadsDataResolver,
    uploadsPatchResolver,
    uploadsQueryResolver
} from './uploads.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {UploadsService, getOptions} from './uploads.class.js'
import {uploadsPath, uploadsMethods} from './uploads.shared.js'

export * from './uploads.class.js'
export * from './uploads.schema.js'
import {
    allUcanAuth,
    anyAuth,
    CapabilityParts,
    CoreCall,
    NullableId,
    loadExists, setExists, AnyObj, noThrow
} from 'feathers-ucan';
import {logChange} from '../../utils/index.js';
import {ObjectId} from 'mongodb';
import multer from 'multer';

const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
    await new Promise((resolve) => multipartMiddleware.single('file')(req, res, resolve));
    req.feathers.file = req.file;
    return next();
}

const removeUrl = (context: HookContext) => {
    if (context.data.url) delete context.data.url;
    return context;
};

const expiresAsNumber = (context: HookContext): HookContext => {
    if (context.data.expires && typeof context.data.expires !== 'number') context.data.expires = Number(context.data.expires);
    return context;
};


/*
pass a patch data object that includes
    fileId?
    verifyUsage: an array of the usages you want to check
    deleteUnused: boolean,
    usage?: just list of usage
 */
const verifyUsage = async (context: HookContext): Promise<HookContext> => {
    if (Object.keys(context.data).includes('verifyUsage')) {

        //make sure we have a fileId
        let {fileId, usage} = context.data;
        if (!fileId || !usage) {
            const existing = await loadExists(context);
            context = setExists(context, existing);
            fileId = existing.fileId;
            usage = existing.usage;
        }

        const removeRecord = async () => {
            if (context.data.deleteUnused && !usage?.length) {
                context.result = await new CoreCall('uploads', context).remove(context.id as NullableId, {admin_pass: true})
            }
            return context;
        }

        const isInUse = (image: AnyObj): boolean => {
            if (typeof image !== 'object') return false;
            const {fileId: ifd} = image || {fileId: '***'}
            if (ifd) return fileId === ifd;
            else {
                let response = false;
                for (const field in image) {
                    if (response) break;
                    response = isInUse(image[field])
                }
                return response
            }
        }
        const pull: any[] = [];
        for await (const config of context.data.verifyUsage) {
            const subject = await new CoreCall(config.subjectModel, context, {skipJoins: true}).get(config.subject);
            let inUse = true;
            if (!subject) inUse = false;
            if (Array.isArray(subject[config.subjectPath])) {
                const useList = subject[config.subjectPath].map(a => isInUse(a));
                if (!useList.filter(a => !!a).length) inUse = false;
            } else inUse = isInUse(subject[config.subjectPath]);
            if (!inUse) {
                pull.push(ObjectId.createFromHexString(config.subject));
            }
        }
        if (pull.length) {
            context.result = await new CoreCall('uploads', context,).patch(context.id as NullableId, {
                $pull: {usage: {subject: {$in: pull}}},
                usageVerified: new Date().toString()
            }, {admin_pass: true});
            usage = context.result.usage;

        }
        //if no remaining usages, remove entire record
        await removeRecord();
        context.data = {usageVerified: new Date().toString()};
    }
    return context;
}
const editor = [['uploads', 'WRITE']] as Array<CapabilityParts>;
const deleter = [['uploads', '*']] as Array<CapabilityParts>;

const authenticate = async (context: HookContext) => {
    const ucanArgs = {
        create: anyAuth,
        patch: editor,
        update: editor,
        remove: deleter,
        find: anyAuth
    };
    if(context.params.allow_upload || ['parse', 'print'].includes(context.data?.storage)) ucanArgs.create = noThrow as any
    return await allUcanAuth<HookContext>(ucanArgs, {
        loginPass: [[['createdBy.login'], '*']],
        or: ['patch', 'create', 'delete'],
        adminPass: ['patch', 'create', 'remove']
    })(context) as any;
}

const passCore = (context: HookContext): HookContext => {
    if (context.params.query?.core && !context.params.core) {
        context.params.core = context.params.query.core;
    }
    return context;
};
// A configure function that registers the service and its hooks via `app.configure`
export const uploads = (app: Application) => {

    // Register our service on the Feathers application
    app.use(uploadsPath,
        restMiddleware,
        new UploadsService(getOptions(app)),
        {
            // A list of all methods this service exposes externally
            methods: uploadsMethods,
            // You can add additional custom events to be sent to clients here
            events: []
        }
    )
    // Initialize hooks
    app.service(uploadsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(uploadsExternalResolver),
                schemaHooks.resolveResult(uploadsResolver)
            ]
        },
        before: {
            all: [
                passCore,
                authenticate,
                logChange(),
                schemaHooks.validateQuery(uploadsQueryValidator),
                schemaHooks.resolveQuery(uploadsQueryResolver)
            ],
            find: [],
            create: [
                expiresAsNumber,
                handleUpload,
                schemaHooks.validateData(uploadsDataValidator),
                schemaHooks.resolveData(uploadsDataResolver),
            ],
            patch: [
                removeUrl,
                verifyUsage,
                handleUpload,
                handleDelete,
                schemaHooks.validateData(uploadsPatchValidator),
                schemaHooks.resolveData(uploadsPatchResolver)
            ],
            update: [
                removeUrl,
                schemaHooks.validateData(uploadsPatchValidator),
                schemaHooks.resolveData(uploadsPatchResolver),
                handleUpload,
                handleDelete
            ],
            remove: [handleDelete]
        },
        after: {
            all: [],
            create: [handleGet],
            patch: [handleGet],
            find: [
                handleFind
            ],
            get: [handleGet]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [uploadsPath]: any;
    }
}
