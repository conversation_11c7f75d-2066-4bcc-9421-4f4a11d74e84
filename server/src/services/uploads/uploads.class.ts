// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type {
  Uploads,
  UploadsData,
  UploadsPatch,
  UploadsQuery
} from './uploads.schema.js'

export type { Uploads, UploadsData, UploadsPatch, UploadsQuery }

export interface UploadsParams extends MongoDBAdapterParams<UploadsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class UploadsService<ServiceParams extends Params = UploadsParams> extends MongoDBService<
  Uploads,
  UploadsData,
  UploadsParams,
  UploadsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('uploads')),
    operators: ['$regex', '$text', '$search', '$options']
  }
}
