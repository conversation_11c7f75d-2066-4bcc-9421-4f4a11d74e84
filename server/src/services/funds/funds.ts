// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    fundsDataValidator,
    fundsPatchValidator,
    fundsQueryValidator,
    fundsResolver,
    fundsExternalResolver,
    fundsDataResolver,
    fundsPatchResolver,
    fundsQueryResolver
} from './funds.schema.js'

import type {Application} from '../../declarations.js'
import {FundsService, getOptions} from './funds.class.js'
import {fundsPath, fundsMethods} from './funds.shared.js'

export * from './funds.class.js'
export * from './funds.schema.js'

import {getSim} from './utils/sim-bills.js';
// A configure function that registers the service and its hooks via `app.configure`
export const funds = (app: Application) => {
    // Register our service on the Feathers application
    app.use(fundsPath, new FundsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: fundsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(fundsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(fundsExternalResolver),
                schemaHooks.resolveResult(fundsResolver)
            ]
        },
        before: {
            all: [
                schemaHooks.validateQuery(fundsQueryValidator),
                schemaHooks.resolveQuery(fundsQueryResolver)
            ],
            find: [],
            get: [
                getSim
            ],
            create: [
                schemaHooks.validateData(fundsDataValidator),
                schemaHooks.resolveData(fundsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(fundsPatchValidator),
                schemaHooks.resolveData(fundsPatchResolver)
            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [fundsPath]: FundsService
    }
}
