// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Funds, FundsData, FundsPatch, FundsQuery } from './funds.schema.js'

export type { Funds, FundsData, FundsPatch, FundsQuery }

export interface FundsParams extends MongoDBAdapterParams<FundsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class FundsService<ServiceParams extends Params = FundsParams> extends MongoDBService<
  Funds,
  FundsData,
  FundsParams,
  FundsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('funds'))
  }
}
