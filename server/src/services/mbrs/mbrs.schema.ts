// TypeBox schema for mbrs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, PhoneSchema } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const mbrsSchema = Type.Object({
  _id: ObjectIdSchema(),
  person: ObjectIdSchema(),
  firstName: Type.String(),
  lastName: Type.String(),
  middleName: Type.Optional(Type.String()),
  suffix: Type.Optional(Type.String()),
  prefix: Type.Optional(Type.String()),
  nickname: Type.Optional(Type.String()),
  dateOfBirth: Type.Optional(Type.Any()),
  gender: Type.Optional(Type.String()),
  ssn: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(PhoneSchema),
  address: Type.Optional(ServiceAddressSchema),
  emergencyContact: Type.Optional(Type.Object({
    name: Type.Optional(Type.String()),
    relationship: Type.Optional(Type.String()),
    phone: Type.Optional(PhoneSchema),
    email: Type.Optional(Type.String())
  ,
  // Missing fields from old schema
  coverage: Type.Optional(ObjectIdSchema()),
  enrollment: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  pm: Type.Optional(Type.String()),
  inactive: Type.Optional(Type.Boolean()),
}, { additionalProperties: false })),
  household: Type.Optional(ObjectIdSchema()),
  relationship: Type.Optional(Type.String()),
  dependent: Type.Optional(Type.Boolean()),
  student: Type.Optional(Type.Boolean()),
  disabled: Type.Optional(Type.Boolean()),
  tobacco: Type.Optional(Type.Boolean()),
  pregnant: Type.Optional(Type.Boolean()),
  enrollments: Type.Optional(Type.Array(ObjectIdSchema())),
  claims: Type.Optional(Type.Array(ObjectIdSchema())),
  visits: Type.Optional(Type.Array(ObjectIdSchema())),
  medications: Type.Optional(Type.Array(ObjectIdSchema())),
  conditions: Type.Optional(Type.Array(ObjectIdSchema())),
  allergies: Type.Optional(Type.Array(Type.String())),
  notes: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type Mbrs = Static<typeof mbrsSchema>
export const mbrsValidator = getValidator(mbrsSchema, dataValidator)
export const mbrsResolver = resolve<Mbrs, HookContext>({})
export const mbrsExternalResolver = resolve<Mbrs, HookContext>({})

// Schema for creating new data
export const mbrsDataSchema = Type.Object({
  ...Type.Omit(mbrsSchema, ['_id']).properties
}, { additionalProperties: false })

export type MbrsData = Static<typeof mbrsDataSchema>
export const mbrsDataValidator = getValidator(mbrsDataSchema, dataValidator)
export const mbrsDataResolver = resolve<MbrsData, HookContext>({})

// Schema for updating existing data
export const mbrsPatchSchema = Type.Object({
  person: Type.Optional(ObjectIdSchema()),
  firstName: Type.Optional(Type.String()),
  lastName: Type.Optional(Type.String()),
  middleName: Type.Optional(Type.String()),
  suffix: Type.Optional(Type.String()),
  prefix: Type.Optional(Type.String()),
  nickname: Type.Optional(Type.String()),
  dateOfBirth: Type.Optional(Type.Any()),
  gender: Type.Optional(Type.String()),
  ssn: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(PhoneSchema),
  address: Type.Optional(ServiceAddressSchema),
  emergencyContact: Type.Optional(Type.Object({
    name: Type.Optional(Type.String()),
    relationship: Type.Optional(Type.String()),
    phone: Type.Optional(PhoneSchema),
    email: Type.Optional(Type.String())
  }, { additionalProperties: false })),
  household: Type.Optional(ObjectIdSchema()),
  relationship: Type.Optional(Type.String()),
  dependent: Type.Optional(Type.Boolean()),
  student: Type.Optional(Type.Boolean()),
  disabled: Type.Optional(Type.Boolean()),
  tobacco: Type.Optional(Type.Boolean()),
  pregnant: Type.Optional(Type.Boolean()),
  enrollments: Type.Optional(Type.Array(ObjectIdSchema())),
  claims: Type.Optional(Type.Array(ObjectIdSchema())),
  visits: Type.Optional(Type.Array(ObjectIdSchema())),
  medications: Type.Optional(Type.Array(ObjectIdSchema())),
  conditions: Type.Optional(Type.Array(ObjectIdSchema())),
  allergies: Type.Optional(Type.Array(Type.String())),
  notes: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $push: Type.Optional(Type.Object({
    enrollments: Type.Optional(ObjectIdSchema()),
    claims: Type.Optional(ObjectIdSchema()),
    visits: Type.Optional(ObjectIdSchema()),
    medications: Type.Optional(ObjectIdSchema()),
    conditions: Type.Optional(ObjectIdSchema()),
    allergies: Type.Optional(Type.String())
  }, { additionalProperties: false })),
  $pull: Type.Optional(Type.Object({
    enrollments: Type.Optional(ObjectIdSchema()),
    claims: Type.Optional(ObjectIdSchema()),
    visits: Type.Optional(ObjectIdSchema()),
    medications: Type.Optional(ObjectIdSchema()),
    conditions: Type.Optional(ObjectIdSchema()),
    allergies: Type.Optional(Type.String())
  }, { additionalProperties: false }))
}, { additionalProperties: false })

export type MbrsPatch = Static<typeof mbrsPatchSchema>
export const mbrsPatchValidator = getValidator(mbrsPatchSchema, dataValidator)
export const mbrsPatchResolver = resolve<MbrsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const mbrsQueryProperties = mbrsSchema

export const mbrsQuerySchema = querySyntax(mbrsQueryProperties)

export type MbrsQuery = Static<typeof mbrsQuerySchema>
export const mbrsQueryValidator = getValidator(mbrsQuerySchema, queryValidator)
export const mbrsQueryResolver = resolve<MbrsQuery, HookContext>({})
