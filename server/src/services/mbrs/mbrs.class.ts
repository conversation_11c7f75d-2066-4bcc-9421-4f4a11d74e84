// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {Mbrs, MbrsData, MbrsPatch, MbrsQuery} from './mbrs.schema.js'

export type {Mbrs, MbrsData, MbrsPatch, MbrsQuery}

export interface MbrsParams extends MongoDBAdapterParams<MbrsQuery> {
}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class MbrsService<ServiceParams extends Params = MbrsParams> extends MongoDBService<
    Mbrs,
    MbrsData,
    MbrsParams,
    MbrsPatch
> {
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        Model: app.get('mongodbClient').then((db) => db.collection('mbrs'))
            .then((collection) => {
                collection.createIndex({itemId: 1}, {unique: true});
                return collection
            })
    }
}
