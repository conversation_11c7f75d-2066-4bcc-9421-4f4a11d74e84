// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    mbrsDataValidator,
    mbrsPatchValidator,
    mbrsQueryValidator,
    mbrsResolver,
    mbrsExternalResolver,
    mbrsDataResolver,
    mbrsPatchResolver,
    mbrsQueryResolver
} from './mbrs.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {MbrsService, getOptions} from './mbrs.class.js'
import {mbrsPath, mbrsMethods} from './mbrs.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall} from 'feathers-ucan';

export * from './mbrs.class.js'
export * from './mbrs.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['mbrs', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['mbrs', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };

    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['get', 'find', 'patch', 'create', 'remove'],
        loginPass: [[['person/owner'], '*']],
        or: '*'
    })(context) as any;
}

const setItemId = (context: HookContext) => {
    context.data.itemId = `${context.data.coverage}:${context.data.person}`
    return context;
}

const addProvider = async (context: HookContext) => {
    if(!context.data.provider){
        const cvg = await new CoreCall('coverages', context).get(context.data.coverage)
        if(cvg.provider) context.data.provider = cvg.provider
    }
    return context;
}


// A configure function that registers the service and its hooks via `app.configure`
export const mbrs = (app: Application) => {
    // Register our service on the Feathers application
    app.use(mbrsPath, new MbrsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: mbrsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(mbrsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(mbrsExternalResolver),
                schemaHooks.resolveResult(mbrsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                schemaHooks.validateQuery(mbrsQueryValidator),
                schemaHooks.resolveQuery(mbrsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                setItemId,
                addProvider,
                schemaHooks.validateData(mbrsDataValidator),
                schemaHooks.resolveData(mbrsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(mbrsPatchValidator),
                schemaHooks.resolveData(mbrsPatchResolver)
            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [mbrsPath]: MbrsService
    }
}
