// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Ims, ImsData, ImsPatch, ImsQuery } from './ims.schema.js'

export type { Ims, ImsData, ImsPatch, ImsQuery }

export interface ImsParams extends MongoDBAdapterParams<ImsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ImsService<ServiceParams extends Params = ImsParams> extends MongoDBService<
  Ims,
  ImsData,
  ImsParams,
  ImsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('ims')),
    operators: ['$regex', '$options']
  }
}
