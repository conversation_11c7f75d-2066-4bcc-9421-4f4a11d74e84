// TypeBox schema for flow-charts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const flowChartsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type FlowCharts = Static<typeof flowChartsSchema>
export const flowChartsValidator = getValidator(flowChartsSchema, dataValidator)
export const flowChartsResolver = resolve<FlowCharts, HookContext>({})
export const flowChartsExternalResolver = resolve<FlowCharts, HookContext>({})

export const flowChartsDataSchema = Type.Object({
  ...Type.Omit(flowChartsSchema, ['_id']).properties
}, { additionalProperties: false })

export type FlowChartsData = Static<typeof flowChartsDataSchema>
export const flowChartsDataValidator = getValidator(flowChartsDataSchema, dataValidator)
export const flowChartsDataResolver = resolve<FlowChartsData, HookContext>({})

export const flowChartsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(flowChartsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type FlowChartsPatch = Static<typeof flowChartsPatchSchema>
export const flowChartsPatchValidator = getValidator(flowChartsPatchSchema, dataValidator)
export const flowChartsPatchResolver = resolve<FlowChartsPatch, HookContext>({})

// Allow querying on any field from the main schema
const flowChartsQueryProperties = flowChartsSchema
export const flowChartsQuerySchema = querySyntax(flowChartsQueryProperties)
export type FlowChartsQuery = Static<typeof flowChartsQuerySchema>
export const flowChartsQueryValidator = getValidator(flowChartsQuerySchema, queryValidator)
export const flowChartsQueryResolver = resolve<FlowChartsQuery, HookContext>({})
