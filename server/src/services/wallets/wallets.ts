// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    walletsDataValidator,
    walletsPatchValidator,
    walletsQueryValidator,
    walletsResolver,
    walletsExternalResolver,
    walletsDataResolver,
    walletsPatchResolver,
    walletsQueryResolver
} from './wallets.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {WalletsService, getOptions} from './wallets.class.js'
import {walletsPath, walletsMethods} from './wallets.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts} from 'feathers-ucan';
import {logChange, relate} from '../../utils/index.js';

export * from './wallets.class.js'
export * from './wallets.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['wallets', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['wallets', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };

    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['get', 'find', 'patch', 'create', 'remove'],
        loginPass: [[['person/owner'], '*']]
    })(context) as any;
}

const relatePerson = async (context: HookContext) => {
    return relate('oto', {herePath: 'person', therePath: 'wallet', thereService: 'ppls', paramsName: 'wallet_person'})(context)
}

// A configure function that registers the service and its hooks via `app.configure`
export const wallets = (app: Application) => {
    // Register our service on the Feathers application
    app.use(walletsPath, new WalletsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: walletsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(walletsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(walletsExternalResolver),
                schemaHooks.resolveResult(walletsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(walletsQueryValidator),
                schemaHooks.resolveQuery(walletsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(walletsDataValidator),
                schemaHooks.resolveData(walletsDataResolver),
                relatePerson
            ],
            patch: [
                schemaHooks.validateData(walletsPatchValidator),
                schemaHooks.resolveData(walletsPatchResolver),
                relatePerson
            ],
            remove: [relatePerson]
        },
        after: {
            all: [],
            create: [relatePerson],
            patch: [relatePerson],
            remove: [relatePerson],
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [walletsPath]: WalletsService
    }
}
