// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Wallets, WalletsData, WalletsPatch, WalletsQuery } from './wallets.schema.js'

export type { Wallets, WalletsData, WalletsPatch, WalletsQuery }

export interface WalletsParams extends MongoDBAdapterParams<WalletsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class WalletsService<ServiceParams extends Params = WalletsParams> extends MongoDBService<
  Wallets,
  WalletsData,
  WalletsParams,
  WalletsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('wallets'))
  }
}
