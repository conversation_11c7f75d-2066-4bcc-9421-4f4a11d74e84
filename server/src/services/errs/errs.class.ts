// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Errs, ErrsData, ErrsPatch, ErrsQuery } from './errs.schema.js'

export type { Errs, ErrsData, ErrsPatch, ErrsQuery }

export interface ErrsParams extends MongoDBAdapterParams<ErrsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ErrsService<ServiceParams extends Params = ErrsParams> extends MongoDBService<
  Errs,
  ErrsData,
  ErrsParams,
  ErrsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('errs'))
  }
}
