// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import { hooks as schemaHooks } from '@feathersjs/schema'

import {
  errsDataValidator,
  errsPatchValidator,
  errsQueryValidator,
  errsResolver,
  errsExternalResolver,
  errsDataResolver,
  errsPatchResolver,
  errsQueryResolver
} from './errs.schema.js'

import type { Application } from '../../declarations.js'
import { ErrsService, getOptions } from './errs.class.js'
import { errsPath, errsMethods } from './errs.shared.js'

export * from './errs.class.js'
export * from './errs.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const errs = (app: Application) => {
  // Register our service on the Feathers application
  app.use(errsPath, new ErrsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: errsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(errsPath).hooks({
    around: {
      all: [schemaHooks.resolveExternal(errsExternalResolver), schemaHooks.resolveResult(errsResolver)]
    },
    before: {
      all: [schemaHooks.validateQuery(errsQueryValidator), schemaHooks.resolveQuery(errsQueryResolver)],
      find: [],
      get: [],
      create: [schemaHooks.validateData(errsDataValidator), schemaHooks.resolveData(errsDataResolver)],
      patch: [schemaHooks.validateData(errsPatchValidator), schemaHooks.resolveData(errsPatchResolver)],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [errsPath]: ErrsService
  }
}
