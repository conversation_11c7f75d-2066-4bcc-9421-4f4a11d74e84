// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Fbs, FbsData, FbsPatch, FbsQuery } from './fbs.schema.js'

export type { Fbs, FbsData, FbsPatch, FbsQuery }

export interface FbsParams extends MongoDBAdapterParams<FbsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class FbsService<ServiceParams extends Params = FbsParams> extends MongoDBService<
  Fbs,
  FbsData,
  FbsParams,
  FbsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('fbs')),
    multi: true,
    operators: ['$regex', '$options']

  }
}
