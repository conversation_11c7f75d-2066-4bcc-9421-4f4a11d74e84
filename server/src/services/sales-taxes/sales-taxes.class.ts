// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { SalesTaxes, SalesTaxesData, SalesTaxesPatch, SalesTaxesQuery } from './sales-taxes.schema.js'

export type { SalesTaxes, SalesTaxesData, SalesTaxesPatch, SalesTaxesQuery }

export interface SalesTaxesParams extends MongoDBAdapterParams<SalesTaxesQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class SalesTaxesService<ServiceParams extends Params = SalesTaxesParams> extends MongoDBService<
  SalesTaxes,
  SalesTaxesData,
  SalesTaxesParams,
  SalesTaxesPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient')
        .then((db) => db.collection('sales-taxes'))
        .then((collection) => {
          collection.createIndex({postal_code: 1}, {unique: true})
          return collection;
        }),
  }
}
