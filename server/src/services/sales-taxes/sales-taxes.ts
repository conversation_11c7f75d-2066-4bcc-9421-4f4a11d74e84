// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    salesTaxesDataValidator,
    salesTaxesPatchValidator,
    salesTaxesQueryValidator,
    salesTaxesResolver,
    salesTaxesExternalResolver,
    salesTaxesDataResolver,
    salesTaxesPatchResolver,
    salesTaxesQueryResolver
} from './sales-taxes.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {SalesTaxesService, getOptions} from './sales-taxes.class.js'
import {salesTaxesPath, salesTaxesMethods} from './sales-taxes.shared.js'

export * from './sales-taxes.class.js'
export * from './sales-taxes.schema.js'

import {CoreCall, noThrowAuth} from 'feathers-ucan';
import axios from 'axios';

const preventExternalChanges = (context: HookContext): HookContext => {
    if (!context.params.sales_tax_create) throw new Error('External writes to sales tax not permitted')
    return context;
}

const getTax = axios.create({
    baseURL: 'https://api.zip-tax.com/request/v40',
    withCredentials: false,
    timeout: 5000,
});

const getOutdatedOrEmpty = async context => {

    //query data is in context.params.query ^^ex: postal_code = context.params.query.postal_code
    // eslint-disable-next-line no-console
    if (!context.result.total) {
        const { postal_code, city } = context.params.query || {};
        const params:any = {
            'key': context.app.get('salesTax').api_key,
            postal_code
        }
        if(city) params.city = city;
        // eslint-disable-next-line no-console
        let res = await getTax({
            'method': 'GET',
            'url': '/',
            params
        }).catch(err => {
            // eslint-disable-next-line no-console
            console.error(`Error getting sales tax: ${err.message}`);
            throw new Error(`Could not get sales tax rate: ${err.message}`);
        });
        if(res.data.results[0])

            if(!postal_code){
                throw new Error('Zip code required for sales tax search');
                // if(!city || !state) throw new Error('City & state or postal code is required for sales tax search');
                // else {
                //     const stDrawer = await new CoreCall('junk-drawers', context).find({ itemId: `states|${state.toLowerCase()}`});
                //     if(!stDrawer.total) throw new Error(`No matching state found for: ${state}`);
                //     const d = stDrawer.data[0];
                //     const string = city.toLowerCase();
                //     const cap = string.split(' ').map(str => str.charAt(0).toUpperCase() + str.slice(1)).join(' ');
                //     const cITy = d.data.cities[cap];
                //     if(!cITy) throw new Error(`No matching city found in ${state} for the name ${cap}`);
                //     postal_code = Array.isArray(city[0]);
                // }
            }
        // eslint-disable-next-line no-console
        await new CoreCall('sales-taxes', context).create({
            country: 'US',
            city,
            postal_code,
            total_tax: res.data.results[0].taxSales,
            taxes: [
                {
                    name: 'State Sales Tax',
                    type: 'percent',
                    rate: res.data.results[0].stateSalesTax
                },
                {
                    name: 'City Sales Tax',
                    type: 'percent',
                    rate: res.data.results[0].citySalesTax
                },
                {
                    name: 'County Sales Tax',
                    type: 'percent',
                    rate: res.data.results[0].countySalesTax
                },
                {
                    name: 'District Sales Tax',
                    type: 'percent',
                    rate: res.data.results[0].districtSalesTax
                },
            ]
        }, { sales_tax_create: true }).then(response => {
            // eslint-disable-next-line no-console
            context.result.data = [response];
            return context;
        }).catch(err => {
            // eslint-disable-next-line no-console
            console.error(`Error creating sales tax: ${err.message}`);
            throw new Error(`Error creating sales tax: ${err.message}`);
        });
    }
};

const checkState = async(context:HookContext):Promise<HookContext> => {
    if(!context.data.state){
        const d = await new CoreCall('junk-drawers', context).find({
            query: { itemId: `zips|${context.data.postal_code.slice(0,3)}`}
        })
        if(!d.total) throw new Error(`Postal code ${context.data.postal_code} not recognized`);
        context.data.state = d.data[0].data[context.data.postal_code].state;
    }
    return context;
}
// A configure function that registers the service and its hooks via `app.configure`
export const salesTaxes = (app: Application) => {
    // Register our service on the Feathers application
    app.use(salesTaxesPath, new SalesTaxesService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: salesTaxesMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(salesTaxesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(salesTaxesExternalResolver),
                schemaHooks.resolveResult(salesTaxesResolver)
            ]
        },
        before: {
            all: [
                noThrowAuth,
                schemaHooks.validateQuery(salesTaxesQueryValidator),
                schemaHooks.resolveQuery(salesTaxesQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                preventExternalChanges,
                checkState,
                schemaHooks.validateData(salesTaxesDataValidator),
                schemaHooks.resolveData(salesTaxesDataResolver)
            ],
            update: [preventExternalChanges],
            patch: [
                preventExternalChanges,
                schemaHooks.validateData(salesTaxesPatchValidator),
                schemaHooks.resolveData(salesTaxesPatchResolver)
            ],
            remove: [preventExternalChanges]
        },
        after: {
            all: [],
            find: [getOutdatedOrEmpty]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [salesTaxesPath]: SalesTaxesService
    }
}
