// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    conditionsDataValidator,
    conditionsPatchValidator,
    conditionsQueryValidator,
    conditionsResolver,
    conditionsExternalResolver,
    conditionsDataResolver,
    conditionsPatchResolver,
    conditionsQueryResolver
} from './conditions.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {ConditionsService, getOptions} from './conditions.class.js'
import {conditionsPath, conditionsMethods} from './conditions.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall} from 'feathers-ucan';
import {logChange} from '../../utils/index.js';

export * from './conditions.class.js'
export * from './conditions.schema.js'

import { searchApi } from '../procedures/utils/search.js';
import {hackIdAfter, hackIdBefore} from '../cross-sections/utils/index.js';

const authenticate = async (context: HookContext) => {
    const deleter = [['conditions', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: deleter,
        update: deleter,
        remove: deleter
    };
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove']
    })(context) as any;
}

// A configure function that registers the service and its hooks via `app.configure`
export const conditions = (app: Application) => {
    // Register our service on the Feathers application
    app.use(conditionsPath, new ConditionsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: conditionsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(conditionsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(conditionsExternalResolver),
                schemaHooks.resolveResult(conditionsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(conditionsQueryValidator),
                schemaHooks.resolveQuery(conditionsQueryResolver)
            ],
            find: [hackIdBefore('plan_conditions', 'plans', 'conditions')],
            get: [],
            create: [
                schemaHooks.validateData(conditionsDataValidator),
                schemaHooks.resolveData(conditionsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(conditionsPatchValidator),
                schemaHooks.resolveData(conditionsPatchResolver)
            ],
            remove: []
        },
        after: {
            all: [],
            find: [
                searchApi('conditions'),
                hackIdAfter('plan_conditions', 'plans'),
            ]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [conditionsPath]: ConditionsService
    }
}
