// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    catsDataValidator,
    catsPatchValidator,
    catsQueryValidator,
    catsResolver,
    catsExternalResolver,
    catsDataResolver,
    catsPatchResolver,
    catsQueryResolver
} from './cats.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {CatsService, getOptions} from './cats.class.js'
import {catsPath, catsMethods} from './cats.shared.js'
import {logChange} from '../../utils/index.js';
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';

export * from './cats.class.js'
export * from './cats.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['cats', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['cats', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = []

    if (['patch', 'remove'].includes(context.method)) {
        const ex = await loadExists(context);
        context = setExists(context, ex);
        if (ex.org) {
            const orgNamespace = `orgs:${ex.org}`
            cap_subjects.push(ex.org);
            const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin']];
            for (const w of orgWrite) {
                ucanArgs.patch.unshift(w);
            }
        }
    }

    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['get', 'find', 'patch', 'create', 'remove'],
        loginPass: [[['managers/owner'], '*'], [['writers/owner'], ['patch', 'create']]],
        cap_subjects
    })(context) as any;
}

export const addCreator = async (context: HookContext) => {
    const managers = context.data.managers || []
    const personId = context.params.login?.owner
    if (personId && !managers.includes(personId)) context.data.managers = [...managers, personId]
    return context;
}

const addProcedureCategory = async (context: HookContext) => {
    if (context.params.query?.category) {
        const procedures = await new CoreCall('procedures', context)._find({
            category: context.params.query.category,
            $limit: 5000,
            skip_hooks: true,
            admin_pass: true
        })
        if (procedures.total) {
            context.data.procedures = context.method === 'patch' ? {$addToSet: {$each: procedures.data.map(a => a._id)}} : procedures.data.map(a => a._id);
        }
    }
    return context;
}

import {nameCats} from './utils/gen-cats.js';
// A configure function that registers the service and its hooks via `app.configure`
export const cats = (app: Application) => {
    // Register our service on the Feathers application
    app.use(catsPath, new CatsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: catsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(catsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(catsExternalResolver),
                schemaHooks.resolveResult(catsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(catsQueryValidator),
                schemaHooks.resolveQuery(catsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                addCreator,
                addProcedureCategory,
                schemaHooks.validateData(catsDataValidator),
                schemaHooks.resolveData(catsDataResolver)
            ],
            patch: [
                addProcedureCategory,
                schemaHooks.validateData(catsPatchValidator),
                schemaHooks.resolveData(catsPatchResolver)
            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [catsPath]: CatsService
    }
}
