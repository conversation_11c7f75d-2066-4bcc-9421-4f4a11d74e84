// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Visits, VisitsData, VisitsPatch, VisitsQuery } from './visits.schema.js'

export type { Visits, VisitsData, VisitsPatch, VisitsQuery }

export interface VisitsParams extends MongoDBAdapterParams<VisitsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class VisitsService<ServiceParams extends Params = VisitsParams> extends MongoDBService<
  Visits,
  VisitsData,
  VisitsParams,
  VisitsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('visits')),
    multi: true
  }
}
