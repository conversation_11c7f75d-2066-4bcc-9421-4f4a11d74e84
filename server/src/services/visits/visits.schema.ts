// TypeBox schema for visits service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Entered by schema
const EnteredBySchema = Type.Object({
  login: Type.Optional(ObjectIdSchema()),
  fingerprint: Type.Optional(ObjectIdSchema()),
  at: Type.Optional(Type.Any())
}, { additionalProperties: false })

// Main data model schema
export const visitsSchema = Type.Object({
  _id: ObjectIdSchema(),
  patient: ObjectIdSchema(),
  person: ObjectIdSchema(),
  provider: ObjectIdSchema(),
  practitioner: Type.Optional(ObjectIdSchema()),
  date: Type.Optional(Type.Any()),
  scheduledDate: Type.Optional(Type.Any()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  reason: Type.Optional(Type.String()),
  diagnosis: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  enteredBy: Type.Optional(EnteredBySchema),
  duration: Type.Optional(Type.Number()),
  location: Type.Optional(Type.String()),
  room: Type.Optional(Type.String()),
  vitals: Type.Optional(Type.Record(Type.String(), Type.Any())),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  medications: Type.Optional(Type.Array(ObjectIdSchema())),
  followUp: Type.Optional(Type.String()),
  referrals: Type.Optional(Type.Array(ObjectIdSchema())),
  attachments: Type.Optional(Type.Array(Type.Any())),
  billing: Type.Optional(Type.Record(Type.String(), Type.Any())),
  insurance: Type.Optional(Type.Record(Type.String(), Type.Any())),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  deductible: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number()),
  paid: Type.Optional(Type.Number()),
  balance: Type.Optional(Type.Number()),
  ...commonFields.properties
,
  // Missing fields from old schema
  care: Type.Optional(Type.String()),
  plan: Type.Optional(ObjectIdSchema()),
  preventive: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Visits = Static<typeof visitsSchema>
export const visitsValidator = getValidator(visitsSchema, dataValidator)
export const visitsResolver = resolve<Visits, HookContext>({})
export const visitsExternalResolver = resolve<Visits, HookContext>({})

// Schema for creating new data
export const visitsDataSchema = Type.Object({
  ...Type.Omit(visitsSchema, ['_id']).properties
}, { additionalProperties: false })

export type VisitsData = Static<typeof visitsDataSchema>
export const visitsDataValidator = getValidator(visitsDataSchema, dataValidator)
export const visitsDataResolver = resolve<VisitsData, HookContext>({})

// Schema for updating existing data
export const visitsPatchSchema = Type.Object({
  patient: Type.Optional(ObjectIdSchema()),
  person: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  practitioner: Type.Optional(ObjectIdSchema()),
  date: Type.Optional(Type.Any()),
  scheduledDate: Type.Optional(Type.Any()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  reason: Type.Optional(Type.String()),
  diagnosis: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  enteredBy: Type.Optional(EnteredBySchema),
  duration: Type.Optional(Type.Number()),
  location: Type.Optional(Type.String()),
  room: Type.Optional(Type.String()),
  vitals: Type.Optional(Type.Record(Type.String(), Type.Any())),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  medications: Type.Optional(Type.Array(ObjectIdSchema())),
  followUp: Type.Optional(Type.String()),
  referrals: Type.Optional(Type.Array(ObjectIdSchema())),
  attachments: Type.Optional(Type.Array(Type.Any())),
  billing: Type.Optional(Type.Record(Type.String(), Type.Any())),
  insurance: Type.Optional(Type.Record(Type.String(), Type.Any())),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  deductible: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number()),
  paid: Type.Optional(Type.Number()),
  balance: Type.Optional(Type.Number()),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $push: Type.Optional(Type.Object({
    procedures: Type.Optional(ObjectIdSchema()),
    medications: Type.Optional(ObjectIdSchema()),
    referrals: Type.Optional(ObjectIdSchema()),
    attachments: Type.Optional(Type.Any())
  ,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })),
  $pull: Type.Optional(Type.Object({
    procedures: Type.Optional(ObjectIdSchema()),
    medications: Type.Optional(ObjectIdSchema()),
    referrals: Type.Optional(ObjectIdSchema()),
    attachments: Type.Optional(Type.Any())
  }, { additionalProperties: false }))
}, { additionalProperties: false })

export type VisitsPatch = Static<typeof visitsPatchSchema>
export const visitsPatchValidator = getValidator(visitsPatchSchema, dataValidator)
export const visitsPatchResolver = resolve<VisitsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const visitsQueryProperties = visitsSchema

export const visitsQuerySchema = querySyntax(visitsQueryProperties)

export type VisitsQuery = Static<typeof visitsQuerySchema>
export const visitsQueryValidator = getValidator(visitsQuerySchema, queryValidator)
export const visitsQueryResolver = resolve<VisitsQuery, HookContext>({})
