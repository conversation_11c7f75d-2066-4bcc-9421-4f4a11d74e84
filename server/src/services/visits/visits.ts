// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    visitsDataValidator,
    visitsPatchValidator,
    visitsQueryValidator,
    visitsResolver,
    visitsExternalResolver,
    visitsDataResolver,
    visitsPatchResolver,
    visitsQueryResolver
} from './visits.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {VisitsService, getOptions} from './visits.class.js'
import {visitsPath, visitsMethods} from './visits.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {findJoin, getJoin, logChange, relate, scrubUploads} from '../../utils/index.js';
import {loadOrCreate} from '../cross-sections/utils/index.js';
import {getClaimPrice} from '../claims/utils/index.js';
import {ObjectId} from 'mongodb';

export * from './visits.class.js'
export * from './visits.schema.js'

const runJoins = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks || context.params.exists_check) return context;
    const {visit_provider, visit_claims} = context.params.runJoin || {};
    if (visit_provider) {
        context = await getJoin({
            herePath: 'provider',
            service: 'providers'
        })(context)
    }
    if (visit_claims) context = await findJoin({
        service: 'claims',
        herePath: 'claims',
        therePath: '_id',
        queryFn: (item: any) => {
            return {
                _id: {$in: item.claims || []}
            }
        }
    })(context);
    return context;
}

const authenticate = async (context: HookContext) => {
    const writer = [['visits', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['visits', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create') {

            const existing = await loadExists(context, {params: {runJoin: {visit_provider: true}}});
            context = setExists(context, existing);
            //allow changes before approval
            if (!existing.approvedAt) context.params.admin_pass = true;

            if (existing) {
                const orgNamespace = `orgs:${existing._fastjoin?.provider?.org}`;
                const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'providerAdmin'], [orgNamespace, 'billingAdmin']]
                for (const w of orgWrite) {
                    ucanArgs.patch.unshift(w);
                    ucanArgs.update.unshift(w);
                    ucanArgs.remove.unshift(w);
                }
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['person/owner'], '*']]
    })(context) as any;
}

const relateCare = async (context: HookContext) => {
    return relate('otm', {
        herePath: 'care',
        therePath: 'visits',
        thereService: 'cares',
        paramsName: 'visitToCare'
    })(context);
}

//Automatically update type when bills or claims are added
const setType = (context: HookContext) => {
    const {$set, claims, bills, $addToSet} = context.data || {};
    if (claims || bills) context.data.status = 'record';
    else if (context.data !== 'record') {
        if ($set || $addToSet) {
            for (const k in {...$set, ...$addToSet}) {
                if (/bills\./.test(k) || /claims/.test(k)) {
                    context.data.status = 'record';
                    break;
                }
            }
        }
    }
    return context;
}

const trackProvider = async (context: HookContext): Promise<HookContext> => {
    const sets: any = {};
    let run;
    const prv = context.data.provider || context.data.$set?.provider;
    if (prv) {
        run = true;
        sets.provider = prv
    }
    const allP = context.data.practitioners || context.data.$set?.practitioners;
    if (allP) {
        run = true;
        sets.practitioners = [];
        for (const k of allP) {
            sets.practitioners.push(k);
        }
    } else {
        const {practitioners} = context.data.$addToSet || {};
        if (practitioners) {
            const p = practitioners.$each ? practitioners.$each : [practitioners];
            sets.practitioners = [...sets.practitioners || [], ...p];
            run = true;
        }

    }

    if (run) {
        const patchObj: any = {$addToSet: {}};
        let provider_doc;
        let sections: any = undefined;
        if (sets.practitioners?.length) {

            patchObj.$addToSet.practitioners = {$each: sets.practitioners};
            const provider = sets.provider || context.result.provider;
            sections = {practitioners: sets.practitioners}
            provider_doc = await loadOrCreate(`provider_docs:providers:${provider}`, {practitioners: sets.practitioners})(context)
                .catch(err => console.error(`Could not add cross section provider_docs:providers:${provider} - ${err.message}`));

        }
        if (sets.provider) {
            patchObj.$addToSet.providers = sets.provider;
            const practitioners = sets.practitioners || context.result.practitioner;
            if (!provider_doc) {
                if (practitioners) {
                    provider_doc = await loadOrCreate(`provider_docs:providers:${sets.provider}`, {practitioners})(context)
                        .catch(err => {
                            console.error(`Could not add cross section provider_docs:providers:${sets.provider} - ${err.message}`);
                        });
                }
            }
            sections = {...sections, providers: sets.provider}
        }
        if (sections) await loadOrCreate(`plan_providers:plans:${context.result.plan}`, sections)(context)
            .catch(err => console.log(`Could not add cross section plan_providers:plans:${context.result.plan}: ${err.message}`));

        await new CoreCall('cares', context, {skipJoins: true}).patch(context.result.care, patchObj, {admin_pass: true});
    }
    return context;
}

const watchDate = async (context: HookContext): Promise<HookContext> => {
    const date = context.data.date || context.data.$set?.date;
    if (date && context.result.claims && !context.params.skip_hooks) {
        await new CoreCall('claims', context, {skipJoins: true})._patch(null, {date}, {skip_hooks: true, admin_pass: true, query: {_id: {$in: context.result.claims}}})
            .catch(err => {
                console.error(`Error syncing dates from visit to claims: ${err.message}`)
                return;
            })
    }
    return context;
}

const addConditionsToCare = async (context: HookContext): Promise<HookContext> => {
    const {$addToSet, $pull} = context.data;
    const addC = $addToSet?.conditions;
    const patchObj: any = {}
    if (addC) {
        patchObj.$addToSet = {conditions: addC}
    }
    const ids = $pull?.conditions?.id
    if (ids) {
        const arr = ids.$in ? ids.$in : [ids];
        const related = await new CoreCall('visits', context, {skipJoins: true}).find({
            query: {
                $limit: 100,
                care: context.result.care
            }
        })
            .catch(err => console.error(`Failed to get related visits to $pull condition from care event on visit _id ${context.result._id}: ${err.message}`))
        const flatVisits = related.data.map(a => (a.conditions || []).map(b => b.id)).flat(2);
        const pullArr: any = [];
        for (let i = 0; i < arr.length; i++) {
            if (!flatVisits.includes(arr[i])) pullArr.push(arr[i]);
        }
        if (pullArr.length) {
            patchObj.$pull = {conditions: {id: {$in: pullArr}}}
        }
    }
    if (Object.keys(patchObj).length) {
        await new CoreCall('cares', context).patch(context.result.care, patchObj, {admin_pass: true})
            .catch(err => console.error(`Failed to update care for add/remove conditions to visit _id ${context.result._id}: ${err.message}`))
    }
    return context;
}

const copyCare = async (context: HookContext): Promise<HookContext> => {
    const {care} = context.data;
    if (care) {
        const {patient, person, plan} = await new CoreCall('cares', context).get(care);
        context.data = {...context.data, patient, person, plan}
    } else throw new Error('Care event required to create visit');
    return context;
}

const protect = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks) return context;
    if (context.method === 'remove' || context.data.deleted || context.data.$set?.deleted) {
        const ex = await loadExists(context);
        context = setExists(context, ex);
        if (ex.claims?.length || ex.claimRequets?.length) throw new Error('Cannot delete this visit because it has claims associated with it')
    }
    return context;
}

const syncBalance = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks) return context;
    const {balanceSyncedAt} = context.data;
    if (balanceSyncedAt) {
        const ex = await loadExists(context);
        context = setExists(context, ex);
        const related = await new CoreCall('claims', context)._find({
            skip_hooks: true, admin_pass: true,
            query: { deleted: { $ne: true }, visit: typeof(context.id) === 'string' ? ObjectId.createFromHexString(context.id as string) : context.id },
            paginate: false,
            pipeline: [
                {
                  $group: {
                      _id: null,
                      claim_total: { $sum: '$total' },
                      claim_subtotal: { $sum: '$subtotal' },
                      claim_balance: { $sum: '$balance' },
                      claim_paid: { $sum: '$paid.amount' },
                      claim_pending: { $sum: '$pending.amount' },
                      claim_ded: { $sum: '$paid.ded' },
                      claim_ded_pending: { $sum: '$pending.ded' },
                      claim_coins: { $sum: '$paid.coins' },
                      claim_coins_pending: { $sum: '$pending.coins' }
                  }
                }
            ]
        });
        if(related[0].claim_total) {
            const {
                claim_total,
                claim_subtotal,
                claim_balance,
                claim_paid,
                claim_ded,
                claim_coins,
                claim_ded_pending,
                claim_coins_pending,
                claim_pending
            } = related[0];
            context.data.$set = {
                ...context.data.$set,
                subtotal: claim_subtotal,
                total: claim_total,
                balance: claim_balance,
                paid: { amount: claim_paid, ded: claim_ded, coins: claim_coins },
                pending: { amount: claim_pending, ded: claim_ded_pending, coins: claim_coins_pending }
            };
            await new CoreCall('cares', context, { skipJoins: true }).patch(ex.care, { balanceSyncedAt: new Date() });
        }
    }
    return context;
}

const paths = ['files.*'];
const uploadsConfig = {paths};

// A configure function that registers the service and its hooks via `app.configure`
export const visits = (app: Application) => {
    // Register our service on the Feathers application
    app.use(visitsPath, new VisitsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: visitsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(visitsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(visitsExternalResolver),
                schemaHooks.resolveResult(visitsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(visitsQueryValidator),
                schemaHooks.resolveQuery(visitsQueryResolver),
                scrubUploads(uploadsConfig)
            ],
            find: [],
            get: [],
            create: [
                copyCare,
                schemaHooks.validateData(visitsDataValidator),
                schemaHooks.resolveData(visitsDataResolver),
                relateCare
            ],
            patch: [
                schemaHooks.validateData(visitsPatchValidator),
                schemaHooks.resolveData(visitsPatchResolver),
                protect,
                syncBalance,
                relateCare,
                setType
            ],
            remove: [protect, relateCare]
        },
        after: {
            all: [
                runJoins,
                scrubUploads(uploadsConfig)
            ],
            create: [relateCare, trackProvider],
            patch: [relateCare, trackProvider, watchDate, addConditionsToCare],
            remove: [relateCare]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [visitsPath]: VisitsService
    }
}
