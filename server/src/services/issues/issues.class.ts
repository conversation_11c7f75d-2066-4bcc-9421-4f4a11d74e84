// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Issues, IssuesData, IssuesPatch, IssuesQuery } from './issues.schema.js'

export type { Issues, IssuesData, IssuesPatch, IssuesQuery }

export interface IssuesParams extends MongoDBAdapterParams<IssuesQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class IssuesService<ServiceParams extends Params = IssuesParams> extends MongoDBService<
  Issues,
  IssuesData,
  IssuesParams,
  IssuesPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('issues'))
  }
}
