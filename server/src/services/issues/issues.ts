// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    issuesDataValidator,
    issuesPatchValidator,
    issuesQueryValidator,
    issuesResolver,
    issuesExternalResolver,
    issuesDataResolver,
    issuesPatchResolver,
    issuesQueryResolver
} from './issues.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {IssuesService, getOptions} from './issues.class.js'
import {issuesPath, issuesMethods} from './issues.shared.js'
import {CapabilityParts, allUcanAuth, anyAuth, loadExists, setExists, CoreCall} from 'feathers-ucan';
import {logChange} from '../../utils/index.js';

export * from './issues.class.js'
export * from './issues.schema.js'


const authenticate = async (context: HookContext) => {
    const writer = [['issues', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['issues', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };

    return await allUcanAuth<HookContext>(ucanArgs, {
        creatorPass: ['*'],
        adminPass: ['patch', 'create', 'remove']
    })(context) as any;
}

import sgMail from '@sendgrid/mail';
import {complaintEmail} from './utils/complaint-email.js';

const notifyComplaints = async (context: HookContext) => {
    const {type} = context.result;
    if (type === 'complaint') {
        const {key} = context.app.get('mailer') || {};
        sgMail.setApiKey(key as string);
        await sgMail.send({
            from: 'CommonCare Notifications <<EMAIL>>',
            to: '<EMAIL>',
            subject: 'CommonCare Complaints',
            html: complaintEmail(`Complaint message: ${context.result.message || ''}`, `https://console.commoncare.org/issues/${context.result._id}`)
        })
            .catch(err => {
                console.log('error sending complaint message', err);
            });
    }
    return context;
}

const treasurySync = async (context: HookContext) => {
    const {treasuryComplaint, $set} = context.data;
    let setOn = !!$set?.treasuryComplaint;
    if(!setOn) for(const k in $set || {}){
        if(/treasuryData/.test(k)){
            setOn = true;
            break;
        }
    }
    if (treasuryComplaint || setOn) {
        const exists = await loadExists(context);
        context = setExists(context, exists);
        const setPath = (path:string) => {
            return (context.data.$set || {})[`treasuryData.${path}`] || context.data[path] || exists[path];
        }
        const mapper = {
            'date_input': () => setPath('createdAt'),
            'date_received': () => setPath('assignedAt'),
            'date_resolved': () => setPath('resolvedAt')
        }
        const data: any = exists?.treasuryData || {};
        for (const k in mapper) {
            if(!data[k]) {
                if (context.data.$set) context.data.$set[`treasuryData.${k}`] = mapper[k]();
                else (context.data.$set) = {[`treasuryData.${k}`]: mapper[k]()}
            }
        }
        if(!exists.treasuryData.user_name){
            const person = await new CoreCall('ppls', context, { skipJoins: true }).get(exists.by, { admin_pass: true})
            context.data.$set = { ...context.data.$set, 'treasuryData.user_name': person.name }
        }
        if(!exists.treasuryData.received_by && exists.assigned){
            const person = await new CoreCall('ppls', context, { skipJoins: true }).get(exists.assigned, { admin_pass: true})
            context.data.$set = { ...context.data.$set, 'treasuryData.received_by': person.name }
        }
        if(!exists.treasuryData.account_ID && exists.org){
            const org = await new CoreCall('orgs', context, { skipJoins: true }).get(exists.org, { admin_pass: true })
            if(org.treasury?.id) context.data.$set = { ...context.data.$set, 'treasuryData.account_ID': org.treasury.id }
        }
    }
    return context;
}

const resolveAt = async (context: HookContext) => {
    const {status, $set} = context.data;
    const s = status || $set?.status
    if (s === 'resolved') {
        const exists = await loadExists(context);
        context = setExists(context, exists);
        if (exists.status !== 'resolved') {
            context.data.resolvedAt = new Date()
        }
    }
    return context;
}

const addBy = async(context: HookContext): Promise<HookContext> => {
    if(!context.data.by){
        const person = await new CoreCall('ppls', context, { skipJoins: true }).find({ query: { _id: context.params.login.owner, $limit: 1}, admin_pass: true });
        context.data.by = person.data[0]._id;
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const issues = (app: Application) => {
    // Register our service on the Feathers application
    app.use(issuesPath, new IssuesService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: issuesMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(issuesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(issuesExternalResolver),
                schemaHooks.resolveResult(issuesResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(issuesQueryValidator),
                schemaHooks.resolveQuery(issuesQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                addBy,
                schemaHooks.validateData(issuesDataValidator),
                schemaHooks.resolveData(issuesDataResolver),
            ],
            patch: [
                schemaHooks.validateData(issuesPatchValidator),
                schemaHooks.resolveData(issuesPatchResolver),
                treasurySync,
                resolveAt
            ],
            remove: []
        },
        after: {
            all: [],
            create: [notifyComplaints]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [issuesPath]: IssuesService
    }
}
