import {CoreCall} from 'feathers-ucan';
import {HookContext} from '../../../declarations.js';
import {Ping, PingConfig} from '../../../utils/index.js';

export const genPingArgs = ({id, planName}:{id?:string,planName?:string}) => {
    return async (context: HookContext): Promise<Ping> => {
        const origin = context.app.get('origin');
        const config: PingConfig = {
            subjectPath: 'plan',
            subjectNamePath: 'name',
            subjectService: 'plans',
            recipientService: 'ppls',
            recipientPath: 'person',
            message: 'Your company health plan enrollment period open',
            action: 'enroll',
            category: 'plans',
            priority: 5,
            methods: {
                internal: {send: true},
                email: {send: true},
                sms: {send: true}
            }
        }
        if (context.type === 'after') {
            let fullPlan = { name: planName };
           if(!planName) fullPlan = await new CoreCall('plans', context, {skipJoins: true}).get(Array.isArray(context.result) ? context.result[0].plan : context.result.plan)
                .catch(err => {
                    console.error(`Error getting full plan for ping config`, err.message);
                    return {name: 'your group health plan'}
                })
            config.message = `You have been invited to enroll in  ${fullPlan.name}`;

            config.link = `${origin || 'https://commoncare.org'}/enroll/${id}`
        }
        return {
            create: {
                '*': {config}
            },
            created: {
                '*': {config}
            },
            patch: {
                '*': {config}
            },
            patched: {
                '*': {config}
            }
        }
    }
}
