// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Enrollments, EnrollmentsData, EnrollmentsPatch, EnrollmentsQuery } from './enrollments.schema.js'

export type { Enrollments, EnrollmentsData, EnrollmentsPatch, EnrollmentsQuery }

export interface EnrollmentsParams extends MongoDBAdapterParams<EnrollmentsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class EnrollmentsService<ServiceParams extends Params = EnrollmentsParams> extends MongoDBService<
  Enrollments,
  EnrollmentsData,
  EnrollmentsParams,
  EnrollmentsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient')
        .then((db) => db.collection('enrollments'))
        .then((collection) => {
          collection.createIndex({idempotency_key: 1}, {unique: true});
          return collection
        })
  }
}
