// TypeBox schema for enrollments service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, MandateSchema, addToSet } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const enrollmentsSchema = Type.Object({
  _id: ObjectIdSchema(),
  person: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  coverage: Type.Optional(ObjectIdSchema()),
  household: Type.Optional(ObjectIdSchema()),
  group: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  status: Type.Optional(Type.String()),
  premium: Type.Optional(Type.Number()),
  subsidyAmount: Type.Optional(Type.Number()),
  employerContribution: Type.Optional(Type.Number()),
  deductibleMet: Type.Optional(Type.Number()),
  oopMet: Type.Optional(Type.Number()),
  mandate: Type.Optional(MandateSchema),
  notes: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
,
  // Missing fields from old schema
  idempotency_key: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Enrollments = Static<typeof enrollmentsSchema>
export const enrollmentsValidator = getValidator(enrollmentsSchema, dataValidator)
export const enrollmentsResolver = resolve<Enrollments, HookContext>({})
export const enrollmentsExternalResolver = resolve<Enrollments, HookContext>({})

// Schema for creating new data
export const enrollmentsDataSchema = Type.Object({
  ...Type.Omit(enrollmentsSchema, ['_id']).properties
}, { additionalProperties: false })

export type EnrollmentsData = Static<typeof enrollmentsDataSchema>
export const enrollmentsDataValidator = getValidator(enrollmentsDataSchema, dataValidator)
export const enrollmentsDataResolver = resolve<EnrollmentsData, HookContext>({})

// Schema for updating existing data
export const enrollmentsPatchSchema = Type.Object({
  person: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  coverage: Type.Optional(ObjectIdSchema()),
  household: Type.Optional(ObjectIdSchema()),
  group: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  status: Type.Optional(Type.String()),
  premium: Type.Optional(Type.Number()),
  subsidyAmount: Type.Optional(Type.Number()),
  employerContribution: Type.Optional(Type.Number()),
  deductibleMet: Type.Optional(Type.Number()),
  oopMet: Type.Optional(Type.Number()),
  mandate: Type.Optional(MandateSchema),
  notes: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number()))
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })

export type EnrollmentsPatch = Static<typeof enrollmentsPatchSchema>
export const enrollmentsPatchValidator = getValidator(enrollmentsPatchSchema, dataValidator)
export const enrollmentsPatchResolver = resolve<EnrollmentsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const enrollmentsQueryProperties = enrollmentsSchema

export const enrollmentsQuerySchema = querySyntax(enrollmentsQueryProperties)

export type EnrollmentsQuery = Static<typeof enrollmentsQuerySchema>
export const enrollmentsQueryValidator = getValidator(enrollmentsQuerySchema, queryValidator)
export const enrollmentsQueryResolver = resolve<EnrollmentsQuery, HookContext>({})
