// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import { hooks as schemaHooks } from '@feathersjs/schema'

import {
  fundsRequestsDataValidator,
  fundsRequestsPatchValidator,
  fundsRequestsQueryValidator,
  fundsRequestsResolver,
  fundsRequestsExternalResolver,
  fundsRequestsDataResolver,
  fundsRequestsPatchResolver,
  fundsRequestsQueryResolver
} from './funds-requests.schema.js'

import type { Application } from '../../declarations.js'
import { FundsRequestsService, getOptions } from './funds-requests.class.js'
import { fundsRequestsPath, fundsRequestsMethods } from './funds-requests.shared.js'

export * from './funds-requests.class.js'
export * from './funds-requests.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const fundsRequests = (app: Application) => {
  // Register our service on the Feathers application
  app.use(fundsRequestsPath, new FundsRequestsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: fundsRequestsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(fundsRequestsPath).hooks({
    around: {
      all: [
        schemaHooks.resolveExternal(fundsRequestsExternalResolver),
        schemaHooks.resolveResult(fundsRequestsResolver)
      ]
    },
    before: {
      all: [
        schemaHooks.validateQuery(fundsRequestsQueryValidator),
        schemaHooks.resolveQuery(fundsRequestsQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(fundsRequestsDataValidator),
        schemaHooks.resolveData(fundsRequestsDataResolver)
      ],
      patch: [
        schemaHooks.validateData(fundsRequestsPatchValidator),
        schemaHooks.resolveData(fundsRequestsPatchResolver)
      ],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [fundsRequestsPath]: FundsRequestsService
  }
}
