// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import type { Application } from '../../declarations.js'
import { UcansService, getOptions } from './ucans.class.js'
import { ucansPath, ucansMethods } from './ucans.shared.js'
import { updateUcan } from './utils/index.js';
import { bareAuth } from 'feathers-ucan';
export * from './ucans.class.js'

// A configure function that registers the service and its hooks via `app.configure`
export const ucans = (app: Application) => {
  // Register our service on the Feathers application
  app.use(ucansPath, new UcansService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: ucansMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(ucansPath).hooks({
    around: {
      all: []
    },
    before: {
      all: [bareAuth],
      find: [],
      get: [],
      create: [],
      patch: [updateUcan()],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [ucansPath]: UcansService
  }
}
