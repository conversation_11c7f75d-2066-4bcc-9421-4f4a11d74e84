// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {Ppls, PplsData, PplsPatch, PplsQuery} from './ppls.schema.js'
import {CoreCall} from 'feathers-ucan';

export type {Ppls, PplsData, PplsPatch, PplsQuery}

export interface PplsParams extends MongoDBAdapterParams<PplsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class PplsService<ServiceParams extends Params = PplsParams> extends MongoDBService<
    Ppls,
    PplsData,
    PplsParams,
    PplsPatch
> {
    app!: any

    setup(app) {
        this.app = app;
    }

    async create(data, params:any) {
        if (params.skip_hooks) return super.create(data, params);
        if (Array.isArray(data)) {
            const results:any = [];
            const emails = data.map(a => a.email);
            const notNull = emails.filter(a => !!a)
            const matches = await this.find({query: {$limit: notNull.length, email: {$in: notNull}}, admin_pass: true, skip_hooks: true } as any)
            for (let i = matches.data.length - 1; i >= 0; i--) {
                const item = matches.data[i];
                const idx = emails.indexOf(item.email);
                if (idx > -1) {
                    results.push(item)
                    data.splice(idx, 1)
                }
            }
            return results;
        } else {
            const {email} = data;
            if (email) {
                const existing = await this.find({query: {email, $limit: 1}, admin_pass: true, skip_hooks: true } as any)
                if (existing.total) {
                    const person:any = existing.data[0];
                    if (data.comp) {
                        params._init_cam = true;
                        const pickList = ['org', 'contract', 'interval', 'estHours', 'amount', 'terms', 'name', 'class', 'extras'];
                        const cam:any = {person: person._id, hireDate: data.hireDate || new Date()}
                        for (const k of pickList) cam[k] = data.comp[k] || undefined;
                        cam.comp = data.comp._id
                        delete data.comp
                        if (data.hireDate) delete data.hireDate;
                        await this.app.service('cams').create(cam, {admin_pass: true, _init_cam: true} as any)
                            .catch(err => console.log(`Error creating new cam for existing person: ${person.name} - id: ${person._id} - ${err.message}`))
                        await this.patch(person._id, {$addToSet: {cams: cam._id}}, {admin_pass: true, skip_hooks: true} as any)
                            .catch(err => console.log(`Error adding cam to person: ${person.name} - id: ${person._id} - ${err.message}`))
                    }
                    if (!person.household && params.runJoin?.add_household) {
                        const {spouse, deps} = params.runJoin.add_household;
                        const hh = await this.app.service('households').create({person: person._id}, {admin_pass: true} as any)
                        const patchObj = {members: {}}
                        if (spouse) {
                            const sp = await this.create({...spouse, household: hh._id}, {} as any)
                                .catch(err => console.log(`Error adding spouse to household: ${err.message}`));
                            if (spouse) patchObj.members[spouse._id] = {relation: 'spouse'}
                        }
                        if (deps) {
                            const promises: any = [];
                            for (let i = 0; i < deps.length; i++) {
                                promises.push(this.create({...deps[i], household: hh._id}, {} as any)
                                    .catch(err => console.log(`Error adding dep ${i} to household ${hh._id}: ${err.message}`)))
                            }
                            await Promise.all(promises)
                            for (const d of promises) {
                                if (d?._id) patchObj.members[d._id] = {relation: 'child', dependent: true}
                            }
                        }
                        if (Object.keys(patchObj.members).length) await this.app.service('households').patch(hh._id, patchObj, {} as any)
                            .catch(err => console.log(`Error adding household members: ${err.message}`))

                    }
                    return existing.data[0]
                }
            }
        }
        return super.create(data, params)
    }
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        multi: true,
        Model: app
            .get('mongodbClient')
            .then((db) => db.collection('ppls'))
            .then((collection) => {
                collection.createIndex({email: 1}, {unique: false})
                return collection;
            })
    }
}
