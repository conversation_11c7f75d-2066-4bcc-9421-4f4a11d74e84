// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import type { Application } from '../../declarations.js'
import { PplsService, getOptions } from './ppls.class.js'
import { pplsPath, pplsMethods } from './ppls.shared.js'
import {pplHooks} from './hooks/index.js';

export * from './ppls.class.js'
export * from './ppls.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const ppls = (app: Application) => {
  // Register our service on the Feathers application
  app.use(pplsPath, new PplsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: pplsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(pplsPath).hooks(pplHooks)
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [pplsPath]: PplsService
  }
}
