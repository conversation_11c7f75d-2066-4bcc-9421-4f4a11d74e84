// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import type { Application } from '../../declarations.js'
import { OrgsService, getOptions } from './orgs.class.js'
import { orgsPath, orgsMethods } from './orgs.shared.js'
import {orgHooks} from './hooks/index.js';

export * from './orgs.class.js'
export * from './orgs.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const orgs = (app: Application) => {
  // Register our service on the Feathers application
  app.use(orgsPath, new OrgsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: orgsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(orgsPath).hooks(orgHooks)
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [orgsPath]: OrgsService
  }
}
