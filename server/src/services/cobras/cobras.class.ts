// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Cobras, CobrasData, CobrasPatch, CobrasQuery } from './cobras.schema.js'

export type { Cobras, CobrasData, CobrasPatch, CobrasQuery }

export interface CobrasParams extends MongoDBAdapterParams<CobrasQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class CobrasService<ServiceParams extends Params = CobrasParams> extends MongoDBService<
  Cobras,
  CobrasData,
  CobrasParams,
  CobrasPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('cobras'))
  }
}
