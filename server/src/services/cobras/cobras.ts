// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import { hooks as schemaHooks } from '@feathersjs/schema'

import {
  cobrasDataValidator,
  cobrasPatchValidator,
  cobrasQueryValidator,
  cobrasResolver,
  cobrasExternalResolver,
  cobrasDataResolver,
  cobrasPatchResolver,
  cobrasQueryResolver
} from './cobras.schema.js'

import type { Application } from '../../declarations.js'
import { CobrasService, getOptions } from './cobras.class.js'
import { cobrasPath, cobrasMethods } from './cobras.shared.js'

export * from './cobras.class.js'
export * from './cobras.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const cobras = (app: Application) => {
  // Register our service on the Feathers application
  app.use(cobrasPath, new CobrasService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: cobrasMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(cobrasPath).hooks({
    around: {
      all: [schemaHooks.resolveExternal(cobrasExternalResolver), schemaHooks.resolveResult(cobrasResolver)]
    },
    before: {
      all: [schemaHooks.validateQuery(cobrasQueryValidator), schemaHooks.resolveQuery(cobrasQueryResolver)],
      find: [],
      get: [],
      create: [schemaHooks.validateData(cobrasDataValidator), schemaHooks.resolveData(cobrasDataResolver)],
      patch: [schemaHooks.validateData(cobrasPatchValidator), schemaHooks.resolveData(cobrasPatchResolver)],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [cobrasPath]: CobrasService
  }
}
