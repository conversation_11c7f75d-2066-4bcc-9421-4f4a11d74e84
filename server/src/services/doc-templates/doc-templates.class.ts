// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type {
  DocTemplates,
  DocTemplatesData,
  DocTemplatesPatch,
  DocTemplatesQuery
} from './doc-templates.schema.js'

export type { DocTemplates, DocTemplatesData, DocTemplatesPatch, DocTemplatesQuery }

export interface DocTemplatesParams extends MongoDBAdapterParams<DocTemplatesQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class DocTemplatesService<ServiceParams extends Params = DocTemplatesParams> extends MongoDBService<
  DocTemplates,
  DocTemplatesData,
  DocTemplatesParams,
  DocTemplatesPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('doc-templates'))
  }
}
