// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    docTemplatesDataValidator,
    docTemplatesPatchValidator,
    docTemplatesQueryValidator,
    docTemplatesResolver,
    docTemplatesExternalResolver,
    docTemplatesDataResolver,
    docTemplatesPatchResolver,
    docTemplatesQueryResolver
} from './doc-templates.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {DocTemplatesService, getOptions} from './doc-templates.class.js'
import {docTemplatesPath, docTemplatesMethods} from './doc-templates.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, loadExists, setExists} from 'feathers-ucan';
import {logChange} from '../../utils/index.js';
import { sanitizeSections } from '../plan-docs/plan-docs.js';

export * from './doc-templates.class.js'
export * from './doc-templates.schema.js'

const authenticate = async (context: HookContext) => {
    const creator = [['doc-templates', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['doc-templates', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: creator,
        patch: creator,
        update: creator,
        remove: deleter
    };
    return await allUcanAuth<HookContext>(ucanArgs, {
        loginPass: [[['owner'], '*']],
        adminPass: ['patch', 'create', 'remove']
    })(context) as any;
}

// A configure function that registers the service and its hooks via `app.configure`
export const docTemplates = (app: Application) => {
    // Register our service on the Feathers application
    app.use(docTemplatesPath, new DocTemplatesService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: docTemplatesMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(docTemplatesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(docTemplatesExternalResolver),
                schemaHooks.resolveResult(docTemplatesResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(docTemplatesQueryValidator),
                schemaHooks.resolveQuery(docTemplatesQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(docTemplatesDataValidator),
                schemaHooks.resolveData(docTemplatesDataResolver),
                sanitizeSections,
            ],
            patch: [
                schemaHooks.validateData(docTemplatesPatchValidator),
                schemaHooks.resolveData(docTemplatesPatchResolver),
                sanitizeSections
            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations' {
    interface ServiceTypes {
        [docTemplatesPath]: DocTemplatesService
    }
}
