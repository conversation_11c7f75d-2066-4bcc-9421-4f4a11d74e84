// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type {
  CareAccounts,
  CareAccountsData,
  CareAccountsPatch,
  CareAccountsQuery
} from './care-accounts.schema.js'

export type { CareAccounts, CareAccountsData, CareAccountsPatch, CareAccountsQuery }

export interface CareAccountsParams extends MongoDBAdapterParams<CareAccountsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class CareAccountsService<ServiceParams extends Params = CareAccountsParams> extends MongoDBService<
  CareAccounts,
  CareAccountsData,
  CareAccountsParams,
  CareAccountsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('care-accounts'))
  }
}
