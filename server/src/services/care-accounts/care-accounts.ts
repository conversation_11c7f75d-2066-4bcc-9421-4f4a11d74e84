// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    careAccountsDataValidator,
    careAccountsPatchValidator,
    careAccountsQueryValidator,
    careAccountsResolver,
    careAccountsExternalResolver,
    careAccountsDataResolver,
    careAccountsPatchResolver,
    careAccountsQueryResolver
} from './care-accounts.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {CareAccountsService, getOptions} from './care-accounts.class.js'
import {careAccountsPath, careAccountsMethods} from './care-accounts.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {AnyObj, logChange, logHistory, relate, dollarString} from '../../utils/index.js';
import {cascadeMcc, looseRelateUsers} from '../budgets/hooks/index.js'
import { walletBalance} from './utils/index.js';

export * from './care-accounts.class.js'
export * from './care-accounts.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['accounts', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['accounts', '*']] as Array<CapabilityParts>;
    const ucanArgs:any = {
        get: writer,
        create: anyAuth,
        find: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = []

    if(context.method === 'create'){
        const orgNamespace = `orgs:${context.data.owner}`;
        cap_subjects.push(context.data.owner);
        const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'providerAdmin'], [orgNamespace, 'financeAdmin']]
        ucanArgs.create = []
        for (const w of orgWrite) {
            ucanArgs.create.push(w);
        }
    }
    else if (!context.params.admin_pass && !context.params.loopingAuth) {
        const existing = await loadExists(context, {params: {admin_pass: true, loopingAuth: true}});
        context = setExists(context, existing);
        //allow changes before approval
        if (existing) {
            const orgNamespace = `orgs:${existing.owner}`;
            cap_subjects.push(existing.owner);
            const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'providerAdmin'], [orgNamespace, 'financeAdmin']]
            for (const w of orgWrite) {
                ucanArgs.get.unshift(w);
                ucanArgs.patch.unshift(w);
                ucanArgs.update.unshift(w);
                ucanArgs.remove.unshift(w);
            }
            // if ((existing.managers || []).includes(context.params.login?._id)) context.params.admin_pass = true;
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['get', 'find', 'patch', 'create', 'remove'],
        loginPass: [[['managers/owner'], '*']],
        cap_subjects
    })(context) as any;
}

//TODO: this could be stronger
const limitFind = async (context: HookContext) => {
    if(!context.params._search && !context.params.skip_hooks){
        const { _id, owner } = context.params.query;
        if(_id || owner) return context;
        else await allUcanAuth({ find: [['accounts', 'READ']]}, { adminPass: ['get', 'find', 'patch', 'create', 'remove'],
            loginPass: [[['managers/owner'], '*']]})(context)
    }
    return context;
}

const relateOwner = async (context: HookContext) => {
    return relate('otm', {
        herePath: 'owner',
        therePath: 'careAccounts',
        thereService: 'orgs'
    })(context);
}

const sendPing = (recipient: string, config: any) => {
    return async (context: HookContext) => {
        return await new CoreCall('pings', context).create({
            recipient,
            recipientService: 'ppls',
            priority: 1,
            category: 'CareAccounts',
            message: 'Your Plan Wallet has been frozen due to committed balances exceeding actual balance.',
            link: 'https://admin.commoncare.org/banking',
            methods: {
                email: {send: true},
                internal: {send: true}
            },
            ...config
        })
            .catch(e => console.error(`Error sending ping in care account balance notification. Care account id ${context.id}: ${e.message}`))
    }
}


const setAccount = (acct:any, path:string) => {
    return (context: HookContext) => {
        context.params.banking = {
            ...context.params.banking,
            moov: {...context.params.banking?.moov, [path]: acct}
        }
        return context;
    }
}
const createFinancialAccount = async (context: HookContext) => {
    if(context.method === 'create' || context.data.$set?.treasury?.id === 'setup') {
        if(context.method !== 'create'){
            const ex = await loadExists(context);
            context = setExists(context, ex);
            if(ex?.treasury?.id){
                /** Make sure no existing moov account is already present */
                const acct = await new CoreCall('banking', context).get(ex.treasury.id, {banking: {moov: { method: 'account_get', args: [ex.treasury.id]}}})
                    .catch(err => console.log(`Error fetching moov account to check before setting up new moov account for existing care account: ${err.message}`))
                if(acct) {
                    context.result = ex;
                    return context;
                }
            }
        }
        const acct = await new CoreCall('banking', context).get(context.data.moov_id || '1', {
            banking: {
                moov: {
                    method: 'accounts_create',
                    args: [context.data.owner, context.params.banking?.account_data]
                }
            }
        })
            .catch(err => {
                console.error(`Error creating financial account on care account create: ${err.message}`);
                throw new Error(`Error creating account: ${err.message}`);
            });
        delete context.data.$set
        context = setAccount(acct, 'account')(context);
        context.data.moov_id = acct.accountID;
        context.data.status = acct.verification?.status;
        context.data.statusNote = acct.verification.details;
        const wallets = await new CoreCall('banking', context).get(acct.accountID, { banking: { moov: { method: 'list_wallets', args: [] }}})
            .catch(err => {
                console.log(`Error listing wallets for care account with moov_id ${acct.accountID}: ${err.message}`);
            })
        if(wallets?.data?.length) context.data.wallet_id = wallets.data[0].walletID;
    }
    // context.data.last4 = (acct.financial_addresses || [])[0]?.aba?.account_number_last4
    return context;
}

type JoinAccountOptions = { returnAccount?:boolean, throw?:boolean }
const joinWallet = (val: any, options?:JoinAccountOptions) => {
    return async (context: HookContext) => {
        if (!val?.moov_id) return val;
        const wallet = await new CoreCall('banking', context).get(val.moov_id, {
            banking: {
                moov: {
                    method: 'get_wallet',
                    args: [val.moov_id, val.wallet_id]
                }
            }
        })
            .catch(err => {
                console.log(`Error joining wallet to care account: ${err.message}`)
                if(options?.throw) throw new Error(`Error joining wallet to care account: ${err.message}`);
                return
            })
        if (val) val._fastjoin = {...val._fastjoin, wallet}
        return options?.returnAccount ? wallet : val;
    }
}

const joinAccount = (val:any, options?:JoinAccountOptions) => {
    return async (context: HookContext) => {
        if (!val?.moov_id) return val;
        const account = await new CoreCall('banking', context).get(val.moov_id, {
            banking: {
                moov: {
                    method: 'get_account',
                    args: [val.moov_id]
                }
            }
        })
            .catch(err => {
                console.log(`Error joining moov account to care account: ${err.message}`)
                if(options?.throw) throw new Error(`Error joining account wallet to care account: ${err.message}`);
                return undefined;
            })
        if (val) val._fastjoin = {...val._fastjoin, account}
        return options?.returnAccount ? account : val;
    }
}
const checkInc = async (context: HookContext) => {
    let {assigned_amount, assigned_recurs} = context.data.$inc || {}
    //for a sub budget, there is no need to update the assigned_amount, only to pass it along in params to make sure the budget integrity of funds is there.
    const params_assigned_amount = context.params.assigned_amount;
    const params_assigned_recurs = context.params.assigned_recurs;
    if ((assigned_amount || assigned_recurs || params_assigned_amount || params_assigned_recurs) && !context.params.banking?.syncing_balances) {
        const existing: any = await loadExists(context);
        context = setExists(context, existing);
        let wallet = existing._fastjoin?.wallet;
        if (!wallet && (assigned_amount || params_assigned_amount)) {
            wallet = await joinWallet(existing, {returnAccount: true})(context);
            context = setAccount(wallet, 'wallet')(context);
        }
        const balance = wallet ? walletBalance(wallet) : 0;
        const checkBalance = assigned_amount ? assigned_amount + (existing.assigned_amount || 0) : params_assigned_amount || 0
        if (checkBalance > balance) throw new Error(`Financial account balance is insufficient to add ${dollarString((assigned_amount || 0) / 100, '$', 2)} in commited budgets`);
        else if ((assigned_recurs ? (assigned_recurs || 0) + (existing.assigned_recurs || 0) : (params_assigned_recurs || 0)) > existing.recurs) {
            await Promise.all((existing.managers || []).map(a => sendPing(a, {
                message: `Assigned recurring budgets exceed recurring target Plan Wallet budget for account ${existing.name}`,
                priority: 2
            })))
        } else if (assigned_amount + existing.assigned_amount > existing.amount) {
            await Promise.all((existing.managers || []).map(a => sendPing(a, {
                message: `Assigned budget amounts exceed target Plan Wallet budget for account ${existing.name}. Your account balance is sufficient to cover budgets, but target balance is exceeded`,
                priority: 2
            })))
        }
    }
    return context;
}

//TODO: need to actually renew budget at monthly recurs
const syncTotals = (service: 'budgets' | 'care-accounts', result: AnyObj) => {
    return async (context: HookContext) => {
        const handleError = async (err, message) => {
            console.error(`Error cascading amount | ${context.id} | ${message} | ${err.message}`)
            await new CoreCall(context.path, context)._patch(context.id as any, {runSync: new Date()}, { skip_hooks: true, admin_pass: true})
            throw new Error(err.message)
        }
        //get path for children
        const paths = {'budgets': 'children', 'care-accounts': 'budgets'};
        const childrenPath = paths[service]
        if ((result || {})[childrenPath]) {
            //load children
            const childrenRecords = await new CoreCall('budgets', context)._find({
                paginate: false,
                skip_hooks: true, admin_pass: true,
                query: {
                    $select: ['amount', 'recurs'],
                    _id: {$in: result[childrenPath] || []},
                    deleted: { $ne: true }
                }
            })
                .catch(err => handleError(err, 'Could not find children budgets'))
            //run next layer only valid if on care-account since 2 layer max depth and bottom layer budget do not have children to sync - so you only need to run once to sync the middle layer budgets
            if (service === 'care-accounts') {
                await Promise.all(childrenRecords.map(a => syncTotals('budgets', a)(context)))
            }
            let amt = 0;
            let rcr = 0;
            //total all sub amounts and recurs
            for (let i = 0; i < childrenRecords.total; i++) {
                const {amount = 0, recurs = 0, children = []} = childrenRecords.data[i]
                amt += amount;
                rcr += recurs;
            }

            //see if either assigned or actual amounts are exceeded
            const adjustAssignedAmount = amt - (result.assigned_amount || 0);
            const adjustAssignedRecurs = rcr - (result.assigned_recurs || 0);
            const adjustAmount = amt - (result.amount || 0);
            const adjustRecurs = rcr - (result.recurs || 0);
            if (adjustAssignedAmount || adjustAssignedRecurs || adjustAmount > 0 || adjustRecurs > 0) {
                let balance = result.amount || 0;
                if (service === 'care-accounts') {
                    let wallet = result._fastjoin?.wallet;
                    if (!wallet) {
                        wallet = await joinWallet(result, { returnAccount: true })(context);
                        result._fastjoin = {...result._fastjoin, wallet}
                    }
                    balance = walletBalance(wallet)
                }

                //prepare status object
                const lastSync: any = {
                    adjusted: true,
                    adjust_assigned_amount: adjustAssignedAmount,
                    adjust_assigned_recurs: adjustAssignedRecurs,
                    adjust_amount: adjustAmount,
                    adjust_recurs: adjustRecurs,
                    freeze: false,
                    date: new Date(),
                    by: context.params.login?._id,
                    balance,
                    amount: amt,
                    recurs: rcr
                }

                if (balance < (amt || 0)) {
                    lastSync.freeze = true;
                    lastSync.excess = amt - balance;
                } else if (result.amount < (amt || 0)) {
                    lastSync.excess = amt - balance;
                }
                //patch all children records if changes are needed
                const patchObj: any = {lastSync, $unset: {runSync: ''}};

                //push syncHistory and limit array length to 100

                //TODO: may need to keep more than 100 or log the history somewhere
                if (result.lastSync) patchObj.$push = {
                    syncHistory: {
                        $each: [result.lastSync],
                        $sort: {date: -1},
                        $slice: -100
                    }
                };
                context.result = await new CoreCall(service, context).patch(result._id as any, patchObj, {
                    admin_pass: true,
                    banking: {syncing_balances: true},
                    syncing_accounts: true
                })
                    .catch(err => handleError(err, `Could not update ${service} in syncing totals`))
            } else if (result.lastSync?.freeze) {
                let balance = result.amount || 0;
                if (service === 'care-accounts') {
                    let wallet = result._fastjoin?.wallet;
                    if (!wallet) {
                        wallet = await joinWallet(result, { returnAccount: true })(context);
                        result._fastjoin = {...result._fastjoin, wallet}
                    }
                    balance = walletBalance(wallet)
                }
                const lastSync: any = {
                    adjusted: false,
                    adjust_assigned_amount: adjustAssignedAmount,
                    adjust_assigned_recurs: adjustAssignedRecurs,
                    adjust_amount: adjustAmount,
                    adjust_recurs: adjustRecurs,
                    freeze: false,
                    err: false,
                    date: new Date(),
                    by: context.params.login?._id,
                    balance,
                    amount: amt,
                    recurs: rcr
                }
                const patchObj: any = {
                    lastSync,
                    $unset: {runSync: ''},
                    $push: {syncHistory: {$each: [result.lastSync], $sort: {date: -1}, $slice: -100}}
                };
                //push syncHistory and limit array length to 100

                //TODO: may need to keep more than 100 or log the history somewhere
                context.result = await new CoreCall(service, context).patch(result._id as any, patchObj, {
                    admin_pass: true,
                    banking: {syncing_balances: true},
                    syncing_accounts: true
                })
                    .catch(err => {
                        handleError(err, `Could not update ${service} in syncing totals`)
                    })
            }
        }
        return context;
    }
}

//TODO: consider syncing on avery amount $inc
const syncAccount = async (context: HookContext) => {
    if (context.params.banking?.sync_budgets) {
        if (context.type === 'before') {
            const existing = await loadExists(context);
            context = setExists(context, existing);
            const wallet = await new CoreCall('banking', context).get(existing.moov_id, {
                banking: {
                    moov: {
                        method: 'get_wallet',
                        args: [existing.moov_id, existing.wallet_id]
                    }
                }
            })
            context.params.banking = {...context.params.banking, stripe: {wallet: wallet}}
            context.data.amount = wallet?.balance?.cash?.usd || 0
        } else {
            context = await syncTotals('care-accounts', context.result)(context);
            if (context.result?.lastSync?.freeze) {
                await Promise.all(context.result.managers.map(m => sendPing(m, {
                    priority: 1,
                    message: `CareAccount ${context.result.name} is frozen due to budget commitments exceeding account balance\``
                })(context)))
            } else if (context.result?.lastSync?.excess) {
                await Promise.all(context.result.managers.map(m => sendPing(m, {
                    priority: 2,
                    message: `CareAccount ${context.result.name} ending in ${context.result.last4} has committed more than its target balance to budgets\``
                })(context)))
            }
        }
    }
    return context;
}


const addManagers = async (context: HookContext) => {
    if (!context.data.managers) {
        context.data.managers = [context.params.login.owner]
    }
    return context;
}

const runJoins = async (context: HookContext) => {
    const {runJoin} = context.params;
    if (runJoin?.with_wallet) {
        if (context.method === 'find') {
            context.result.data = await Promise.all(context.result.data.map(a => joinAccount(a)(context)))
        } else {
            let account = context.params.banking?.moov?.account
            if (account) context.result._fastjoin = {...context.result_fastjoin, account}
            else context.result = await joinAccount(context.result)(context);
            const acctStatus = context.result._fastjoin?.account?.verification?.status
            if(acctStatus && context.result.status !== acctStatus) {
                context.result.status = acctStatus;
                await new CoreCall('care-accounts', context, { skipJoins: true}).patch(context.result._id, { status: acctStatus })
            }
            let wallet = context.result._fastjoin?.wallet
            if(!wallet) {
                context.result = await joinWallet(context.result)(context)
                wallet = context.result._fastjoin?.wallet
            }
            const balance = walletBalance(wallet);
            if (!context.params.syncing_accounts && balance < context.result.assigned_amount || 0) {
                await syncAccount(context)
            }
        }
    }
    return context
}

export const checkFreeze = (service: 'care-accounts' | 'budgets') => {
    return async (context: HookContext) => {
        if (!context.params.syncing_accounts && context.result.lastSync?.freeze && context.params.banking?.sync_budgets) {
            context = await syncTotals(service, context.result)(context);
        } else if (!context.params.syncing_accounts && context.result.runSync) {
            if (new Date().getTime() - new Date(context.result.runSync).getTime() > 1000 * 60) {
                if (service === 'care-accounts') context = await syncTotals(service, context.result)(context)
                else {
                    const ca = await new CoreCall('care-accounts', context).get(context.result.careAccount, { syncing_accounts: true })
                    if (ca) context = await syncTotals('care-accounts', ca)(context)
                }
            }
        }
        return context;
    }
}

const checkWallet = async (context: HookContext) => {
    if (!context.params.skip_joins) {
        const wallet_id = context.result.wallet_id;
        if (wallet_id) return context;
        const wallets = await new CoreCall('banking', context).get(context.result.moov_id, { banking: { moov: { method: 'list_wallets', args: [] }}})
            .catch(err => {
                console.log(`Error adding wallet for care account: ${context.result._id} with moov_id ${context.result.accountID}: ${err.message}`);
            })
        if(wallets?.wallets?.length) {
            context.result = await new CoreCall('care-accounts', context).patch(context.result._id, { wallet_id: wallets.walllets[0].walletID }, { admin_pass: true, skip_hooks: true })
        }

    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const careAccounts = (app: Application) => {
    // Register our service on the Feathers application
    app.use(careAccountsPath, new CareAccountsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: careAccountsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(careAccountsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(careAccountsExternalResolver),
                schemaHooks.resolveResult(careAccountsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(careAccountsQueryValidator),
                schemaHooks.resolveQuery(careAccountsQueryResolver)
            ],
            find: [limitFind],
            get: [],
            create: [
                schemaHooks.validateData(careAccountsDataValidator),
                schemaHooks.resolveData(careAccountsDataResolver),
                addManagers,
                createFinancialAccount,
                relateOwner
            ],
            patch: [
                createFinancialAccount,
                schemaHooks.validateData(careAccountsPatchValidator),
                schemaHooks.resolveData(careAccountsPatchResolver),
                checkInc,
                syncAccount,
                cascadeMcc('care-accounts'),
                relateOwner,
                logHistory(['managers', 'approvers', 'members', 'budgets'])
            ],
            remove: [relateOwner]
        },
        after: {
            all: [runJoins],
            get: [
                checkFreeze('care-accounts'),
                checkWallet
            ],
            create: [looseRelateUsers('care_account_user'), relateOwner],
            patch: [
                syncAccount,
                looseRelateUsers('care_account_user'),
                relateOwner,
                checkFreeze('care-accounts')
            ],
            remove: [looseRelateUsers('care_account_user'), relateOwner],
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [careAccountsPath]: CareAccountsService
    }
}
