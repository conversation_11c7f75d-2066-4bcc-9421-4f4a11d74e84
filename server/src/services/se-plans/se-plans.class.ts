// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { SePlans, SePlansData, SePlansPatch, SePlansQuery } from './se-plans.schema.js'

export type { SePlans, SePlansData, SePlansPatch, SePlansQuery }

export interface SePlansParams extends MongoDBAdapterParams<SePlansQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class SePlansService<ServiceParams extends Params = SePlansParams> extends MongoDBService<
  SePlans,
  SePlansData,
  SePlansParams,
  SePlansPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('se-plans'))
        .then((collection) => {
          collection.createIndex({state_plan_id: 1}, {unique: true})
          collection.createIndex({plan_id: 1}, {unique: true})
          collection.createIndex({first_3_zips: 1})
          return collection;
        }),
    operators: ['$regex', '$options']
  }
}
