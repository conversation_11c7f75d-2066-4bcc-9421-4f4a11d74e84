// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {Budgets, BudgetsData, BudgetsPatch, BudgetsQuery} from './budgets.schema.js'

export type {Budgets, BudgetsData, BudgetsPatch, BudgetsQuery}

export interface BudgetsParams extends MongoDBAdapterParams<BudgetsQuery> {
}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class BudgetsService<ServiceParams extends Params = BudgetsParams> extends MongoDBService<
    Budgets,
    BudgetsData,
    BudgetsParams,
    BudgetsPatch
> {
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        Model: app.get('mongodbClient').then((db) => db.collection('budgets'))
            .then((collection) => {
                collection.createIndex({parent: 1})
                return collection;
            }),
        operators: ['$regex', '$options']
    }
}
