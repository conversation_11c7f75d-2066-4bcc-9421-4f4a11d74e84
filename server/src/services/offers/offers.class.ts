// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Offers, OffersData, OffersPatch, OffersQuery } from './offers.schema.js'

export type { Offers, OffersData, OffersPatch, OffersQuery }

export interface OffersParams extends MongoDBAdapterParams<OffersQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class OffersService<ServiceParams extends Params = OffersParams> extends MongoDBService<
  Offers,
  OffersData,
  OffersParams,
  OffersPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('offers'))
  }
}
