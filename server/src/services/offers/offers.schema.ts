// TypeBox schema for offers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const offersSchema = Type.Object({
  _id: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  coverage: Type.Optional(ObjectIdSchema()),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  price: Type.Optional(Type.Number()),
  originalPrice: Type.Optional(Type.Number()),
  discount: Type.Optional(Type.Number()),
  discountType: Type.Optional(Type.String()),
  validFrom: Type.Optional(Type.Any()),
  validTo: Type.Optional(Type.Any()),
  conditions: Type.Optional(Type.Array(Type.String())),
  eligibility: Type.Optional(Type.Record(Type.String(), Type.Any())),
  maxUses: Type.Optional(Type.Number()),
  currentUses: Type.Optional(Type.Number()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
,
  // Missing fields from old schema
  contract: Type.Optional(Type.String()),
  role: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Offers = Static<typeof offersSchema>
export const offersValidator = getValidator(offersSchema, dataValidator)
export const offersResolver = resolve<Offers, HookContext>({})
export const offersExternalResolver = resolve<Offers, HookContext>({})

export const offersDataSchema = Type.Object({
  ...Type.Omit(offersSchema, ['_id']).properties
}, { additionalProperties: false })

export type OffersData = Static<typeof offersDataSchema>
export const offersDataValidator = getValidator(offersDataSchema, dataValidator)
export const offersDataResolver = resolve<OffersData, HookContext>({})

export const offersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(offersSchema, ['_id'])).properties
}, { additionalProperties: false })
export type OffersPatch = Static<typeof offersPatchSchema>
export const offersPatchValidator = getValidator(offersPatchSchema, dataValidator)
export const offersPatchResolver = resolve<OffersPatch, HookContext>({})

// Allow querying on any field from the main schema
const offersQueryProperties = offersSchema
export const offersQuerySchema = querySyntax(offersQueryProperties)
export type OffersQuery = Static<typeof offersQuerySchema>
export const offersQueryValidator = getValidator(offersQuerySchema, queryValidator)
export const offersQueryResolver = resolve<OffersQuery, HookContext>({})
