// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    offersDataValidator,
    offersPatchValidator,
    offersQueryValidator,
    offersResolver,
    offersExternalResolver,
    offersDataResolver,
    offersPatchResolver,
    offersQueryResolver
} from './offers.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {OffersService, getOptions} from './offers.class.js'
import {offersPath, offersMethods} from './offers.shared.js'
import {allUcanAuth, CapabilityParts, loadExists, setExists, anyAuth, CoreCall} from 'feathers-ucan'
import {getJoin, logChange} from '../../utils/index.js';

export * from './offers.class.js'
export * from './offers.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['offers', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['offers', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = []
    if (!['get', 'find'].includes(context.method)) {
        let hostId = context.data.host;
        let planId = context.data.plan;
        if (context.method !== 'create') {

            const existing = await loadExists(context, {});
            context = setExists(context, existing);
            hostId = existing?.host;
            planId = existing?.plan;
        }
        let host = hostId ? await new CoreCall('hosts', context).get(hostId) : {org: undefined}
        let plan = planId ? await new CoreCall('plans', context).get(planId) : {};
        if (!plan) plan = {};
        if (!host) host = {}
        //TODO: this assumes only an org makes an offer to a plan
        const hostP = `orgs:${host.org}`;
        const planP = `plans:${planId}`;
        cap_subjects.push(host.org);
        cap_subjects.push(planId);
        cap_subjects.push(host._id);
        cap_subjects.push(plan.org);
        const orgWrite: CapabilityParts[] = [
            [hostP, 'WRITE'],
            [hostP, 'providerAdmin'],
            [hostP, 'orgAdmin'],
            [`hosts:${host._id}`, 'hostAdmin'],
            [planP, 'WRITE'],
            [planP, 'planAdmin'],
            [`orgs:${plan.org}`, 'orgAdmin'],
        ]
        for (const w of orgWrite) {
            ucanArgs.patch.unshift(w);
            ucanArgs.update.unshift(w);
            ucanArgs.remove.unshift(w);
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        specialChange: context.params.special_change || undefined,
        cap_subjects,
        or: '*'
    })(context) as any;
}


const runJoins = async (context: HookContext): Promise<HookContext> => {
    const {offer_plan, offer_host} = context.params.runJoin || {}
    if (offer_plan) context = await getJoin({
        service: 'plans',
        herePath: 'plan'
    })(context);
    if (offer_host) context = await getJoin({
        service: 'hosts',
        herePath: 'host'
    })(context);
    return context;
}

const handleApproval = async (context: HookContext): Promise<HookContext> => {
    const status = context.data.status || context.data.$set?.status;
    if (status === 'active') {
        const ex = await loadExists(context);
        context = setExists(context, ex);
        if (ex.status !== 'active') {
            await new CoreCall('plans', context).patch(ex.plan, {
                $set: {
                    [`team.${ex.host}`]: {
                        id: ex.host,
                        role: ex.role,
                        fee: ex.fee,
                        feeType: ex.feeType,
                        approvedBy: context.params.login._id,
                        approvedAt: new Date(),
                        contract: ex.contract
                    }
                }
            })
        }
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const offers = (app: Application) => {
    // Register our service on the Feathers application
    app.use(offersPath, new OffersService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: offersMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(offersPath).hooks({
        around: {
            all: [schemaHooks.resolveExternal(offersExternalResolver), schemaHooks.resolveResult(offersResolver)]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(offersQueryValidator),
                schemaHooks.resolveQuery(offersQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(offersDataValidator),
                schemaHooks.resolveData(offersDataResolver)
            ],
            patch: [
                schemaHooks.validateData(offersPatchValidator),
                schemaHooks.resolveData(offersPatchResolver),
                handleApproval
            ],
            remove: []
        },
        after: {
            all: [
                runJoins
            ]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [offersPath]: OffersService
    }
}
