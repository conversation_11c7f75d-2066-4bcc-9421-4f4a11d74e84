// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { AiChats, AiChatsData, AiChatsPatch, AiChatsQuery } from './ai-chats.schema.js'

export type { AiChats, AiChatsData, AiChatsPatch, AiChatsQuery }

export interface AiChatsParams extends MongoDBAdapterParams<AiChatsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class AiChatsService<ServiceParams extends Params = AiChatsParams> extends MongoDBService<
  AiChats,
  AiChatsData,
  AiChatsParams,
  AiChatsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('ai-chats'))
        .then((collection) => {
          collection.createIndex({chatId: 1}, {unique: true});
          return collection
        }),
  }
}
