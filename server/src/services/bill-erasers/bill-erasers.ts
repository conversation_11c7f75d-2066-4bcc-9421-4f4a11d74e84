// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    billErasersDataValidator,
    billErasersPatchValidator,
    billErasersQueryValidator,
    billErasersResolver,
    billErasersExternalResolver,
    billErasersDataResolver,
    billErasersPatchResolver,
    billErasersQueryResolver
} from './bill-erasers.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {BillErasersService, getOptions} from './bill-erasers.class.js'
import {billErasersPath, billErasersMethods} from './bill-erasers.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, loadExists, noThrow, setExists} from 'feathers-ucan';
import {logChange} from '../../utils/index.js';

export * from './bill-erasers.class.js'
export * from './bill-erasers.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['bill-erasers', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['bill-erasers', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        create: noThrow,
        patch: writer,
        update: writer,
        remove: deleter
    };

    if (context.method === 'patch') {
        if (context.params.runJoin?.add_files) ucanArgs.patch = noThrow
        else {
            const existing = await loadExists(context);
            context = setExists(context, existing);
            if (!existing?.updatedBy?.login) {
                ucanArgs.patch = noThrow
            } else if (existing.person) {
                const prsn = await new CoreCall('ppls', context).get(existing.person)
                if (!prsn?.login) ucanArgs.patch = noThrow
            } else ucanArgs.patch = noThrow
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove']
    })(context)
}

import {getFileSchema} from '../uploads/utils/index.js';

const addFiles = async (context: HookContext) => {
    const {add_files} = context.params.runJoin || {};
    if (add_files) {
        const promises: any = [];
        for (let i = 0; i < context.params.files.length; i++) {
            const {name, size, type, mimetype, lastModifiedDate, originalname} = context.params.files[i];
            const info = {name: name || originalname, size, type: type || mimetype, lastModifiedDate};
            for (const k in info) {
                if (!info[k]) delete info[k];
            }
            const data: any = {info}
            if (originalname) data.originalname = originalname
            promises.push(new CoreCall('uploads', context).create(data, {
                allow_upload: true,
                admin_pass: true,
                file: context.params.files[i]
            })
                .catch(err => {
                    console.log(`Failed to upload file in bill erasers: ${err.message}`)
                }))
        }
        const addedFiles = await Promise.all(promises);
        context.data.files = addedFiles.filter(a => !!a._id).map(a => getFileSchema(a));
        context.params.query = {}
    }
    return context;
}

import { drugUpload, getRxFile } from './utils/rx.js';

import {autoBillErase} from './utils/bill-upload.js';
import multer from 'multer';

const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
   if(req.query.runJoin?.singleFile) await new Promise((resolve) => multipartMiddleware.single('file')(req, res, resolve));
   else  await new Promise((resolve) => multipartMiddleware.array('files', 5)(req, res, resolve));
    req.feathers.files = req.files;
    if(!req.files?.length) {
        req.feathers.file = req.file;
        req.feathers.files = [req.file];
    }
    req.feathers.runJoin = req.query.runJoin;
    req.feathers.core = req.query.core
    return next();
}
// A configure function that registers the service and its hooks via `app.configure`
export const billErasers = (app: Application) => {
    // Register our service on the Feathers application
    app.use(billErasersPath,
        restMiddleware,
        new BillErasersService(getOptions(app)), {
            // A list of all methods this service exposes externally
            methods: billErasersMethods,
            // You can add additional custom events to be sent to clients here
            events: []
        })
    // Initialize hooks
    app.service(billErasersPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(billErasersExternalResolver),
                schemaHooks.resolveResult(billErasersResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(billErasersQueryValidator),
                schemaHooks.resolveQuery(billErasersQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                autoBillErase,
                drugUpload,
                async context => {
                    if (!context.result) {
                        await schemaHooks.validateData(billErasersDataValidator)(context)
                        await schemaHooks.resolveData(billErasersDataResolver)(context);
                    }
                    return context;
                },
                schemaHooks.validateData(billErasersDataValidator),
                schemaHooks.resolveData(billErasersDataResolver)
            ],
            patch: [
                addFiles,
                drugUpload,
                schemaHooks.validateData(billErasersPatchValidator),
                schemaHooks.resolveData(billErasersPatchResolver),
            ],
            remove: []
        },
        after: {
            all: [getRxFile],
            create: [drugUpload],
            patch: [drugUpload]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [billErasersPath]: any
    }
}
