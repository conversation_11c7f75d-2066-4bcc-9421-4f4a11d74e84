// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Bill<PERSON>rase<PERSON>, BillErasersData, BillErasersPatch, BillErasersQuery } from './bill-erasers.schema.js'

export type { BillErasers, BillErasersData, BillErasersPatch, BillErasersQuery }

export interface BillErasersParams extends MongoDBAdapterParams<BillErasersQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class BillErasersService<ServiceParams extends Params = BillErasersParams> extends MongoDBService<
  BillErasers,
  BillErasersData,
  Bill<PERSON>rasersParams,
  BillErasersPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('bill-erasers'))
  }
}
