import xlsx from 'node-xlsx';
import {getFileSchema} from '../../uploads/utils/index.js';
import {HookContext} from '../../../declarations.js';
import {CoreCall} from 'feathers-ucan';
import {getRelatedNdcs} from '../../meds/meds.js';
import {_flatten, _get} from '../../../utils/index.js';

const headers: { [key: string]: number } = {
    'ndc': 0,
    'price': 1,
    'name': 2,
    's_f': 3,
    'description': 4,
    // 'providerName': 5,
    // 'rxcui': 6,
    // 'code': 7
}
const format = {
    'ndc': (val) => {
        if (!val) return;

        val = String(val).replace(/\D/g, ''); // Remove any non-numeric characters
        const l = val.length;
        if (l < 11) {
            // Pad to 10 digits if requested format is NDC 10
            val = val.padStart(11, '0');
        }
        return val;
    },
    'price': (val) => {
        if (!val) return ''
        return Number(val.replace(/[^\d.]/g, '') || 0) || undefined;
    },
    'name': (val) => val || '',
    's_f': (val) => val || '',
    'description': (val) => val || '',
    // 'providerName': (val) => val || '',


}

/**
 headers - which I have namespaced selected_headers are for csv files where certain headers have been selected from the data. Other columns are replaced with blank values according to the official headers above. For data manually input headers should be passed to match the above standard headers
 */
export const drugUpload = async (context: HookContext) => {
    const {drug_upload} = context.params.runJoin || {};
    if (drug_upload) {
        const {headers: selected_headers} = drug_upload;

        if (context.type === 'before') {

            /** set an acceptable create/patch object to pass validation */
            const obj: any = {};
            for (const k of ['createdAt', 'updatedAt', 'createdBy', 'updatedBy', 'updatedByHistory', 'host', 'ref']) {
                if (context.data[k]) obj[k] = context.data[k]
            }
            context.data = obj;
            /**
             *
             * @param sheet single csv file format data - parsed any[][]
             * @param startIdx if first row needed to be omitted - 1 else 0
             */
            const setQueries = (sheet: any[][], startIdx: 0 | 1) => {
                const ndcs: string[] = [];
                const products: string[] = [];

                for (let i = startIdx; i < sheet.length; i++) {
                    const ndc = format.ndc(sheet[i][selected_headers.ndc])
                    if (ndc) ndcs.push(ndc);
                    if (ndc.length === 11) products.push(ndc.substring(6, 10))
                }
                context.params.drug_queries = {products, ndcs, sheet: sheet.slice(startIdx)};
            }

            /** parse either the uploaded or entered data */
            if (context.params.files) {
                if (drug_upload.sheet) {
                    const sheets = xlsx.parse(context.params.files[0].buffer)
                    const sheet = sheets.filter(a => a.name === drug_upload.sheet)[0]
                    setQueries(sheet.data, drug_upload.omitFirstRow ? 1 : 0);

                }
            } else if (drug_upload.csvData) setQueries(drug_upload.csvData, 0)
        } else {

            /** function for adding the final formatted data as an upload file on the bill-eraser. Remove previous file versions */
            const uploadCsv = async (buffer) => {
                if (context.result.files) {
                    for (let i = 0; i < context.result.files.length; i++) {
                        if (context.result.files[i].uploadId) await new CoreCall('uploads', context).remove(context.result.files[i].uploadId)
                    }
                }
                const name = 'rx_upload.csv'
                const info = {
                    lastModifiedDate: new Date(),
                    name,
                    type: 'text/csv',
                    size: buffer.length
                }
                return await new CoreCall('uploads', context).create({info}, {
                    allow_upload: true,
                    admin_pass: true,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    createdBy: {
                        login: _get(context.params, 'login._id', undefined) || undefined,
                        fingerprint: _get(context.params, 'core.fp', undefined) || undefined,
                        origin: _get(context.params, 'originalOrigin', undefined) || undefined,
                        longtail: _get(context.params, 'core.ltail', undefined) || undefined
                    },
                    file: {
                        originalname: name,
                        buffer
                    }
                })
            }


            /** take the parsed ndc's from the data and run a query for exact and possible matches in price database */
            const {ndcs, sheet} = context.params.drug_queries;
            const query: any = {
                price: {$gt: 0},
                source: {$nin: ['vision', 'bill', 'upload']}
            }
            const matches = await new CoreCall('prices', context)._find({
                paginate: false,
                skip_hooks: true, admin_pass: true,
                pipeline: [
                    {
                        $sort: {ndc: 1, price: 1}
                    },
                    {
                        $facet: {
                            exact_match: [
                                {
                                    $match: {
                                        ndc11: {$in: ndcs},
                                        ...query
                                    }
                                },
                                {
                                    $sort: {ndc: 1, price: 1}
                                },
                                {
                                    $group: {
                                        _id: "$ndc",
                                        prices: {
                                            $push: {
                                                _id: "$_id",
                                                price: "$price",
                                                ndc: "$ndc",
                                                name: '$name',
                                                s_f: '$s_f',
                                                description: '$description'
                                            }
                                        },
                                        best: {"$min": "$price"}
                                    }
                                },
                                {
                                    $project: {
                                        _id: 0,
                                        ndc: "$_id",
                                        prices: {$slice: ["$prices", 3]},
                                        best: "$best"
                                    }
                                }
                            ],
                            close_match: [
                                {
                                    $match: {
                                        ndc: {$exists: true, $nin: ndcs },
                                        ndcs: {$in: ndcs},
                                        ...query
                                    }
                                },
                                {
                                    $sort: {ndc: 1, price: 1}
                                },
                                {
                                    $group: {
                                        _id: "$ndc",
                                        prices: {
                                            $push: {
                                                _id: "$_id",
                                                price: "$price",
                                                ndc: "$ndc",
                                                name: '$name',
                                                s_f: '$s_f',
                                                description: '$description'
                                            }
                                        },
                                        ndcs: {$addToSet: '$ndcs'},
                                        best: {$min: "$price"}
                                    }
                                },
                                {
                                    $project: {
                                        _id: 0,
                                        ndc: "$_id",
                                        prices: {$slice: ["$prices", 3]},
                                        best: "$best"
                                    }
                                }
                            ]
                        }
                    }
                ]
            })

            const exactById: any = {};
            for (let i = 0; i < matches[0].exact_match?.length || 0; i++) {
                exactById[matches[0].exact_match[i].ndc] = matches[0].exact_match[i]
            }
            const closeById: any = {};
            for (let i = 0; i < matches[0].close_match?.length || 0; i++) {
                const ndc1 = matches[0].close_match[i].ndc || '*'
                closeById[ndc1] = [matches[0].close_match[i], ...closeById[ndc1] || []]
                /** Add all loose matches as well using ndcs */
                for (const ndc of matches[0].close_match[i].ndcs || []) {
                    closeById[ndc] = [...closeById[ndc], matches[0].close_match[i] || []]
                }
            }


            let upload;
            let buffer;
            /** re-access the sheet from parsing the upload, add to it, save the file */
            let sheetLength = 0;
            const newSheet: any[][] = [[]];
            if (sheet) {
                sheetLength = sheet.length;


                let original_price = 0;
                let est_price = 0;
                const sortedHeaders = Object.keys(headers).sort((a, b) => headers[a] - headers[b])
                /** re-set first row according to headers settings */
                for (const k of sortedHeaders) {
                    newSheet[0].push(k)
                }
                /** initialize new sheet with old headers + added headers */
                newSheet[0].push('Best Price')
                for (const k of [1, 2, 3]) {
                    newSheet[0].push(`Exact Match: ${k} id`)
                    newSheet[0].push(`Exact Match: ${k} price`)
                }
                for (const k of [1, 2, 3]) {
                    newSheet[0].push(`Close Match: ${k} id`)
                    newSheet[0].push(`Close Match: ${k} price`)
                }

                /** Loop through sheet and add pricing data so we can save the file for large datasets and return it to the client for small datasets */
                for (let i = 0; i < sheetLength; i++) {
                    const newRow: Array<any> = [];
                    /** set empty cells for each header value if no value exists or no header was selected */
                    for (const k of sortedHeaders) {
                        if (selected_headers[k] || selected_headers[k] === 0) newRow.push(format[k](sheet[i][selected_headers[k]] || ''))
                        else newRow.push('')
                    }
                    const ndc:any = newRow[headers.ndc];

                    // Initialize columns with default empty values
                    let bestPrice = 0;
                    let exactMatchCols: any = ['', '', '', '', '', ''];  // Three pairs of ID + price
                    let closeMatchCols: any = ['', '', '', '', '', ''];
                    if (ndc) {
                        let before = newRow[headers.price] || 0;
                        original_price += before;
                        const exact = exactById[ndc] || {};
                        let close = closeById[ndc] || {};

                        /** if no results were found, search for more related options by querying related NDC's since perhaps we don't have a good record of related NDC's. Then search by concept name next */
                        if (!exact.prices?.length && !close.prices?.length && sheetLength <= 100) {
                            try {
                                const related = await getRelatedNdcs(ndc)
                                    .catch(err => console.log(`Could not get related ndcs for rx upload. NDC: ${ndc}. Err: ${err.message}`))
                                if (related?.length) {
                                    const ndcs = related.slice(0, 25).map(a => a.ndc11);
                                    let newClose = await new CoreCall('prices', context)._find({
                                        skip_hooks: true, admin_pass: true,
                                        query: {
                                            $limit: 3,
                                            $sort: {price: 1},
                                            ndc11: {$in: ndcs}
                                        }
                                    })
                                        .catch(err => console.log(`Could not search related prices for new close rx upload. NDC: ${ndc}. Err: ${err.message}`))
                                    if (!newClose?.total && related[0].conceptName) {
                                        newClose = await new CoreCall('prices', context)._find({
                                            skip_hooks: true, admin_pass: true,
                                            query: {
                                                $limit: 3,
                                                $sort: {price: 1},
                                                ndc11: { $exists: true },
                                                name: {
                                                    $regex: `${related[0].conceptName.split(' ')[0]}`,
                                                    $options: 'i',
                                                }
                                            }
                                        })
                                    }
                                    /** take close data and add it in to the byId structure */
                                    if (newClose?.total) {
                                        closeById[ndc] = {
                                            ndc,
                                            best: newClose.data[0].price,
                                            ndcs: _flatten(newClose.data.map(a => a.ndcs || [])),
                                            prices: newClose.data.map(a => {
                                                return {
                                                    _id: a._id,
                                                    price: a.price,
                                                    ndc: a.ndc,
                                                    name: a.name,
                                                    s_f: a.s_f,
                                                    description: a.description
                                                }
                                            })

                                        }
                                    }
                                }
                            } catch (e: any) {
                                console.log(`Uncaught error loading in more close data for ex upload: ${e.message}`)
                            }
                        }
                        close = closeById[ndc] || {}

                        bestPrice = Math.round(Math.min(exact.best || close.best || before)) / 100
                        est_price += bestPrice;
                        /** add best price col */

                        /** add exact prices cols */
                        const exactPrices = exact.prices || []
                        for (const idx of [0, 1, 2]) {
                            const {_id, price} = exactPrices[idx] || {}
                            exactMatchCols[idx * 2] = String(_id || '');
                            exactMatchCols[idx * 2 + 1] = (Math.round(Number(price || 0)) / 100)
                        }

                        /** add close prices cols */
                        const closePrices = close.prices || []
                        for (const idx of [0, 1, 2]) {
                            const {_id, price} = closePrices[idx] || {}
                            closeMatchCols[idx * 2] = String(_id || '');
                            closeMatchCols[idx * 2 + 1] = (Math.round(Number(price || 0)) / 100) || '';
                        }
                        /**Populate some of the details if they're missing */
                        const {name, description, s_f} = {...closePrices[0], ...exactPrices[0]}
                        if (name && !newRow[headers.name]) newRow.splice(headers.name, 1, name.replaceAll(',', ' '));
                        if (description && !newRow[headers.description]) newRow.splice(headers.description, 1, description.replaceAll(',', ' '));
                        if (s_f && !newRow[headers.s_f]) newRow.splice(headers.s_f, 1, s_f.replaceAll(',', ' '));

                    }
                    newRow.push(bestPrice, ...exactMatchCols, ...closeMatchCols);
                    newSheet.push(newRow);
                }

                const csvData = newSheet.map(row => row.join(',')).join('\n');

                buffer = Buffer.from(csvData, 'utf-8');
                upload = await uploadCsv(buffer)
                context.result = await new CoreCall('bill-erasers', context).patch(context.result._id, {
                    original_price: Math.round(original_price) / 100,
                    est_price: Math.round(est_price) / 100,
                    files: [getFileSchema(upload)]
                }, {admin_pass: true})
                    .catch(err => {
                        console.log(`Failed to update bill-eraser file for bill-eraser ${context.result._id}: ${err.message}`)
                        return context.result
                    })

                //TODO: may consider adding this back - my concern was I don't want to store price data I have no qa for.

                // /** create prices for any rows that have included a price - a provisional step for us to build our database */
                // await new CoreCall('prices', context).create({}, {
                //     file: {buffer},
                //     runJoin: {price_upload: {...drug_upload, addrs: {source: 'upload', eraser: context.result._id}}}
                // })
                //     .catch(err => console.log(`Error returning price add on drug upload: ${err.message}`))

            }

            context.result.files[0].url = upload.url;
            context.result._fastjoin = {
                ...context.result._fastjoin,
                files: [upload],
                exactById,
                closeById,
                rawCsv: buffer ? new Uint8Array(buffer) : undefined,
                sheet: sheetLength > 100 ? newSheet.slice(0, 100) : newSheet,
                sheetLength
            }
            context.result._id = String(context.result._id)
        }
    }
    return context;
}

/** Return the file associated with this rx bill eraser and optionally also parse it and return the data for client display using rx_file.parse */
import https from 'https';

export const getRxFile = async (context: HookContext): Promise<HookContext> => {
    const {rx_file} = context.params.runJoin || {}
    if (rx_file && context.result.files) {

        const upload = await new CoreCall('uploads', context).get(context.result.files[0].uploadId)
        context.result._fastjoin = {...context.result._fastjoin, files: [upload]}
        context.result.files[0].url = upload.url;
        if (rx_file.parse) {
            const fetchCsv = (url: URL) => {
                return new Promise((resolve, reject) => {
                    https.get(url, (res) => {
                        let data = '';

                        res.on('data', chunk => {
                            data += chunk;
                        });
                        res.on('end', () => resolve(data.trim())); // Trim to remove trailing spaces
                        res.on('error', reject);
                    }).on('error', reject);
                });
            }
            try {
                const csvText: any = await fetchCsv(upload.url)
                const sheet = csvText.split('\n').map(row => row.split(',').map(cell => cell.trim()));
                context.result._fastjoin.sheetLength = sheet.length - 1;
                context.result._fastjoin.sheet = sheet.slice(1, 101);
                context.result._fastjoin.rawCsv = csvText
            } catch (error: any) {
                throw new Error(`Error parsing rx file: ${error.message}`)
            }
        }
    }
    return context;
}
