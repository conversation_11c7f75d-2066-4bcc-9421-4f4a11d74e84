// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    proceduresDataValidator,
    proceduresPatchValidator,
    proceduresQueryValidator,
    proceduresResolver,
    proceduresExternalResolver,
    proceduresDataResolver,
    proceduresPatchResolver,
    proceduresQueryResolver
} from './procedures.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {ProceduresService, getOptions} from './procedures.class.js'
import {proceduresPath, proceduresMethods} from './procedures.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall} from 'feathers-ucan';
import {logChange} from '../../utils/index.js';
import {hackIdBefore, hackIdAfter} from '../cross-sections/utils/index.js';

export * from './procedures.class.js'
export * from './procedures.schema.js'

const authenticate = async (context: HookContext) => {
    const deleter = [['procedures', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: deleter,
        update: deleter,
        remove: deleter
    };
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove']
    })(context) as any;
}

import OpenAi from 'openai'

const supportedSearch = async (context: HookContext) => {
    if(context.params.skip_hooks) return context;
    const {procedure_search} = context.params.runJoin || {}
    if (procedure_search || context.params.check_ai) {
        const aiSearch = async () => {
            const {key, org} = context.app.get('openai');
            const openai = new OpenAi({apiKey: key, organization: org})
            const result = await openai.chat.completions.create({
                messages: [{
                    role: 'system',
                    content: 'You are an expert technical medical assistant to help identify and code procedures to make it easier to document them',
                    },
                    {
                        role: 'user',
                        content: `Return a JSON array containing a list of likely CPT codes that ${procedure_search.condition ? 'would most likely be used to treat' : 'most closely match the description'} ${procedure_search.text}. The list should include no more than ${Math.min(25, (context.params.query?.$limit || 5))} entries. Each entry should be an array with the CPT code at index 0 and the CPT name at index 1, and a lay description at index 2. Return the list as a JSON list. No commentary and no markdown - just a JSON list`
                    }],
                model: "gpt-4o"
            })
                .catch(err => {
                    console.log(`Error searching ai procedures: ${err.message}`)
                })
            // console.log('result', result);
            const list = result ? JSON.parse((result.choices || [])[0]?.message?.content?.replace('```json', '').replace('```', '')?.trim().replace(/[\n']/g, '') || "[]") : [];
            // console.log('list', list);
            const listMap = list.map(a => a[0]);
            const ex = await new CoreCall('procedures', context).find({
                query: {
                    $limit: list.length || 1,
                    code: {$in: listMap}
                }
            })
                .catch(err => {
                    console.log(`Error finding procedures for ai response: ${err.message}`)
                    return {data: []}
                })
            const notFound: any = [];
            const found: any = [];
            const exMap = (ex.data || []).map(a => a.code);
            for (let i = 0; i < list.length; i++) {
                if (!exMap.includes(list[i][0])) notFound.push(list[i]);
                else {
                    found.push(list[i]);
                }
            }
            const pMap = (a: any) => {
                return {
                    code: a[0],
                    codes: [{standard: 'hcpcs', code: a[0]}],
                    name: a[1],
                    names: [a[1]],
                    layName: a[1],
                    layDescription: a[2],
                    description: a[2],
                    descriptions: [a[2]]
                }
            }
            if (found.length) {
                for (let i = 0; i < ex.data.length; i++) {
                    for (let a = 0; a < found.length; a++) {
                        if (found[a][0] === ex.data[i].code) {
                            const description = found[a][2];
                            const name = found[a][1];
                            const pcode = found[a][0];
                            const obj = {
                                descriptions: [description, ...(ex.data[i].descriptions) || []].slice(0, 25),
                                layDescription: description,
                                description,
                                name,
                                layName: name,
                                names: [name, ...(ex.data[i].names) || []].slice(0, 25),
                                codes: Array.from(new Set([pcode, ...(ex.data[i].codes) || []])).slice(0, 25)
                            }
                            const layDescription = ex.data[i].layDescription?.includes('CPT code') ? description : ex.data[i].layDescription;
                            for (const k of Object.keys(obj)) {
                                ex.data[i][k] = obj[k]
                            }
                            new CoreCall('procedures', context)._patch(ex.data[i]._id, {
                                name,
                                layName: name,
                                layDescription,
                                $push: {descriptions: {$each:[description], $position: 0, $slice: 25}, names: { $each: [name], $position: 0, $slice: 25}, codes: pcode}
                            }, { skip_hooks: true, admin_pass: true})
                                .catch(err => console.log(`Error patching code: ${pcode} - ${err.message}`))
                            break;
                        }
                    }
                }
            }
            if (notFound.length) {
                const created = await new CoreCall('procedures', context)._create(notFound.map(a => pMap(a)), { skip_hooks: true, admin_pass: true})
                    .catch(err => {
                        console.log(`Error adding ai procedures: ${err.message}`)
                    })
                for (let i = 0; i < created.length; i++) {
                    ex.total++
                    ex.data.push(created[i]);
                }
            }

            context.result = ex;
            return context;
        }

        if (context.type === 'before') {

            if (!/.*\d.*\d.*/.test(procedure_search.text?.trim())) return await aiSearch()
            else {
                context.params.check_ai = true;
                context.params.query = {code: procedure_search.text.trim()}
            }
        } else if (context.params.check_ai && !context.result.data?.length) return await aiSearch()
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const procedures = (app: Application) => {
    // Register our service on the Feathers application
    app.use(proceduresPath, new ProceduresService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: proceduresMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(proceduresPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(proceduresExternalResolver),
                schemaHooks.resolveResult(proceduresResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(proceduresQueryValidator),
                schemaHooks.resolveQuery(proceduresQueryResolver)
            ],
            find: [
                supportedSearch,
                hackIdBefore('plan_procedures', 'plans', 'procedures')

            ],
            get: [],
            create: [
                schemaHooks.validateData(proceduresDataValidator),
                schemaHooks.resolveData(proceduresDataResolver)
            ],
            patch: [
                schemaHooks.validateData(proceduresPatchValidator),
                schemaHooks.resolveData(proceduresPatchResolver)
            ],
            remove: []
        },
        after: {
            all: [],
            find: [
                hackIdAfter('plan_procedures', 'plans'),
                supportedSearch
            ]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [proceduresPath]: ProceduresService
    }
}
