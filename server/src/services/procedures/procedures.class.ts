// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {Procedures, ProceduresData, ProceduresPatch, ProceduresQuery} from './procedures.schema.js'

export type {Procedures, ProceduresData, ProceduresPatch, ProceduresQuery}

export interface ProceduresParams extends MongoDBAdapterParams<ProceduresQuery> {
}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ProceduresService<ServiceParams extends Params = ProceduresParams> extends MongoDBService<
    Procedures,
    ProceduresData,
    ProceduresParams,
    ProceduresPatch
> {
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        multi: true,
        Model: app.get('mongodbClient').then((db) => db.collection('procedures'))
            .then((collection) => {
                collection.createIndex({code: 1}, {unique: true})
                return collection;
            }),
        operators: ['$regex', '$options']

    }
}
