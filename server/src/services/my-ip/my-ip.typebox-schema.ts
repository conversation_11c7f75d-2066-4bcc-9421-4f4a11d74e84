// TypeBox schema for my-ip service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const myUipSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type UmyUip = Static<typeof myUipSchema>
export const myUipValidator = getValidator(myUipSchema, dataValidator)
export const myUipResolver = resolve<UmyUip, HookContext>({})
export const myUipExternalResolver = resolve<UmyUip, HookContext>({})

export const myUipDataSchema = Type.Object({
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean())
}, { additionalProperties: false })

export type UmyUipData = Static<typeof myUipDataSchema>
export const myUipDataValidator = getValidator(myUipDataSchema, dataValidator)
export const myUipDataResolver = resolve<UmyUipData, HookContext>({})

export const myUipPatchSchema = Type.Partial(myUipDataSchema)
export type UmyUipPatch = Static<typeof myUipPatchSchema>
export const myUipPatchValidator = getValidator(myUipPatchSchema, dataValidator)
export const myUipPatchResolver = resolve<UmyUipPatch, HookContext>({})

const myUipQueryProperties = Type.Pick(myUipSchema, ['_id', 'name', 'type', 'active'], { additionalProperties: false })
export const myUipQuerySchema = querySyntax(myUipQueryProperties)
export type UmyUipQuery = Static<typeof myUipQuerySchema>
export const myUipQueryValidator = getValidator(myUipQuerySchema, queryValidator)
export const myUipQueryResolver = resolve<UmyUipQuery, HookContext>({})
