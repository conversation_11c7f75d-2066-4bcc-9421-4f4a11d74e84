// TypeBox schema for practitioners service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'

// Phone number schema (simplified)
const PhoneSchema = Type.Object({
  number: Type.Optional(Type.Object({
    input: Type.Optional(Type.String()),
    international: Type.Optional(Type.String()),
    national: Type.Optional(Type.String()),
    e164: Type.Optional(Type.String()),
    rfc3966: Type.Optional(Type.String()),
    significant: Type.Optional(Type.String())
  }, { additionalProperties: false })),
  regionCode: Type.Optional(Type.String()),
  valid: Type.Optional(Type.Boolean()),
  possible: Type.Optional(Type.Boolean()),
  possibility: Type.Optional(Type.String()),
  countryCode: Type.Optional(Type.Number()),
  canBeInternationallyDialled: Type.Optional(Type.Boolean()),
  typeIsMobile: Type.Optional(Type.Boolean()),
  typeIsFixedLine: Type.Optional(Type.Boolean())
}, { additionalProperties: true })

// Image schema (simplified)
const ImageSchema = Type.Object({
  uploadId: Type.Optional(ObjectIdSchema()),
  fileId: Type.Optional(Type.String()),
  storage: Type.Optional(Type.String()),
  appPath: Type.Optional(Type.Union([Type.Boolean(), Type.Null()])),
  info: Type.Optional(Type.Object({
    name: Type.Optional(Type.String()),
    size: Type.Optional(Type.Number()),
    type: Type.Optional(Type.String()),
    lastModifiedDate: Type.Optional(Type.Any())
  }, { additionalProperties: false })),
  subPath: Type.Optional(Type.Union([Type.Array(Type.String()), Type.Null()])),
  url: Type.Optional(Type.String())
}, { additionalProperties: true })

// License schema
const LicenseSchema = Type.Object({
  state: Type.Optional(Type.String())
}, { additionalProperties: true })

// City schema
const CitySchema = Type.Object({
  city: Type.Optional(Type.String()),
  state: Type.Optional(Type.String())
}, { additionalProperties: false })

// Provider reference schema
const ProviderRefSchema = Type.Object({
  id: ObjectIdSchema()
}, { additionalProperties: false })

// Main data model schema
export const practitionersSchema = Type.Object({
  _id: ObjectIdSchema(),
  person: Type.Optional(ObjectIdSchema()),
  avatar: Type.Optional(ImageSchema),
  firstName: Type.String(),
  lastName: Type.String(),
  name: Type.Optional(Type.String()),
  name_prefix: Type.Optional(Type.String()),
  gender: Type.Optional(Type.String()),
  credential: Type.Optional(Type.String()),
  auto_created: Type.Optional(Type.Boolean()),
  credentials: Type.Optional(Type.Array(Type.String())),
  phone: Type.Optional(PhoneSchema),
  soleProp: Type.Optional(Type.String()),
  phones: Type.Optional(Type.Array(PhoneSchema)),
  email: Type.Optional(Type.String()),
  npi_date: Type.Optional(Type.Any()),
  npi_update: Type.Optional(Type.Any()),
  npi_status: Type.Optional(Type.String()),
  npi: Type.Optional(Type.String()),
  license: Type.Optional(Type.String()),
  licenses: Type.Optional(Type.Array(LicenseSchema)),
  cities: Type.Optional(Type.Array(CitySchema)),
  license_states: Type.Optional(Type.Array(Type.String())),
  taxonomy1: Type.Optional(Type.String()),
  taxonomy2: Type.Optional(Type.String()),
  taxonomy3: Type.Optional(Type.String()),
  providers: Type.Optional(Type.Record(Type.String(), ProviderRefSchema)),
  // Common fields (simplified)
  createdAt: Type.Optional(Type.Any()),
  updatedAt: Type.Optional(Type.Any()),
  deleted: Type.Optional(Type.Boolean())
}, { additionalProperties: false })

export type Practitioners = Static<typeof practitionersSchema>
export const practitionersValidator = getValidator(practitionersSchema, dataValidator)

// Email handler for resolvers
const emailHandler = async (val: string | undefined) => {
  if (val) return val.toLowerCase().trim()
  return val
}

export const practitionersResolver = resolve<Practitioners, HookContext>({
  name: async (val, data) => {
    return data.firstName + ' ' + data.lastName
  },
  email: emailHandler,
  gender: async (val) => {
    if (val === 'M') return 'male'
    if (val === 'F') return 'female'
    return val
  }
})

export const practitionersExternalResolver = resolve<Practitioners, HookContext>({})

// Schema for creating new data
export const practitionersDataSchema = Type.Object({
  ...Type.Omit(practitionersSchema, ['_id']).properties
}, { additionalProperties: false })

export type PractitionersData = Static<typeof practitionersDataSchema>
export const practitionersDataValidator = getValidator(practitionersDataSchema, dataValidator)
export const practitionersDataResolver = resolve<PractitionersData, HookContext>({
  npi: async (val, data) => {
    if (!val) return `*_${data.lastName}_${new Date().getTime()}`
    return val
  }
})

// Schema for updating existing data (using Type.Partial for simplicity)
export const practitionersPatchSchema = Type.Intersect([
  Type.Partial(practitionersDataSchema),
  Type.Object({
    $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
    $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
  })
])

export type PractitionersPatch = Static<typeof practitionersPatchSchema>
export const practitionersPatchValidator = getValidator(practitionersPatchSchema, dataValidator)
export const practitionersPatchResolver = resolve<PractitionersPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const practitionersQueryProperties = practitionersSchema

export const practitionersQuerySchema = Type.Intersect([
  querySyntax(practitionersQueryProperties),
  Type.Object({
    name: Type.Optional(Type.Any()),
    firstName: Type.Optional(Type.Any()),
    lastName: Type.Optional(Type.Any())
  }, { additionalProperties: false })
])

export type PractitionersQuery = Static<typeof practitionersQuerySchema>
export const practitionersQueryValidator = getValidator(practitionersQuerySchema, queryValidator)
export const practitionersQueryResolver = resolve<PractitionersQuery, HookContext>({})
