// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Threads, ThreadsData, ThreadsPatch, ThreadsQuery } from './threads.schema.js'

export type { Threads, ThreadsData, ThreadsPatch, ThreadsQuery }

export interface ThreadsParams extends MongoDBAdapterParams<ThreadsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ThreadsService<ServiceParams extends Params = ThreadsParams> extends MongoDBService<
  Threads,
  ThreadsData,
  ThreadsParams,
  ThreadsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('threads'))
  }
}
