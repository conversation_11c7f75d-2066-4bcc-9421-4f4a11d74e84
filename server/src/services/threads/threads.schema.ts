// TypeBox schema for threads service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const threadsSchema = Type.Object({
  _id: ObjectIdSchema(),
  subject: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  participants: Type.Optional(Type.Array(ObjectIdSchema())),
  messages: Type.Optional(Type.Array(ObjectIdSchema())),
  status: Type.Optional(Type.String()),
  priority: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  tags: Type.Optional(Type.Array(Type.String())),
  lastMessage: Type.Optional(Type.Any()),
  lastActivity: Type.Optional(Type.Any()),
  archived: Type.Optional(Type.Boolean()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
,
  // Missing fields from old schema
  upVotes: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Threads = Static<typeof threadsSchema>
export const threadsValidator = getValidator(threadsSchema, dataValidator)
export const threadsResolver = resolve<Threads, HookContext>({})
export const threadsExternalResolver = resolve<Threads, HookContext>({})

export const threadsDataSchema = Type.Object({
  ...Type.Omit(threadsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ThreadsData = Static<typeof threadsDataSchema>
export const threadsDataValidator = getValidator(threadsDataSchema, dataValidator)
export const threadsDataResolver = resolve<ThreadsData, HookContext>({})

export const threadsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(threadsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type ThreadsPatch = Static<typeof threadsPatchSchema>
export const threadsPatchValidator = getValidator(threadsPatchSchema, dataValidator)
export const threadsPatchResolver = resolve<ThreadsPatch, HookContext>({})

// Allow querying on any field from the main schema
const threadsQueryProperties = threadsSchema
export const threadsQuerySchema = querySyntax(threadsQueryProperties)
export type ThreadsQuery = Static<typeof threadsQuerySchema>
export const threadsQueryValidator = getValidator(threadsQuerySchema, queryValidator)
export const threadsQueryResolver = resolve<ThreadsQuery, HookContext>({})
