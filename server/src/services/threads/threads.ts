// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    threadsDataValidator,
    threadsPatchValidator,
    threadsQueryValidator,
    threadsResolver,
    threadsExternalResolver,
    threadsDataResolver,
    threadsPatchResolver,
    threadsQueryResolver
} from './threads.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {ThreadsService, getOptions} from './threads.class.js'
import {threadsPath, threadsMethods} from './threads.shared.js'
import {
    logChange,
    logHistory,
    scrub,
    relate,
    getJoin
} from '../../utils/index.js';

import {
    allUcanAuth,
    CapabilityParts,
    noThrowAuth,
    noThrow as noThrowArg, loadExists, setExists,
} from 'feathers-ucan';

import {handleVotes} from '../../utils/votes/index.js';


const authenticate = async (context: HookContext) => {
    const writer = [['threads', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['threads', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: noThrowArg,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };

    if(context.method === 'patch'){
        const ex = await loadExists(context);
        context = setExists(context, ex);
        if(!ex.owners?.length) ucanArgs.patch = noThrowArg as any
    }

    return await allUcanAuth<HookContext>(ucanArgs, {
        creatorPass: ['*'],
        loginPass: [[['createdBy.login'], ['patch']]],
        adminPass: ['patch', 'create', 'remove'],
        or: '*'
    })(context) as any;
}

const joinOwner = {
    service: 'logins',
    herePath: 'createdBy.login',
    joinPath: 'person',
    through: {
        'owner': {
            service: 'ppls'
        }
    }
}

const relateParent = async (context: HookContext): Promise<HookContext> => {
    return await relate('otm', {
        herePath: 'parent.id',
        therePath: 'threads',
        paramsName: 'threadParent',
        thereService: context.data.parent.service
    })(context);
};
const noThrow = async (context:HookContext) => {
    return noThrowAuth(context);
}


export * from './threads.class.js'
export * from './threads.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const threads = (app: Application) => {
    // Register our service on the Feathers application
    app.use(threadsPath, new ThreadsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: threadsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(threadsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(threadsExternalResolver),
                schemaHooks.resolveResult(threadsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(threadsQueryValidator),
                schemaHooks.resolveQuery(threadsQueryResolver)
            ],
            find: [],
            get: [noThrow],
            create: [
                schemaHooks.validateData(threadsDataValidator),
                schemaHooks.resolveData(threadsDataResolver),
                scrub(['body']),
                relateParent
            ],
            patch: [
                schemaHooks.validateData(threadsPatchValidator),
                schemaHooks.resolveData(threadsPatchResolver),
                scrub(['body']),
                logHistory(['body']),
                handleVotes()
            ],
            update: [scrub(['body'])],
            remove: [ relateParent]
        },
        after: {
            all: [getJoin(joinOwner)],
            create: [relateParent],
            patch: [handleVotes()],
            remove: [relateParent]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [threadsPath]: ThreadsService
    }
}
