// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Pings, PingsData, PingsPatch, PingsQuery } from './pings.schema.js'

export type { Pings, PingsData, PingsPatch, PingsQuery }

export interface PingsParams extends MongoDBAdapterParams<PingsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class PingsService<ServiceParams extends Params = PingsParams> extends MongoDBService<
  Pings,
  PingsData,
  PingsParams,
  PingsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('pings'))
  }
}
