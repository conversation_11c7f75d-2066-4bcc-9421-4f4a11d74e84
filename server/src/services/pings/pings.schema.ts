// TypeBox schema for pings service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const pingsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
,
  // Missing fields from old schema
  subject: Type.Optional(Type.String()),
  subjectService: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Pings = Static<typeof pingsSchema>
export const pingsValidator = getValidator(pingsSchema, dataValidator)
export const pingsResolver = resolve<Pings, HookContext>({})
export const pingsExternalResolver = resolve<Pings, HookContext>({})

export const pingsDataSchema = Type.Object({
  ...Type.Omit(pingsSchema, ['_id']).properties
}, { additionalProperties: false })

export type PingsData = Static<typeof pingsDataSchema>
export const pingsDataValidator = getValidator(pingsDataSchema, dataValidator)
export const pingsDataResolver = resolve<PingsData, HookContext>({})

export const pingsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(pingsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type PingsPatch = Static<typeof pingsPatchSchema>
export const pingsPatchValidator = getValidator(pingsPatchSchema, dataValidator)
export const pingsPatchResolver = resolve<PingsPatch, HookContext>({})

// Allow querying on any field from the main schema
const pingsQueryProperties = pingsSchema
export const pingsQuerySchema = querySyntax(pingsQueryProperties)
export type PingsQuery = Static<typeof pingsQuerySchema>
export const pingsQueryValidator = getValidator(pingsQuerySchema, queryValidator)
export const pingsQueryResolver = resolve<PingsQuery, HookContext>({})
