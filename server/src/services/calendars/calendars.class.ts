// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Calendars, CalendarsData, CalendarsPatch, CalendarsQuery } from './calendars.schema.js'

export type { Calendars, CalendarsData, CalendarsPatch, CalendarsQuery }

export interface CalendarsParams extends MongoDBAdapterParams<CalendarsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class CalendarsService<ServiceParams extends Params = CalendarsParams> extends MongoDBService<
  Calendars,
  CalendarsData,
  CalendarsParams,
  CalendarsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('calendars')),
    operators: ['$regex', '$options']

  }
}
