import {ObjectIdSchema} from '@feathersjs/schema';

export const costLimits: any = {
    premium: {},
    moop: {
        single: 9200,
        family: 18400
    },
    deductible: {}
}

export const coins_categories = ['emergency_room', 'primary_care', 'urgent_care', 'dental', 'specialist', 'mental', 'drug'];

export const category_weights = {
    emergency_room: .25,
    primary_care: .1,
    urgent_care: .2,
    specialist: .2,
    dental: .05,
    drug: .2
}


import {AnyObj} from '../../../utils/index.js';


export type CoCategories = {
    emergency_room: number,
    primary_care: number,
    urgent_care: number,
    specialist: number,
    dental: number,
    drug: number
}
type BenNetwork = {
    avg: number,
    display: Array<string>,
    categories: CoCategories
}
type Benefit = {
    in_network: BenNetwork,
    in_network2: BenNetwork,
    combined: BenNetwork,
    oon: BenNetwork
}
type OopNetwork = Partial<{
    in_network: number,
    in_network2: number,
    combined: number,
    oop: number
}>
type Oop = {
    medical: {
        single: OopNetwork,
        family: OopNetwork
    },
    drug: {
        single: OopNetwork,
        family: OopNetwork
    }
}
type BenefitDisplay = { label: string, covered: boolean, detail?: string }
export type NormPolicy = {
    _id: string,
    plan_id: string,
    acaPlan: boolean,
    type: string,
    name: string,
    title: string,
    state_code?: string,
    subtitle: string,
    business_year?: number,
    premium?: number,
    aptc_eligible_premium?: number,
    eligible_dependents: Array<string>,
    hsa_eligible: boolean,
    issuer_id?: string,
    carrierName: string,
    carrierLogo?: string,
    plan_type: string, //HMO, PPO
    benefits_url: string,
    formulary_url: string,
    network_url: string,
    brochure_url: string,
    benefits: { [key: string]: BenefitDisplay },
    exclusions: string,
    on_exchange: boolean,
    off_exchange: boolean,
    tobacco_lookback?: number,
    tobacco?: string,
    moop: Partial<Oop>,
    deductible: Partial<Oop>,
    metal: string,
    coins: Benefit
    copay: Benefit
}

export type Policy = AnyObj

export const oopSchema = {
    type: 'object',
    $comment: 'For each key, medical and drug provide single and family in_network, tier2 network, combined, and out of network limits',
    properties: {
        medical: {
            type: 'object',
            properties: {
                single: {
                    type: 'object',
                    properties: {
                        in_network: {type: 'number'},
                        in_network2: {type: 'number'},
                        combined: {type: 'number'},
                        oop: {type: 'number'}
                    }
                },
                family: {
                    type: 'object',
                    properties: {
                        in_network: {type: 'number'},
                        in_network2: {type: 'number'},
                        combined: {type: 'number'},
                        oop: {type: 'number'}
                    }
                }
            }
        },
        drug: {
            type: 'object',
            properties: {
                single: {
                    type: 'object',
                    properties: {
                        in_network: {type: 'number'},
                        in_network2: {type: 'number'},
                        combined: {type: 'number'},
                        oop: {type: 'number'}
                    }
                },
                family: {
                    type: 'object',
                    properties: {
                        in_network: {type: 'number'},
                        in_network2: {type: 'number'},
                        combined: {type: 'number'},
                        oop: {type: 'number'}
                    }
                }
            }
        }
    }
} as const

export const coinsSchema = {
    type: 'object',
    $comment: 'for each key - in_network, in_network2, combined, oon - provide coinsurance or copay rates for 5 care types, an average for representing it in a single number, and a display array for how to describe or display to users',
    properties: {
        in_network: {
            type: 'object',
            properties: {
                avg: {type: 'number'},
                display: {type: 'array', items: {type: 'string'}},
                categories: {
                    type: 'object',
                    properties: {
                        emergency_room: {type: 'number'},
                        primary_care: {type: 'number'},
                        urgent_care: {type: 'number'},
                        specialist: {type: 'number'},
                        dental: {type: 'number'},
                        drug: {type: 'number'}
                    }
                }
            }
        },
        in_network2: {
            type: 'object',
            properties: {
                avg: {type: 'number'},
                display: {type: 'array', items: {type: 'string'}},
                categories: {
                    type: 'object',
                    properties: {
                        emergency_room: {type: 'number'},
                        primary_care: {type: 'number'},
                        urgent_care: {type: 'number'},
                        specialist: {type: 'number'},
                        dental: {type: 'number'},
                        drug: {type: 'number'}
                    }
                }
            }
        },
        combined: {
            type: 'object',
            properties: {
                avg: {type: 'number'},
                display: {type: 'array', items: {type: 'string'}},
                categories: {
                    type: 'object',
                    properties: {
                        emergency_room: {type: 'number'},
                        primary_care: {type: 'number'},
                        urgent_care: {type: 'number'},
                        specialist: {type: 'number'},
                        dental: {type: 'number'},
                        drug: {type: 'number'}
                    }
                }
            }
        },
        oon: {
            type: 'object',
            properties: {
                avg: {type: 'number'},
                display: {type: 'array', items: {type: 'string'}},
                categories: {
                    type: 'object',
                    properties: {
                        emergency_room: {type: 'number'},
                        primary_care: {type: 'number'},
                        urgent_care: {type: 'number'},
                        specialist: {type: 'number'},
                        dental: {type: 'number'},
                        drug: {type: 'number'}
                    }
                }
            }
        }
    }
} as const

export const benSchema = {
    type: 'object',
    patternProperties: {
        "^.*$": {
            $comment: 'benefits are just areas of coverage and aren\'t necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts',
            type: 'object',
            properties: {
                label: {type: 'string'},
                covered: {type: 'boolean'},
                detail: {type: 'string'},
                cat: ObjectIdSchema()
            }
        }

    }
} as const

export const normPolicySchema = {
    type: 'object',
    properties: {
        acaPlan: {type: 'boolean'},
        plan_id: {type: 'string'},
        state_code: {type: 'string'},
        business_year: {type: 'number'},
        import_date: {},
        rate_expiration: {},
        rate_effective_date: {},
        state_plan_id: {type: 'string'},
        issuer_id: {type: 'string'},
        type: {type: 'string'},
        name: {type: 'string'},
        title: {type: 'string'},
        subtitle: {type: 'string'},
        premium: {type: 'number'},
        aptc_eligible_premium: {type: 'number'},
        eligible_dependents: {
            type: 'array',
            items: {type: 'string'}
        },
        hsa_eligible: {type: 'boolean'},
        carrierName: {type: 'string'},
        carrierLogo: {type: 'string'},
        plan_type: {type: 'string'},
        benefits_url: {type: 'string'},
        formulary_url: {type: 'string'},
        network_url: {type: 'string'},
        brochure_url: {type: 'string'},
        benefits: benSchema,
        exclusions: {type: 'string'},
        on_exchange: {type: 'boolean'},
        off_exchange: {type: 'boolean'},
        tobacco_lookback: {type: 'number'},
        tobacco: {type: 'string'},
        moop: oopSchema,
        deductible: oopSchema,
        metal: {type: 'string'},
        coins: coinsSchema,
        copay: coinsSchema
    }
} as const
