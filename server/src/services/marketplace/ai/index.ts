import {CoreCall} from 'feathers-ucan';
import { HookContext} from '../../../declarations.js';
import OpenAi from 'openai';

import {acaToMarkdown} from '../../shops/chat/files.js';
import {AnyObj, saveChatHistory} from '../../../utils/index.js';
import {cmsMarketplaceGet} from '../cms/index.js';

type Place = { countyfips: string, zipcode: string, state: string }
interface AIChatParams {
    chatId: string;           // Used for saving chat history
    chat_session?: string;    // Optional session ID for grouping messages
    text: string;             // User's input question
    chat_history?: string;    // Markdown or text-formatted history for context
    shop_id?: string;         // Used to fetch a specific 'shop' record,
    household?: { place: Place, people: Array<{age:number, relation:string, gender:string }&AnyObj>, income?: number }, // required if plan not passed
    plan?: any; //Optionally pass in the plan
}
export const ai_chat_get = async (context: HookContext) => {
    const {ai_chat} = context.params.runJoin || {};
    if (ai_chat) {
        const {key, org} = context.app.get('openai');
        const openai = new OpenAi({apiKey: key, organization: org})

        const { shop_id } = ai_chat;
        let shop;
        if(shop_id){
            shop = await new CoreCall('shops', context).get(shop_id);
        }

        if(ai_chat.plan) context.result = ai_chat.plan;
        else {
            const plans: any = await cmsMarketplaceGet(context.id as string, ai_chat.household, ai_chat.household.place)(context);
            context.result = plans[0]
        }

        const drawers = await new CoreCall('junk-drawers', context).find({
            query: {
                $limit: 1,
                itemId: 'ai|context'
            }
        })
        const drawer = drawers.data[0];
        const biasVectorId = drawer?.data?.bias?.vectorStore?.id;
        const vector_store_ids = [biasVectorId];
        const tools:any = [
            {
                type: 'file_search',
                vector_store_ids
            }
        ]
        const input = `
        ## 🧠 Assistant Context
        A user is seeing a health plan in a rich UI that provides information and comparative analysis about the plan. They are asking about additional details. The plan is a major medical plan with the name ${context.result.name || context.result.title}. The carrier name is ${context.result.carrierName}.
        
        ## Plan Summary Details
        These details summarize the benefit structure and known details of this plan. This plan is an ACA exchange plan, so some other elements are known such as covering preventive services, no annual or lifetime dollar limits, no preexisting condition exclusions, etc.
        
        Concerning the below plan details:

- ✅ This data is comprehensive and official. It comes from their legal Summary of Benefits and Coverage (SBC) document and other officially published details. 
- ✅ Although a lot of details are not given for handling situational specifics - that's how insurance works. You have to call the carrier for information like that - and even then it can be hard to get answers.
- ❌ There isn't a laundry list of covered and denied services outside of what's given here - so don't just tell them to go looking for one. Tell them you're referencing the details from the official plan documents, and it doesn't specify. Unfortunately they'll have to contact the carrier about their specific situational need.

        ${acaToMarkdown(context.result)}      
        
        ${shop?.vectoreStore?.id ? `## 📊 Comparative Analysis \n The **final vector store** provided has the results of a comparative analysis. You can trust these as the primary source of how well suited this plan is for the user - outside of specific contract issues that would disqualify it as a fit. This plan is a premium tax credit eligible plan - where that is applicable. Refer to it when asked for recommendations for how well this plan fits.` : ''}
        
              ${ai_chat.current_coverage ? `## Current Coverage \n - The user is currently enrolled in the plan ID ${ai_chat.current_coverage._id || ai_chat.current_coverage.id} - name: ${ai_chat.current_coverage.name}. A major purpose of this simulation is to compare this plan against individual market options.` : ''}
        
        ## Chat History
        ${ai_chat.chat_history || 'N/A'}
        
        ## Question
        ${ai_chat.text}
        
        ## Behavioral Guide
- Use the **vector store** provided is for behavioral/bias guidance. Do not refer to the behavioral guide in your response, it's just a background guidance for context.
        `
        const response = await openai.responses.create({
            model: 'gpt-4o',
            input,
            tools
        })
            .catch(err => {
                console.error(`Error querying plan doc ai chat: ${err.message}`)
                throw new Error(`Error querying plan doc ai chat: ${err.message}`)
            })
        const output: any = response.output.filter(a => a.type === 'message');
        const content: any = output[0].content.filter(a => a.type === 'output_text')[0];

        saveChatHistory({
            chatId: ai_chat.chatId,
            subjectId: context.result._id || context.result.plan_id,
            chat_session: ai_chat.chat_session,
            chat: { question: ai_chat.text, answer: content.text, annotations: content.annotations }
        })(context)

        context.result = { ...context.result, _fastjoin: { ai_response: content} };

    }
    return context;
}
