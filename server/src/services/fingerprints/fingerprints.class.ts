// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type {
  Fingerprints,
  FingerprintsData,
  FingerprintsPatch,
  FingerprintsQuery
} from './fingerprints.schema.js'

export type { Fingerprints, FingerprintsData, FingerprintsPatch, FingerprintsQuery }

export interface FingerprintsParams extends MongoDBAdapterParams<FingerprintsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class FingerprintsService<ServiceParams extends Params = FingerprintsParams> extends MongoDBService<
  Fingerprints,
  FingerprintsData,
  FingerprintsParams,
  FingerprintsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('fingerprints'))
  }
}
