import {HookContext} from '../../../declarations.js';
import {createParse} from '../../uploads/parser/index.js';
import {parsePhoneNumber} from 'awesome-phonenumber';
import {CoreCall, loadExists, setExists} from 'feathers-ucan';
import {toString} from 'uint8arrays';

const isEmailRule = (val) => {
    const reg = /^(([^<>()[\]\\.,;:\s@']+(\.[^<>()[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/;
    return reg.test(val);
};

import {ObjectId} from 'mongodb';
import {_set} from 'symbol-ucan';
import {AnyObj, symmetricEncrypt} from '../../../utils/index.js';
const name_suffixes = ['jr', 'sr', 'jr.', 'sr.', 'ii', 'iii', 'iv', 'v', 'vi', 'vii', 'viii', 'ix', 'x']
const name_prefixes = ['mr', 'mr.', 'ms', 'ms.', 'mrs', 'mrs.', 'dr', 'dr.', 'miss']
export const nameToFirstName = (val) => {
    if(!val) return '';
    const spl = val.split(',');
    if(spl.length > 1) return spl[1].trim()
    else return val.split(' ').filter(a => !name_prefixes.includes(a.toLowerCase()))[0]?.trim()
}
export const nameToLastName = (val) => {
    if(!val) return '';
    const spl = val.split(',');
    if(spl.length > 1) return spl[0].trim()
    else return val.split(' ').filter(a => !name_suffixes.includes(a.toLowerCase())).slice(-1)[0]?.trim()
}

const encryptionKey = process.env.PIN_ENCRYPTOR || '83bca3cf17b4a6525e3895863fce6805a4aae2002915d763fc000dd56044d530';

export const memberUploads = async (context: HookContext): Promise<HookContext> => {
    const {addMembers} = context.params
    if (addMembers && context.params.file) {
        if (context.type === 'before') {
            const exists = await loadExists(context);
            context = setExists(context, exists);
            context.params.query = {$limit: 1000}
            const {result} = createParse(context);

            const errs: { row: number, data: any, key: string, err: string, throw?: boolean }[] = [];

            const thisYear = new Date().getFullYear();
            const oldest = thisYear - 100;
            const {encrypt} = symmetricEncrypt(encryptionKey)

            const testDate = (val: string | number, row, key) => {
                const d = new Date(val);
                const yr = d.getFullYear();
                if (!(yr > oldest) || !(yr < thisYear)) {
                    errs.push({row, key, err: 'Invalid date - must be MM/DD/YYYY or MM/DD/YY', data:val})
                    return undefined;
                } else return toString(encrypt(d.toString()), 'hex');
            }


            const parseDate = (val, row, key) => {
                if (!val) return undefined;
                if (typeof val === 'number') {
                    const unix = new Date().setTime(new Date(val * 1000).getTime() + new Date('01/01/1970').getTime())
                    return testDate(unix, row, key);

                } else {
                    const splt = val.split('/');
                    const yr = splt[2];
                    const nyr = Number(yr);
                    if (nyr > 99) return testDate(val, row, key);
                    else {
                        const prefix = nyr > 24 ? '19' : '20';
                        return testDate(`${splt[0]}/${splt[1]}/${prefix}${yr}`, row, key)
                    }
                }
            }

            const compsObj:any = {};
            const compsByEmail:any = {};
            const headers = addMembers.headers;
            const format = (row, key, data) => {
                return {
                    'name': async (val) => {
                        if (!val) {
                            return undefined;
                        } else return val.trim()
                    },
                    'firstName': async (val, d) => {
                        if (!val) {
                            if(d[headers.name]){
                                return nameToFirstName(d[headers.name])
                            } else errs.push({row, key, err: 'No First Name', throw: true, data})
                            return undefined;
                        } else return val.trim()
                    },
                    'lastName': async (val, d) => {
                        if (!val) {
                            if(d[headers.name]){
                                return nameToLastName(d[headers.name])
                            } else errs.push({row, key, err: 'No Last Name', throw: true, data})
                            return undefined;
                        } else return val.trim()
                    },
                    'email': async (val) => {
                        if (isEmailRule(val)) return val.trim().toLowerCase();
                        else {
                            errs.push({row, key, err: 'Invalid Email', throw: true, data});
                            return undefined;
                        }
                    },
                    'phone': async (val) => {
                        if (!val) return undefined
                        const raw = val.split(' ').join('').split('-').join('').split('(').join('').split(')').join('').split('(').join('');
                        if (raw.charAt(0) === '+' && raw.length === 11) return parsePhoneNumber(raw);
                        else if (raw.length === 10) return parsePhoneNumber(`+1${raw}`);
                        else {
                            errs.push({row, key, err: 'Invalid Phone', data})
                            return undefined;
                        }
                    },
                    'ssn': async (val) => {
                        if (!val) return undefined;
                        const raw = String(val || '1').trim().split('-').join('').split(' ').join('');
                        if (raw.length === 9) return toString(encrypt(raw), 'hex');
                        else {
                            errs.push({row, key, err: 'Invalid SSN', data})
                            return undefined
                        }
                    },
                    'dob': async (val) => {
                        return parseDate(val, row, key)
                    },
                    'hireDate': async (val) => {
                        if (!val) return new Date()
                        else return parseDate(val, row, key)
                    },
                    'comp': async (val, d) => {
                        if (!val) return undefined
                        else if (compsObj[val]){
                            compsByEmail[data[headers.email]] = compsObj[val];
                        } else {
                            const comp = await new CoreCall('comps', context, {skipJoins: true}).find({
                                query: {
                                    key: val,
                                    $limit: 1
                                }, admin_pass: true
                            })
                                .catch(err => {
                                    console.error(`Error loading comp for upload:`, row, err.message);
                                })
                            if (comp?.total) {
                                compsObj[val] = comp.data[0];
                                compsByEmail[d[headers.email]] = comp.data[0];
                            }
                        }
                        return undefined
                    }
                }
            }


            const sheet = addMembers.sheet ? result.filter(a => a.name === addMembers.sheet)[0] : result[0];

            const headerKeys = Object.keys(headers);
            const sliceCount = addMembers.omitFirstRow ? 1 : 0;

            const getPerson = async (val, row): Promise<AnyObj> => {
                const obj = {};
                for (const key of headerKeys) {
                    obj[key] = await format(row, key, val[headers[key]])[key](val[headers[key]], val)
                }
                return obj
            }

            const pplList = await Promise.all(sheet.data.slice(sliceCount).map((a, i) => getPerson(a, i)))

            const filteredPpls = pplList.filter((a, i) => !errs.some(b => b.row === i && b.throw));

            const existing = await new CoreCall('ppls', context, {skipJoins: true}).find({
                query: {
                    $limit: 1000,
                    email: {$in: filteredPpls.map(a => a.email)}
                },
                admin_pass: true
            });

            const modifiedExisting: any[] = [];

            if (existing.total) {
                for (let i = 0; i < existing.data.length; i++) {
                    for (let j = filteredPpls.length - 1; j > -1; j--) {
                        const exd = existing.data[i];
                        const ft = filteredPpls[j];
                        if (ft.code === exd.code) {
                            modifiedExisting.push({...exd, ...ft, _id: exd._id})
                            filteredPpls.splice(j, 1);
                            break;
                        }
                    }
                }
                if (modifiedExisting.length) {
                    const patchMe = async (p) => {
                        const {_id, ...rest} = p;
                        await new CoreCall('ppls', context, {skipJoins: true}).patch(_id, {...rest, $addToSet: { inGroups: context.id , inOrgs: context.data.org || exists.org }}, {admin_pass: true})
                    }
                    await Promise.all(modifiedExisting.map(a => patchMe(a)))
                }
            }

            let status = 200;
            let err = '';
            let ppls: any[] = [];
            if (filteredPpls?.length) ppls = await new CoreCall('ppls', context, {skipJoins: true}).create(filteredPpls.map(a => {
                const { comp, ...rest } = a;
                const obj = {
                    ...rest,
                    inGroups: [context.id],
                    inOrgs: [context.data.org || exists.org].filter(b => !!b)
                };
                if (exists?.org) obj.inOrgs = [exists.org].filter(b => !!b);
                return obj;
            }, { skip_hooks: true, admin_pass: true}))
                .catch(e => {
                    err = `Error adding people: ${e.message.split('').slice(0, 50).join('')}`
                    status = 500;
                    return [];
                })

            let data = {};
            context.result = {};
            context.data = {}
            if (status === 200) {
                const addList: any[] = [...(ppls || []), ...(existing.data || [])].filter(a => !!a);
                const addOne = async (p) => {
                    return await new CoreCall('grp-mbrs', context, {skipJoins: true}).create({
                        person: p._id,
                        name: p.name,
                        email: p.email,
                        group: context.id,
                        org: context.data.org || exists.org
                    })
                        .catch(err => console.error(`Error adding group member: ${err.message}`))
                }
                await Promise.all(addList.map(a => addOne(a)))
                context.result = exists
            }
            const promises:any = []
            for(let i = 0; i < ppls.length; i++){
                const comp = compsByEmail[ppls[i].email]
                if(comp?._id) {
                    promises.push(new CoreCall('cams', context).create({ ...comp, comp: comp._id, person: ppls[i]._id, active: true })
                        .catch(err => console.error(`Error adding cam for person ${ppls[i].email}: ${err.message}`)))
                }
            }
            await Promise.all(promises)
            context.params.addMembers = {
                ...context.params.addMembers,
                added: ppls?.length || 0,
                existing,
                errors: errs,
                updated: Date.now(),
                status,
                err,
                data
            }

        } else {
            context.result = _set(context.result, '_fastjoin.addMembers', context.params.addMembers);
        }
    }
    return context;
}
