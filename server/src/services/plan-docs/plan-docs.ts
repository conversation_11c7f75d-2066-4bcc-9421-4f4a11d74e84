// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    planDocsDataValidator,
    planDocsPatchValidator,
    planDocsQueryValidator,
    planDocsResolver,
    planDocsExternalResolver,
    planDocsDataResolver,
    planDocsPatchResolver,
    planDocsQueryResolver
} from './plan-docs.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {PlanDocsService, getOptions} from './plan-docs.class.js'
import {planDocsPath, planDocsMethods} from './plan-docs.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, loadExists, setExists, noThrow} from 'feathers-ucan';
import {fakeId, getJoin, logChange, logHistory, sanitize} from '../../utils/index.js';
import showdown from 'showdown'
import {_get} from 'symbol-ucan';

export * from './plan-docs.class.js'
export * from './plan-docs.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['plan-docs', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['plan-docs', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        find: noThrow,
        get: noThrow,
        create: [...writer],
        patch: [...writer],
        update: [...writer],
        remove: deleter
    };
    const cap_subjects: any = [];
    if (!['get', 'find'].includes(context.method)) {
        let existing: any = {org: undefined, _fastjoin: {plan: undefined}};
        let plan;
        if (context.method !== 'create') {
            context.params.runJoin = {...context.params.runJoin, plan: true}
            existing = await loadExists(context, {skipJoins: false});
            context = setExists(context, existing);
            plan = existing._fastjoin?.plan
        } else if (context.data.plan) plan = await new CoreCall('plans', context, {skipJoins: true}).get(context.data.plan)
        if (plan?.org) {
            cap_subjects.push(plan.org)
            const orgNamespace = `orgs:${plan?.org}`;
            [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin']].forEach((a: any) => {
                ucanArgs.create.unshift(a);
                ucanArgs.patch.unshift(a);
                ucanArgs.update.unshift(a);
                ucanArgs.remove.unshift(a);
            })
        }
        const planId = plan?._id || context.data.plan || existing.plan
        if (planId) {
            cap_subjects.push(planId)
            const w: CapabilityParts = [`plans:${planId}`, 'planAdmin']
            ucanArgs.create.unshift(w);
            ucanArgs.patch.unshift(w);
            ucanArgs.update.unshift(w);
            ucanArgs.remove.unshift(w);
        }

    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        or: '*',
        cap_subjects
    })(context) as any;
}

const joinPlan = async (context: HookContext): Promise<HookContext> => {
    if (context.params.runJoin?.plan) {
        return getJoin({herePath: 'plan', service: 'plans'})(context)
    }
    return context;
};

export const sanitizeSections = async (context: HookContext): Promise<HookContext> => {
    const {sections} = context.data;
    if (sections) {
        context.data.sectionsUpdatedAt = new Date();
        const converter = new showdown.Converter();
        for (const k in sections) {
            for (const sk in sections[k].sections || {}) {
                const body = sections[k].sections[sk].body
                if (body) context.data.sections[k].sections[sk].body = sanitize(body)
                // if(body) context.data.sections[k].sections[sk].body = NodeHtmlMarkdown.translate(sanitize(converter.makeHtml(body)));
            }
        }
    }
    return context;
}

const replacePlans = async (context: HookContext): Promise<HookContext> => {
    const {path, plan} = context.data;
    if (path && plan) {
        const query: any = {path, plan};
        if (context.id) query._id = {$ne: context.id}
        const ex = await new CoreCall('plan-docs', context, {skipJoins: true}).find({query});
        if (ex.data?.length) {
            delete context.data.createdAt;
            delete context.data.createdBy;
            context.result = await new CoreCall('plan-docs', context).patch(ex.data[0]._id, context.data)
            const removePlanDoc = async (p) => {
                await new CoreCall('plan-docs', context).remove(p._id, {admin_pass: true})
            }
            await Promise.all((ex.data || []).slice(1).map(a => removePlanDoc(a)));
        }
    }
    return context;
}

const checkPath = async (context: HookContext): Promise<HookContext> => {
    if (context.method === 'remove' || context.data.deleted) {
        const existing = await loadExists(context);
        const {path, plan} = existing;
        if (path && plan) {
            const exPlan = await new CoreCall('plans', context, {skipJoins: true}).get(plan);
            const planPath = _get(exPlan, path);
            if (String(planPath) === String(context.id)) {
                await new CoreCall('plans', context, {skipJoins: true}).patch(exPlan._id, {$unset: {[path]: ''}})
                    .catch(err => {
                        throw new Error(`Error removing doc from active plan: ${err.message}`)
                    })
            }
        }
    }
    return context;
}

import {vectorizePlanDocs, ai_query} from './utils/ai.js';

// A configure function that registers the service and its hooks via `app.configure`
export const planDocs = (app: Application) => {
    // Register our service on the Feathers application
    app.use(planDocsPath, new PlanDocsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: planDocsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(planDocsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(planDocsExternalResolver),
                schemaHooks.resolveResult(planDocsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(planDocsQueryValidator),
                schemaHooks.resolveQuery(planDocsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(planDocsDataValidator),
                schemaHooks.resolveData(planDocsDataResolver),
                sanitizeSections,
                replacePlans
            ],
            patch: [
                vectorizePlanDocs,
                schemaHooks.validateData(planDocsPatchValidator),
                schemaHooks.resolveData(planDocsPatchResolver),
                sanitizeSections,
                replacePlans,
                logHistory(['sections']),
                checkPath
            ],
            remove: [checkPath]
        },
        after: {
            all: [joinPlan],
            get: [ai_query],
            create: [],
            patch: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [planDocsPath]: any
    }
}
