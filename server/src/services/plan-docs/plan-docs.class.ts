// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { PlanDocs, PlanDocsData, PlanDocsPatch, PlanDocsQuery } from './plan-docs.schema.js'

export type { PlanDocs, PlanDocsData, PlanDocsPatch, PlanDocsQuery }

export interface PlanDocsParams extends MongoDBAdapterParams<PlanDocsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class PlanDocsService<ServiceParams extends Params = PlanDocsParams> extends MongoDBService<
  PlanDocs,
  PlanDocsData,
  PlanDocsParams,
  PlanDocsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('plan-docs')),
    operators: ['$regex', '$options']
  }
}
