// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Ledgers, LedgersData, LedgersPatch, LedgersQuery } from './ledgers.schema.js'

export type { Ledgers, LedgersData, LedgersPatch, LedgersQuery }

export interface LedgersParams extends MongoDBAdapterParams<LedgersQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class LedgersService<ServiceParams extends Params = LedgersParams> extends MongoDBService<
  Ledgers,
  LedgersData,
  LedgersParams,
  LedgersPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('ledgers'))
  }
}
