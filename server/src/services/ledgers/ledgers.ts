// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    ledgersDataValidator,
    ledgersPatchValidator,
    ledgersQueryValidator,
    ledgersResolver,
    ledgersExternalResolver,
    ledgersDataResolver,
    ledgersPatchResolver,
    ledgersQueryResolver
} from './ledgers.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {LedgersService, getOptions} from './ledgers.class.js'
import {ledgersPath, ledgersMethods} from './ledgers.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, loadExists, setExists} from 'feathers-ucan';
import {logChange} from '../../utils/index.js';

export * from './ledgers.class.js'
export * from './ledgers.schema.js'

const authenticate = async (context: HookContext) => {
    const editor = [['ledgers', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['ledgers', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: editor,
        update: editor,
        remove: deleter
    };
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove']
    })(context) as any;
}

// A configure function that registers the service and its hooks via `app.configure`
export const ledgers = (app: Application) => {
    // Register our service on the Feathers application
    app.use(ledgersPath, new LedgersService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: ledgersMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(ledgersPath).hooks({
        around: {
            all: [schemaHooks.resolveExternal(ledgersExternalResolver), schemaHooks.resolveResult(ledgersResolver)]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(ledgersQueryValidator),
                schemaHooks.resolveQuery(ledgersQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(ledgersDataValidator),
                schemaHooks.resolveData(ledgersDataResolver)
            ],
            patch: [
                schemaHooks.validateData(ledgersPatchValidator),
                schemaHooks.resolveData(ledgersPatchResolver)
            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [ledgersPath]: LedgersService
    }
}
