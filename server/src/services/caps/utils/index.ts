import {HookContext} from '../../../declarations';
import {AnyObj} from '../../../utils/index';
import {CoreCall} from 'feathers-ucan';
import {buildUcan, Capability, encodeKeyPair, ucanToken} from 'symbol-ucan';

export const autoCreate = (service:string, name:string) => {
    return async (context: HookContext): Promise<HookContext> => {

        //remove org if method fails
        const undo = async (message: string, err?: AnyObj) => {
            console.error(message + ': ' + err?.message || '');
            await new CoreCall(service, context).remove(context.result._id, {admin_pass: true, disableSoftDelete: true})
            throw new Error(message)
        }

        const cap = await new CoreCall('caps', context).create({subject: context.result._id, subjectService: service})
            .catch(err => undo('Error setting up permissions', err))

        if(!cap) return context;
        const {_id, owner, did} = context.params.login || {};
        if (!_id || !owner || !did) await undo('Issue authenticating group creator')

        //build ucan
        const {secret, defaultScheme, defaultHierPart} = context.app.get('authentication') as AnyObj;

        const canEditOrg: Capability = {
            with: {scheme: defaultScheme, hierPart: defaultHierPart},
            can: {namespace: `${service}:${context.result._id}`, segments: ['*']}
        }

        const applyUcan = await buildUcan({
            audience: did,
            issuer: encodeKeyPair({secretKey: secret}),
            proofs: [],
            capabilities: [canEditOrg]
        })
            .catch(async err => {
                await undo(`Issue creating ${service} automatic admin permissions ucan`, err)
                throw new Error('Error in auto ucan gen for admin permissions set')
            })

        const token = ucanToken(applyUcan);
        //create superadmins cap
        await new CoreCall('caps', context).patch(cap._id, {
            $set: {
                [`caps.${name}.ucan`]: token,
                [`caps.${name}.description`]: 'All available permissions'
            },
            $addToSet: {[`caps.${name}.logins`]: String(_id)}
        }, { admin_pass: true})
            .catch(async err => {
                await undo('Issue setting up super admin - try again', err)
                throw new Error('Error creating admin group')
            })
        return context;
    }
}
