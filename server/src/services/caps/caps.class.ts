// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Caps, CapsData, CapsPatch, CapsQuery } from './caps.schema.js'

export type { Caps, CapsData, CapsPatch, CapsQuery }

export interface CapsParams extends MongoDBAdapterParams<CapsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class CapsService<ServiceParams extends Params = CapsParams> extends MongoDBService<
  Caps,
  CapsData,
  CapsParams,
  CapsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('caps'))
        .then((collection) => {
          collection.createIndex({subject: 1}, {unique: true});
          return collection
        }),
  }
}
