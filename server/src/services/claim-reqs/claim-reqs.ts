// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    claimReqsDataValidator,
    claimReqsPatchValidator,
    claimReqsQueryValidator,
    claimReqsResolver,
    claimReqsExternalResolver,
    claimReqsDataResolver,
    claimReqsPatchResolver,
    claimReqsQueryResolver
} from './claim-reqs.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {ClaimReqsService, getOptions} from './claim-reqs.class.js'
import {claimReqsPath, claimReqsMethods} from './claim-reqs.shared.js'
import {getJoin, logChange, relate, scrubUploads} from '../../utils/index.js';
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, getExists, loadExists, setExists} from 'feathers-ucan';

export * from './claim-reqs.class.js'
export * from './claim-reqs.schema.js'
const authenticate = async (context: HookContext) => {
    const writer = [['claims-requests', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['claims-requests', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };
    const cap_subjects:any = []

    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create') {

            const existing = await loadExists(context);
            context = setExists(context, existing);
            //allow changes before approval
            if (existing) {
                const orgNamespace = `orgs:${existing.org}`;
                cap_subjects.push(existing.org);
                if (existing.providerOrg) {
                    const providerNamespace = `orgs:${existing.providerOrg}`;
                    const providerWrite: CapabilityParts[] = [[providerNamespace, 'WRITE'], [providerNamespace, 'providerAdmin'], [providerNamespace, 'orgAdmin']]
                    for (const w of providerWrite) {
                        ucanArgs.patch.unshift(w);
                        ucanArgs.update.unshift(w);
                    }
                }
                const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'planAdmin']]
                for (const w of orgWrite) {
                    ucanArgs.patch.unshift(w);
                    ucanArgs.update.unshift(w);
                }
            }
        }
    }

    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['person/person'], '*']],
        creatorPass: '*',
        cap_subjects,
        or: '*'
    })(context) as any;
}

const runJoins = async (context: HookContext) => {
    const {cr_provider} = context.params.runJoin || {};
    if (cr_provider) return getJoin({service: 'providers', herePath: 'provider'})(context);
    return context;
}

const relateVisits = async (context: HookContext) => {
    const config = {
        herePath: 'visit',
        therePath: 'claimReqs',
        thereService: 'visits',
        paramsName: 'claimRequestToVisit',
    }
    return await relate('otm', config)(context)
}

const paths = ['files.*'];
const uploadsConfig = {paths};
// A configure function that registers the service and its hooks via `app.configure`

const protectStatus = async (context: HookContext) => {
    if(context.params.skip_hooks) return context;
    const ex = await loadExists(context);
    context = setExists(context, ex);
    if (ex.status === 'approved') throw new Error('This request has been approved, you cannot change it');
    return context;
}
const execute = async (context: HookContext) => {
    if (context.data.status === 'approved' || context.data.$set?.status === 'approved') {
        const ex = getExists(context);
        const {files, claim, visit} = ex;
        if (ex.removeRequest) {
            if (visit && !claim) await new CoreCall('visits', context).remove(visit, {admin_pass: true});
            else if (claim) await new CoreCall('claims', context).remove(claim, {admin_pass: true});
        } else {
            const $set = {};
            for (const k in files) {
                $set[`files.${k}_auto_added`] = files[k];
            }
            const {claimData, visit, person, patient, plan, provider, practitioner} = ex;
            if (claim) await new CoreCall('claims', context).patch(claim, {$set});
            else if (claimData) {
                await new CoreCall('claims', context).create({
                    visit,
                    person,
                    patient,
                    plan,
                    provider,
                    practitioner, ...claimData
                }, {$set});
            }
            if (visit) await new CoreCall('visit', context).patch(claim, {$set});
        }
    }
    return context;
}

import OpenAi from 'openai'

const parse_bill = async (context: HookContext) => {
    const {key, org} = context.app.get('openai');
    const openai = new OpenAi({apiKey: key, organization: org})
    const result = await openai.chat.completions.create({

        messages: [{
            role: 'system',
            content: `Return a JSON object containing a list of Merchant Category Codes (MCC) that match the description. The list should include up to 5 entries, each as an array with the MCC code at index 0 and the MCC name at index 1. Format each entry as [code, 'name']. For example, if the description were 'accountant', the entry would be [8931, 'accountant']. Return the list as a JSON list. No commentary and no markdown - just a JSON list`
        }],
        model: "gpt-4o"
    })
    context.result = result;
    return context;
}

export const claimReqs = (app: Application) => {
    // Register our service on the Feathers application
    app.use(claimReqsPath, new ClaimReqsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: claimReqsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(claimReqsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(claimReqsExternalResolver),
                schemaHooks.resolveResult(claimReqsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(claimReqsQueryValidator),
                schemaHooks.resolveQuery(claimReqsQueryResolver),
                scrubUploads(uploadsConfig)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(claimReqsDataValidator),
                schemaHooks.resolveData(claimReqsDataResolver),
                relateVisits
            ],
            patch: [
                schemaHooks.validateData(claimReqsPatchValidator),
                schemaHooks.resolveData(claimReqsPatchResolver),
                protectStatus,
                execute,
                relateVisits
            ],
            remove: [relateVisits]
        },
        after: {
            all: [
                scrubUploads(uploadsConfig),
                runJoins

            ],
            create: [relateVisits],
            patch: [relateVisits],
            remove: [relateVisits]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [claimReqsPath]: ClaimReqsService
    }
}
