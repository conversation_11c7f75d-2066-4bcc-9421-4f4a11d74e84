// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import type { Application } from '../../declarations.js'
import { LoginsService, getOptions } from './logins.class.js'
import { loginsPath, loginsMethods } from './logins.shared.js'
import {loginHooks} from "./hooks/index.js";

export * from './logins.class.js'
export * from './logins.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const logins = (app: Application) => {
  // Register our service on the Feathers application
  app.use(loginsPath, new LoginsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: loginsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(loginsPath).hooks(loginHooks)
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [loginsPath]: LoginsService
  }
}
