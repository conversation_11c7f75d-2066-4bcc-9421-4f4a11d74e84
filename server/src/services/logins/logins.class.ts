// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {Logins, LoginsData, LoginsPatch, LoginsQuery} from './logins.schema.js'

export type {Logins, LoginsData, LoginsPatch, LoginsQuery}

export interface LoginsParams { [key: string]: any }
// export interface LoginsParams extends MongoDBAdapterParams<LoginsQuery> {
//     core: any
// }

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class LoginsService<ServiceParams extends Params = LoginsParams> extends MongoDBService<Logins,
    LoginsData,
    LoginsParams,
    LoginsPatch> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        multi: true,
        Model: app
            .get('mongodbClient')
            .then((db) => db.collection('logins'))
            .then((collection) => {
                collection.createIndex({owner: 1}, {unique: true});
                collection.createIndex({email: 1}, {unique: false});
                return collection
            })
    }
}
