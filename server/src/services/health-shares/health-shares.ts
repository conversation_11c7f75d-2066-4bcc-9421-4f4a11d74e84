// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    healthSharesDataValidator,
    healthSharesPatchValidator,
    healthSharesQueryValidator,
    healthSharesResolver,
    healthSharesExternalResolver,
    healthSharesDataResolver,
    healthSharesPatchResolver,
    healthSharesQueryResolver
} from './health-shares.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {HealthSharesService, getOptions} from './health-shares.class.js'
import {healthSharesPath, healthSharesMethods} from './health-shares.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {logChange, removeVectorStore, scrubUploads} from '../../utils/index.js';
import {hsGuidelines} from './utils/ai.js'

export * from './health-shares.class.js'
export * from './health-shares.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['health-shares', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['health-shares', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: [...writer],
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        or: '*'
    })(context) as any;
}

/** coverages using a product vector id cannot have that reference removed - or they won't know to update it. Prevent it being removed for this reason - the product can simply be "retired" by emptying it out */
const protectProducts = async (context: HookContext) => {
    if (context.data.products) throw new Error('Cannot patch products like that - use $set, $unset')
    if(context.data.$unset){
        if(context.data.$unset.products) throw new Error('Cannot unset all products at once - remove one at a time')
        for (const key in context.data.$unset) {
            if (/^products\.[^.]+$/.test(key)) {
                const using = await new CoreCall('coverages', context, {skipJoins: true}).find({ query: { $limit: 1, productDetailRef: `${context.id}:${key.split('.')[1] }`}});
                if(using.total) throw new Error('Cannot remove product that is in use by coverages - change the product or add a new one')
                else {
                    const ex = await loadExists(context)
                    context = setExists(context, ex);
                    const product = ex.products[key.split('.')[1]];
                    if(product.vectorStore?.id) await removeVectorStore(product.vectorStore.id)(context)
                    if(product.guidelines?.uploadId) await new CoreCall('uploads', context).remove(product.guidelines.uploadId, { admin_pass: true, disableSoftDelete: true })
                }

            } else if(key.includes('products.') && key.includes('vectorStore')){
                const ex = await loadExists(context)
                context = setExists(context, ex);
                await removeVectorStore(ex.products[key.split('.')[1]].vectorStore.id)(context)
            }
        }
    }
    return context;
}

import multer from 'multer';

const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
    await new Promise((resolve) => multipartMiddleware.single('file')(req, res, resolve));
    req.feathers.file = req.file;
    req.feathers.runJoin = req.query.runJoin
    req.feathers.core = req.query.core
    return next();
}

// A configure function that registers the service and its hooks via `app.configure`
export const healthShares = (app: Application) => {
    // Register our service on the Feathers application
    app.use(healthSharesPath, restMiddleware, new HealthSharesService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: healthSharesMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(healthSharesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(healthSharesExternalResolver),
                schemaHooks.resolveResult(healthSharesResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(healthSharesQueryValidator),
                schemaHooks.resolveQuery(healthSharesQueryResolver),
                scrubUploads({paths: ['products.*.guidelines', 'logo', 'video', 'files.*']})
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(healthSharesDataValidator),
                schemaHooks.resolveData(healthSharesDataResolver)
            ],
            patch: [
                hsGuidelines,
                protectProducts,
                schemaHooks.validateData(healthSharesPatchValidator),
                schemaHooks.resolveData(healthSharesPatchResolver)
            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [healthSharesPath]: any
    }
}
