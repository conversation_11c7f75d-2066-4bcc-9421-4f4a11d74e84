import {HookContext} from '../../../declarations.js';
import {CoreCall, loadExists, setExists} from 'feathers-ucan';
import {addFileToVectorStore, removeVectorStore} from '../../../utils/index.js';
import {getFileSchema} from '../../uploads/utils/index.js';
import OpenAi from 'openai';

export const hsGuidelines = async (context: HookContext) => {
    const {hs_guidelines} = context.params.runJoin || {};
    if (hs_guidelines) {

        const ex = await loadExists(context);
        context = setExists(context, ex);

        const { _id } = ex;

        const { productId } = hs_guidelines;

        const newFile = await new CoreCall('uploads', context).create({...context.data, file: context.params.file, storage: 'storj'})
            .catch((err) => {
                throw new Error(`Could not add upload: ${err.message}`)
            })
        const fileSchema = getFileSchema(newFile)
        context.data.$set = { ...context.data.$set, [`products.${productId}.guidelines`]: fileSchema }

        const vectorRes = await addFileToVectorStore(context, {  mimeType: 'text/markdown', buffer: context.params.file.buffer, vectorConfig: ex.products[productId].vectorStore, fileName: `${ex.name}_${productId}.md`, storeName: _id, taskName: 'hs guidelines'})

        context.data.$set = { ...context.data.$set, [`products.${productId}.vectorStore`]: { id: vectorRes.vectorStoreId, fileIds: [vectorRes.file.id].filter(a => !!a), updatedAt: new Date() }}

        delete context.params.file;
        context.params.readable = vectorRes.readable;


    }
    return context;
}
