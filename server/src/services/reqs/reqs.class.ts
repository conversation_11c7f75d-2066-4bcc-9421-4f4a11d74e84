// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Reqs, ReqsData, ReqsPatch, ReqsQuery } from './reqs.schema.js'

export type { Reqs, ReqsData, ReqsPatch, ReqsQuery }

export interface ReqsParams extends MongoDBAdapterParams<ReqsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ReqsService<ServiceParams extends Params = ReqsParams> extends MongoDBService<
  Reqs,
  ReqsData,
  ReqsParams,
  ReqsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('reqs'))
  }
}
