// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    reqsDataValidator,
    reqsPatchValidator,
    reqsQueryValidator,
    reqsResolver,
    reqsExternalResolver,
    reqsDataResolver,
    reqsPatchResolver,
    reqsQueryResolver
} from './reqs.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {ReqsService, getOptions} from './reqs.class.js'
import {reqsPath, reqsMethods} from './reqs.shared.js'
import {logChange} from "../../utils/index.js";

import {allUcanAuth, CapabilityParts, noThrow} from 'feathers-ucan';


const authenticate = async (context: HookContext) => {
    const writer = [['reqs', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['reqs', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: noThrow,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };

    if(context.params.runJoin?.patchReq) ucanArgs.patch = noThrow as any
    return await allUcanAuth<HookContext>(ucanArgs, {
        creatorPass: ['*'],
        adminPass: ['patch', 'create', 'remove'],
        or: '*'
    })(context)
        .catch(err => {
            console.log(err);
        })
}



export * from './reqs.class.js'
export * from './reqs.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const reqs = (app: Application) => {
    // Register our service on the Feathers application
    app.use(reqsPath, new ReqsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: reqsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(reqsPath).hooks({
        around: {
            all: [schemaHooks.resolveExternal(reqsExternalResolver), schemaHooks.resolveResult(reqsResolver)]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(reqsQueryValidator),
                schemaHooks.resolveQuery(reqsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(reqsDataValidator),
                schemaHooks.resolveData(reqsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(reqsPatchValidator),
                schemaHooks.resolveData(reqsPatchResolver)
            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [reqsPath]: ReqsService
    }
}
