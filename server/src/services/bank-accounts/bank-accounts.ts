// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    bankAccountsDataValidator,
    bankAccountsPatchValidator,
    bankAccountsQueryValidator,
    bankAccountsResolver,
    bankAccountsExternalResolver,
    bankAccountsDataResolver,
    bankAccountsPatchResolver,
    bankAccountsQueryResolver
} from './bank-accounts.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {BankAccountsService, getOptions} from './bank-accounts.class.js'
import {bankAccountsPath, bankAccountsMethods} from './bank-accounts.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists, UcanAllArgs} from 'feathers-ucan';
import {encryptedFields, getJoin, logChange, fieldDecrypt} from '../../utils/index.js';

export * from './bank-accounts.class.js'
export * from './bank-accounts.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['bank-accounts', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['bank-accounts', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        get: writer,
        find: writer,
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects: any = [];
    if (context.method !== 'create' && !context.params.admin_pass && !context.params.loopingAuth) {
        const existing = await loadExists(context, {params: {admin_pass: true, loopingAuth: true}});
        context = setExists(context, existing);
        //allow changes before approval
        if (existing) {
            if (existing.type === 'business') {
                const orgNamespace = `orgs:${existing.owner}`;
                cap_subjects.push(existing.owner);
                const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'providerAdmin'], [orgNamespace, 'WRITE']]
                for (const w of orgWrite) {
                    ucanArgs.get.unshift(w);
                    ucanArgs.find.unshift(w);
                    ucanArgs.patch.unshift(w);
                    ucanArgs.update.unshift(w);
                    ucanArgs.remove.unshift(w);
                }
            }
        }
        if (context.params.banking?.otp) {
            ucanArgs.get = undefined
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['get', 'find', 'patch', 'create', 'remove'],
        loginPass: [[['owner/owner'], '*']],
        cap_subjects
    })(context) as any;
}

const protectAccountNumber = (context: HookContext) => {
    if (!context.params.encrypt?.unprotect?.accountNumber) {
        const restrict = (val: any) => {
            const {accountNumber, ...rest} = val;
            return rest;
        }
        if (context.method === 'find') context.result.data = context.result.data.map(a => restrict(a));
        else context.result = restrict(context.result);
    } else return encryptedFields(['accountNumber'])(context);
    return context;
}

const setLast4 = (context: HookContext) => {
    if (['patch', 'create'].includes(context.method)) {
        const acctNo = context.data.accountNumber || context.data.$set?.accountNumber;
        if (acctNo) context.data.last4 = acctNo.slice(acctNo.length - 4)
    }
    return context;
}

const syncOwner = async (context: HookContext) => {
    const service = context.result.type === 'individual' ? 'ppls' : 'orgs';
    const obj = context.method === 'remove' ? {$unset: {[`bankAccounts.${context.id}`]: ''}} : {$set: {[`bankAccounts.${context.result._id}.id`]: context.result._id}}
    await new CoreCall(service, context).patch(context.result.owner, obj)
    return context;
}

const protectOwner = (context: HookContext) => {
    if (context.data.owner || context.data.$set?.owner) throw new Error('Cannot edit account owner - add a new account instead');
    return context;
}


const handleRemove = async (context: HookContext) => {
    if ((context.method === 'remove' || context.data.deleted) && context.result.moov_link_id) {
        const org = await new CoreCall('orgs', context, {skipJoins: true}).get(context.result.owner);
        const moov_id = org.treasury.id;
        await new CoreCall('banking', context).get(moov_id, {
            banking: {
                moov: {
                    method: 'delete_external_account',
                    args: [moov_id, context.result.moov_link_id]
                }
            }
        })
    }
    return context;
}


const runJoins = async (context: HookContext) => {
    if (context.params.runJoin) {
        if (context.params.runJoin.account_owner) return await getJoin({service: 'orgs', herePath: 'owner'})(context)
    }
    return context;
}

const linkToMoov = async (context: HookContext) => {
    if (context.method === 'create' || context.result.moov_link_id === 'connect') {
        context = encryptedFields(['accountNumber'])(context)
        const {accountNumber, routingNumber, type, accountType} = context.result
        const owner = await new CoreCall(type === 'business' ? 'orgs' : 'ppls', context).get(context.result.owner);

        const bankAccount = {
            accountNumber,
            routingNumber,
            bankAccountType: accountType || 'checking',
            holderType: type,
            holderName: owner.legalName || owner.name
        }
        const linked = await new CoreCall('banking', context).get(owner.treasury.id, {
            banking: {
                moov: {
                    method: 'create_external_account',
                    args: [bankAccount]
                }
            }
        })

        if (linked) {
            const obj: any = {moov_link_id: linked.bankAccountID}
            if (linked.verification) obj.verification = linked.verification;
            context.result = await new CoreCall('bank-accounts', context).patch(context.result._id, obj)
        }

    }
    return context;
}


const dedup = async (context: HookContext) => {
    const {owner, routingNumber} = context.data;
    const siblings = await new CoreCall('bank-accounts', context).find({
        query: {
            $limit: 1,
            owner,
            routingNumber
        },
        encrypt: {unprotect: {accountNumber: true}}
    })
    if (siblings.total) {
        const accounts = siblings.data.map(a => fieldDecrypt(a.accountNumber, context));
        if (accounts.includes(fieldDecrypt(context.data.accountNumber, context))) throw new Error('This account number has already been added')
    }
    return context;
}


// A configure function that registers the service and its hooks via `app.configure`
export const bankAccounts = (app: Application) => {
    // Register our service on the Feathers application
    app.use(bankAccountsPath, new BankAccountsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: bankAccountsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(bankAccountsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(bankAccountsExternalResolver),
                schemaHooks.resolveResult(bankAccountsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                setLast4,
                schemaHooks.validateQuery(bankAccountsQueryValidator),
                schemaHooks.resolveQuery(bankAccountsQueryResolver),
                encryptedFields(['accountNumber'])
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(bankAccountsDataValidator),
                schemaHooks.resolveData(bankAccountsDataResolver),
                dedup
            ],
            patch: [
                protectOwner,
                schemaHooks.validateData(bankAccountsPatchValidator),
                schemaHooks.resolveData(bankAccountsPatchResolver)
            ],
            update: [protectOwner],
            remove: []
        },
        after: {
            all: [runJoins],
            get: [
                protectAccountNumber
            ],
            find: [protectAccountNumber],
            create: [linkToMoov, protectAccountNumber, syncOwner],
            update: [protectAccountNumber, syncOwner],
            patch: [linkToMoov, protectAccountNumber, syncOwner, handleRemove],
            remove: [protectAccountNumber, handleRemove]

        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [bankAccountsPath]: BankAccountsService
    }
}
