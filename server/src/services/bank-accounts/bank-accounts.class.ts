// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { BankAccounts, BankAccountsData, BankAccountsPatch, BankAccountsQuery } from './bank-accounts.schema.js'

export type { BankAccounts, BankAccountsData, BankAccountsPatch, BankAccountsQuery }

export interface BankAccountsParams extends MongoDBAdapterParams<BankAccountsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class BankAccountsService<ServiceParams extends Params = BankAccountsParams> extends MongoDBService<
  BankAccounts,
  BankAccountsData,
  BankAccountsParams,
  BankAccountsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('bank-accounts'))
  }
}
