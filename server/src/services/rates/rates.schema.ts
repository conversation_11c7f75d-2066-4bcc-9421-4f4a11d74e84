// TypeBox schema for rates service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const ratesSchema = Type.Object({
  _id: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  coverage: Type.Optional(ObjectIdSchema()),
  market: Type.Optional(ObjectIdSchema()),
  network: Type.Optional(ObjectIdSchema()),
  ageRange: Type.Optional(Type.Object({
    min: Type.Optional(Type.Number()),
    max: Type.Optional(Type.Number())
  ,
  // Missing fields from old schema
  state: Type.Optional(Type.Number()),
}, { additionalProperties: false })),
  tobacco: Type.Optional(Type.Boolean()),
  region: Type.Optional(Type.String()),
  county: Type.Optional(Type.String()),
  zipCode: Type.Optional(Type.String()),
  premium: Type.Number(),
  deductible: Type.Optional(Type.Number()),
  outOfPocketMax: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  rateType: Type.Optional(Type.String()),
  familyTier: Type.Optional(Type.String()),
  metalLevel: Type.Optional(Type.String()),
  issuerName: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type Rates = Static<typeof ratesSchema>
export const ratesValidator = getValidator(ratesSchema, dataValidator)
export const ratesResolver = resolve<Rates, HookContext>({})
export const ratesExternalResolver = resolve<Rates, HookContext>({})

export const ratesDataSchema = Type.Object({
  ...Type.Omit(ratesSchema, ['_id']).properties
}, { additionalProperties: false })

export type RatesData = Static<typeof ratesDataSchema>
export const ratesDataValidator = getValidator(ratesDataSchema, dataValidator)
export const ratesDataResolver = resolve<RatesData, HookContext>({})

export const ratesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(ratesSchema, ['_id'])).properties
}, { additionalProperties: false })
export type RatesPatch = Static<typeof ratesPatchSchema>
export const ratesPatchValidator = getValidator(ratesPatchSchema, dataValidator)
export const ratesPatchResolver = resolve<RatesPatch, HookContext>({})

// Allow querying on any field from the main schema
const ratesQueryProperties = ratesSchema
export const ratesQuerySchema = querySyntax(ratesQueryProperties)
export type RatesQuery = Static<typeof ratesQuerySchema>
export const ratesQueryValidator = getValidator(ratesQuerySchema, queryValidator)
export const ratesQueryResolver = resolve<RatesQuery, HookContext>({})
