// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    ratesDataValidator,
    ratesPatchValidator,
    ratesQueryValidator,
    ratesResolver,
    ratesExternalResolver,
    ratesDataResolver,
    ratesPatchResolver,
    ratesQueryResolver
} from './rates.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {RatesService, getOptions} from './rates.class.js'
import {ratesPath, ratesMethods} from './rates.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {getJoin, logChange, relate} from '../../utils/index.js';
import { fillFixedRates, fixFixedRates } from '../coverages/utils/rates.js';

export * from './rates.class.js'
export * from './rates.schema.js'

const runJoins = async (context:HookContext):Promise<HookContext> => {
    const { runJoin } = context.params;
    if(runJoin){
        if(runJoin.rateCoverage) return getJoin({ herePath: 'coverage', therePath: '_id', service: 'coverages' })(context)
    }
    return context;
}

const authenticate = async (context: HookContext) => {
    const writer = [['coverages', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['coverages', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: writer,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = []
    if (!['get', 'find'].includes(context.method)) {
        let existing:{ by:any, issuer:any, _fastjoin:any} = {by: undefined, issuer: undefined, _fastjoin: {}};
        if (context.method !== 'create') {
            existing = await loadExists(context, { params: { runJoin: { 'rateCoverage': true, admin_pass: true  }}});
            context = setExists(context, existing);
        }
        const byId = existing._fastjoin?.coverage?.by
        if(byId){
            const byNamespace = `orgs:${byId}`;
            cap_subjects.push(byId)
            const orgWrite: CapabilityParts[] = [[byNamespace, 'WRITE'], [byNamespace, 'orgAdmin']]
            for (const w of orgWrite) {
                ucanArgs.patch.unshift(w);
                ucanArgs.update.unshift(w);
                ucanArgs.remove.unshift(w);
            }
        }
        const issuerId = existing._fastjoin?.coverage?.issuer
        if(issuerId){
            const issuerNamespace = `orgs:${issuerId}`;
            cap_subjects.push(issuerId)
            const w:CapabilityParts = [issuerNamespace, 'WRITE']
            ucanArgs.patch.unshift(w);
            ucanArgs.update.unshift(w);
            ucanArgs.remove.unshift(w);
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        cap_subjects
    })(context) as any;
}

const relateCoverage = async (context: HookContext): Promise<HookContext> => {
    return relate('otm', { herePath: 'coverage', therePath: 'rates', thereService: 'coverages', paramsName: 'rateToCoverage'})(context);
}

const fixAreaRates = async (context: HookContext): Promise<HookContext> => {
    /** account for all possible area patch scenarios */
    let setAreas = (val:any, c:HookContext) => {
        c.data.areas = val;
        return c;
    }
    let getEx = (val:any, ex:any) => {
        return (ex?.areas || []).filter((a:any) => a.name === val?.name)[0]?.premium
    }
    let areas = context.data.areas;
    if(!areas) {
        areas = context.data.$push?.areas;
        setAreas = (val:any, c:HookContext) => {
            if(Array.isArray(val)) context.data.$push.areas = { $each: val };
            else c.data.$push.areas = val;
            return c;
        }
        if(!areas) {
            areas = context.data.$addToSet?.areas;
            setAreas = (val:any, c:HookContext) => {
                if(Array.isArray(val)) c.data.$addToSet.areas = { $each: val };
                else c.data.$addToSet.areas = val;
                return c;
            }
            if(!areas){
                areas = context.data.$set.areas;
                setAreas = (val:any, c:HookContext) => {
                    c.data.$set.areas = val;
                    return c
                }
                /** possibly overkill - but account for set unless it's as deep as areas.index.premium.some_property */
                if(!areas && context.data.$set){
                    for(const k in context.data.$set) {
                        if(/areas\.\d+/.test(k)){
                            /** if a specific area at index is being set - can still identify existing by the index */
                            const spl = k.split('.')
                            if(spl.length === 2) {
                                areas = context.data.$set[k]
                                setAreas = (val:any, c:HookContext) => {
                                    c.data.$set[k] = val;
                                    return c;
                                }
                                getEx = (val:any, ex:any) => {
                                    return (ex?.areas || [])[spl[1]]?.premium
                                }
                            }
                            /** maximum depth - if areas.index.premium is being set. Existing premium can still be identified by the index */
                            else if(spl.length === 3 && k.split('.')[2] === 'premium'){
                                areas = { premium: context.data.$set[k] }
                                setAreas = (val:any, c:HookContext) => {
                                    c.data.$set[k] = val;
                                    return c;
                                }
                                getEx = (val:any, ex:any) => {
                                    return (ex?.areas || [])[spl[1]]?.premium
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    if(areas){
        let exRate:any = {}
        if(context.method !== 'create'){
            exRate = await loadExists(context);
            context = setExists(context, exRate);
        }
        let maxAge = 120;
        let coverage = context.params.rate_coverage;
        if(context.params.max_rate_age) maxAge = context.params.max_rate_age;
        else {
            if(!maxAge) {
                if (!coverage) {
                    let id = context.data.coverage || exRate.coverage
                    coverage = await new CoreCall('coverages', context, { skipJoins: true }).get(id);
                }
                maxAge = coverage?.maxAge
            }
        }
        const getOnePremium = async (area:any, existing:any) => {
            return await fixFixedRates(area.premium, { maxAge, existing })
        }

        if(Array.isArray(areas)){
            const premiums = await Promise.all(areas.map(a => getOnePremium(a, getEx(a, exRate))))
            context = setAreas(premiums, context);
        } else if(areas.$each){
            const premiums = await Promise.all(areas.$each.map(a => getOnePremium(a, getEx(a, exRate))))
            context = setAreas(premiums, context);
        } else {
            const premium = await getOnePremium(areas, getEx(areas, exRate))
            context = setAreas(premium, context);
        }
    }
    return context;
}
// A configure function that registers the service and its hooks via `app.configure`
export const rates = (app: Application) => {
    // Register our service on the Feathers application
    app.use(ratesPath, new RatesService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: ratesMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(ratesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(ratesExternalResolver),
                schemaHooks.resolveResult(ratesResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(ratesQueryValidator),
                schemaHooks.resolveQuery(ratesQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                fillFixedRates,
                fixAreaRates,
                schemaHooks.validateData(ratesDataValidator),
                schemaHooks.resolveData(ratesDataResolver),
                relateCoverage
            ],
            patch: [
                fillFixedRates,
                fixAreaRates,
                schemaHooks.validateData(ratesPatchValidator),
                schemaHooks.resolveData(ratesPatchResolver),
                relateCoverage
            ],
            remove: [relateCoverage]
        },
        after: {
            all: [runJoins],
            create: [relateCoverage],
            patch: [relateCoverage],
            remove: [relateCoverage]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [ratesPath]: RatesService
    }
}
