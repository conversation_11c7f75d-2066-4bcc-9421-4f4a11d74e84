// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {Rates, RatesData, RatesPatch, RatesQuery} from './rates.schema.js'

export type {Rates, RatesData, RatesPatch, RatesQuery}

export interface RatesParams extends MongoDBAdapterParams<RatesQuery> {
}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class RatesService<ServiceParams extends Params = RatesParams> extends MongoDBService<
    Rates,
    RatesData,
    RatesParams,
    RatesPatch
> {
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        Model: app.get('mongodbClient').then((db) => db.collection('rates'))
            .then((collection) => {
                collection.createIndex({stateKey: 1}, {unique: true})
                return collection;
            }),
        operators: ['$regex', '$options']
    }
}
