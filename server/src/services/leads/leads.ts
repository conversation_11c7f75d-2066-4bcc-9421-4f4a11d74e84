// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import { hooks as schemaHooks } from '@feathersjs/schema'

import {
  leadsDataValidator,
  leadsPatchValidator,
  leadsQueryValidator,
  leadsResolver,
  leadsExternalResolver,
  leadsDataResolver,
  leadsPatchResolver,
  leadsQueryResolver
} from './leads.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import { LeadsService, getOptions } from './leads.class.js'
import { leadsPath, leadsMethods } from './leads.shared.js'
import {logChange} from '../../utils/index.js';

import { allUcanAuth, CapabilityParts, noThrow } from 'feathers-ucan';
const creator = [['leads', 'WRITE']] as Array<CapabilityParts>;
const deleter = [['leads', '*']] as Array<CapabilityParts>;
const ucanArgs = {
  create: noThrow,
  patch: creator,
  update: creator,
  remove: deleter
}

export * from './leads.class.js'
export * from './leads.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const leads = (app: Application) => {
  // Register our service on the Feathers application
  app.use(leadsPath, new LeadsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: leadsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(leadsPath).hooks({
    around: {
      all: [schemaHooks.resolveExternal(leadsExternalResolver), schemaHooks.resolveResult(leadsResolver)]
    },
    before: {
      all: [
        allUcanAuth<HookContext>(ucanArgs),
        logChange(),
          schemaHooks.validateQuery(leadsQueryValidator), schemaHooks.resolveQuery(leadsQueryResolver)],
      find: [],
      get: [],
      create: [schemaHooks.validateData(leadsDataValidator), schemaHooks.resolveData(leadsDataResolver)],
      patch: [schemaHooks.validateData(leadsPatchValidator), schemaHooks.resolveData(leadsPatchResolver)],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [leadsPath]: LeadsService
  }
}
