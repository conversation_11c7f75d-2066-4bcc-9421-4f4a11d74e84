// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Leads, LeadsData, LeadsPatch, LeadsQuery } from './leads.schema.js'

export type { Leads, LeadsData, LeadsPatch, LeadsQuery }

export interface LeadsParams extends MongoDBAdapterParams<LeadsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class LeadsService<ServiceParams extends Params = LeadsParams> extends MongoDBService<
  Leads,
  LeadsData,
  LeadsParams,
  LeadsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('leads'))
  }
}
