// TypeBox schema for leads service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, PhoneSchema, ServiceAddressSchema } from '../../utils/common/typebox-schemas.js'

export const leadsSchema = Type.Object({
  _id: ObjectIdSchema(),
  firstName: Type.Optional(Type.String()),
  lastName: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(PhoneSchema),
  address: Type.Optional(ServiceAddressSchema),
  dateOfBirth: Type.Optional(Type.Any()),
  gender: Type.Optional(Type.String()),
  household: Type.Optional(ObjectIdSchema()),
  income: Type.Optional(Type.Number()),
  householdSize: Type.Optional(Type.Number()),
  source: Type.Optional(Type.String()),
  campaign: Type.Optional(Type.String()),
  referrer: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  stage: Type.Optional(Type.String()),
  priority: Type.Optional(Type.String()),
  assignedTo: Type.Optional(ObjectIdSchema()),
  notes: Type.Optional(Type.String()),
  tags: Type.Optional(Type.Array(Type.String())),
  interests: Type.Optional(Type.Array(Type.String())),
  preferences: Type.Optional(Type.Record(Type.String(), Type.Any())),
  lastContact: Type.Optional(Type.Any()),
  nextFollowUp: Type.Optional(Type.Any()),
  converted: Type.Optional(Type.Boolean()),
  convertedAt: Type.Optional(Type.Any()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type Leads = Static<typeof leadsSchema>
export const leadsValidator = getValidator(leadsSchema, dataValidator)
export const leadsResolver = resolve<Leads, HookContext>({})
export const leadsExternalResolver = resolve<Leads, HookContext>({})

export const leadsDataSchema = Type.Object({
  ...Type.Omit(leadsSchema, ['_id']).properties
}, { additionalProperties: false })

export type LeadsData = Static<typeof leadsDataSchema>
export const leadsDataValidator = getValidator(leadsDataSchema, dataValidator)
export const leadsDataResolver = resolve<LeadsData, HookContext>({})

export const leadsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(leadsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type LeadsPatch = Static<typeof leadsPatchSchema>
export const leadsPatchValidator = getValidator(leadsPatchSchema, dataValidator)
export const leadsPatchResolver = resolve<LeadsPatch, HookContext>({})

// Allow querying on any field from the main schema
const leadsQueryProperties = leadsSchema
export const leadsQuerySchema = querySyntax(leadsQueryProperties)
export type LeadsQuery = Static<typeof leadsQuerySchema>
export const leadsQueryValidator = getValidator(leadsQuerySchema, queryValidator)
export const leadsQueryResolver = resolve<LeadsQuery, HookContext>({})
