// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Drops, DropsData, DropsPatch, DropsQuery } from './drops.schema.js'

export type { Drops, DropsData, DropsPatch, DropsQuery }

export interface DropsParams extends MongoDBAdapterParams<DropsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class DropsService<ServiceParams extends Params = DropsParams> extends MongoDBService<
  Drops,
  DropsData,
  DropsParams,
  DropsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('drops'))
        .then((collection) => {
          collection.createIndex(
              {
                title: 'text',
                body: 'text',
                tags: 'text'
              },
              {
                'weights': {
                  title: 4,
                  body: 2,
                  tags: 4
                }
              }
          )
          return collection;
        })
  }
}
