// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    fbResDataValidator,
    fbResPatchValidator,
    fbResQueryValidator,
    fbResResolver,
    fbResExternalResolver,
    fbResDataResolver,
    fbResPatchResolver,
    fbResQueryResolver
} from './fb-res.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {FbResService, getOptions} from './fb-res.class.js'
import {fbResPath, fbResMethods} from './fb-res.shared.js'
import {getJoin, logChange, relate} from '../../utils/index.js';
import {allUcanAuth, CapabilityParts} from 'feathers-ucan';

export * from './fb-res.class.js'
export * from './fb-res.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['fbs', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['fbs', '*']] as Array<CapabilityParts>;
    const ucanArgs:any = {
        create: writer,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects: any = [];
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['person/owner'], '*']],
        or: '*',
        cap_subjects
    })(context) as any;
}


const runJoins = async (context: HookContext) => {
    const {with_person} = context.params.runJoin || {}
    if (with_person) return getJoin({
        herePath: 'person',
        service: 'ppls'
    })(context)
}

const relateForm = async (context: HookContext) => {
    return relate('otm', {
        herePath: 'form',
        thereService: 'fbs',
        therePath: 'responses'
    })(context)
}

// A configure function that registers the service and its hooks via `app.configure`
export const fbRes = (app: Application) => {
    // Register our service on the Feathers application
    app.use(fbResPath, new FbResService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: fbResMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(fbResPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(fbResExternalResolver),
                schemaHooks.resolveResult(fbResResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(fbResQueryValidator),
                schemaHooks.resolveQuery(fbResQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(fbResDataValidator),
                schemaHooks.resolveData(fbResDataResolver),
                relateForm
            ],
            patch: [
                schemaHooks.validateData(fbResPatchValidator),
                schemaHooks.resolveData(fbResPatchResolver),
                relateForm
            ],
            remove: [relateForm]
        },
        after: {
            all: [runJoins],
            create: [relateForm],
            patch: [relateForm],
            remove: [relateForm]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [fbResPath]: FbResService
    }
}
