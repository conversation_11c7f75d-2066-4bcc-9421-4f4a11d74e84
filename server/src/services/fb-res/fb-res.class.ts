// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { FbRes, FbResData, FbResPatch, FbResQuery } from './fb-res.schema.js'

export type { FbRes, FbResData, FbResPatch, FbResQuery }

export interface FbResParams extends MongoDBAdapterParams<FbResQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class FbResService<ServiceParams extends Params = FbResParams> extends MongoDBService<
  FbRes,
  FbResData,
  FbResParams,
  FbResPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('fb-res'))
  }
}
