// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {Providers, ProvidersData, ProvidersPatch, ProvidersQuery} from './providers.schema.js'

export type {Providers, ProvidersData, ProvidersPatch, ProvidersQuery}

export interface ProvidersParams extends MongoDBAdapterParams<ProvidersQuery> {
}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ProvidersService<ServiceParams extends Params = ProvidersParams> extends MongoDBService<
    Providers,
    ProvidersData,
    ProvidersParams,
    ProvidersPatch
> {
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        multi: true,
        Model: app.get('mongodbClient')
            .then((db) => db.collection('providers'))
            .then((collection) => {
                collection.createIndex({googlePlacesId: 1}, {unique: true});
                return collection
            }),
        operators: ['$regex', '$options', '$geoIntersects', '$geometry']

    }
}
