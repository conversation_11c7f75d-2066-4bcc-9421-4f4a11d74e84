// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    providersDataValidator,
    providersPatchValidator,
    providersQueryValidator,
    providersResolver,
    providersExternalResolver,
    providersDataResolver,
    providersPatchResolver,
    providersQueryResolver
} from './providers.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {ProvidersService, getOptions} from './providers.class.js'
import {providersPath, providersMethods} from './providers.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {logChange, geoQuery, relate, scrubUploads, findJoin} from '../../utils/index.js';
import {hackIdAfter, hackIdBefore, loadOrCreate} from '../cross-sections/utils/index.js';

export * from './providers.class.js'
export * from './providers.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['providers', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['providers', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = []
    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create') {

            const existing = await loadExists(context);
            context = setExists(context, existing);
            //allow changes before approval
            if (!existing?.approvedAt) context.params.admin_pass = true;

            if (existing) {
                const orgId = existing.org || context.data.org
                const orgNamespace = `orgs:${orgId}`;
                cap_subjects.push(orgId);
                const providerNamespace = `providers:${existing._id}`;
                cap_subjects.push(context.id)
                const providerWrite: CapabilityParts[] = [[providerNamespace, 'WRITE'], [providerNamespace, 'providerAdmin']]
                const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin']]
                for (const w of orgWrite) {
                    ucanArgs.patch.unshift(w);
                    ucanArgs.update.unshift(w);
                    ucanArgs.remove.unshift(w);
                }
                for (const w of providerWrite) {
                    ucanArgs.patch.unshift(w);
                    ucanArgs.update.unshift(w);
                    ucanArgs.remove.unshift(w);
                }
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['managers/owner'], '*']],
        cap_subjects
    })(context) as any;
}


import {searchApi} from './utils/search.js'

const setGeo = async (context: HookContext): Promise<HookContext> => {
    const {locations, geo: g} = context.result;
    if (locations) {
        const geo = {type: 'Feature', geometry: {type: 'MultiPoint', coordinates: g?.geometry?.coordinates || []}}
        if (!geo.geometry?.coordinates) geo.geometry = {...geo.geometry, coordinates: []};
        const coords = (geo.geometry.coordinates || []).map(a => JSON.stringify(a));
        const locs = locations.map(a => JSON.stringify([a.longitude, a.latitude]));

        const add: any[] = [];
        const pullIdxs: any[] = [];
        for (const ll of locs) {
            if (ll[0] && ll[1]) {
                if (!coords.includes(ll)) add.push(JSON.parse(ll));
            }
        }

        for (const crd in coords) {
            if (!locs.includes(coords[crd])) pullIdxs.push(crd);
        }
        const pl = pullIdxs.length;
        const al = add.length;
        if (pl || al) {
            for (const p of pullIdxs.sort((a, b) => b - a)) {
                geo.geometry.coordinates.splice(p, 1);
            }
            for (const a of add) {
                geo.geometry.coordinates.push(a);
            }
            context.result = await new CoreCall('providers', context).patch(context.result._id, {geo})
        }
    }
    return context;
}

const prioritizeInternal = (context: HookContext) => {
    context.params.query.$sort = {priority: -1, bundle_length: -1, ...context.params.query?.$sort};
    return context;
}

const contactPaths = [['email', 'emails'], ['phone', 'phones'], ['address', 'addresses']];
import {addContacts} from '../orgs/hooks/index.js'

const relateOrg = async (context: HookContext): Promise<HookContext> => {
    return relate('otm', {
        herePath: 'org',
        therePath: 'providerAccounts',
        thereService: 'orgs',
        paramsName: 'providerOrgRelate'
    })(context)
}

const uploadScrub = async (context: HookContext) => {
    const {provider_videos, provider_avatar, provider_images} = context.params.runJoin || {};
    const paths: string[] = [];
    if (provider_videos) {
        if (provider_videos === '*') paths.push('videos/*')
        else {
            for (let i = 0; i < provider_videos.length; i++) {
                paths.push(`videos.${provider_videos[i]}`);
            }
        }
    }
    if (provider_avatar) paths.push('avatar')
    if (provider_images) paths.push('images')
    return await scrubUploads({paths})(context)
}

const updatePatients = async (context: HookContext): Promise<HookContext> => {
    if (context.data.patientUpdate) {
        const patients = await new CoreCall('visits', context, {skipJoins: true}).find({
            query: {provider: context.id},
            paginate: false,
            pipeline: [
                // {
                //     $match: {
                //         provider: { $eq: context.id }
                //     },
                // },
                {
                    $lookup: {
                        from: 'ppls',
                        as: 'visit_patients',
                        localField: 'patient',
                        foreignField: '_id'
                    }
                },
                {
                    $unwind: '$visit_patients'
                },
                {
                    $replaceRoot: {newRoot: '$visit_patients'}
                },
                {
                    $project: {
                        _id: 1
                    }
                }
            ]
        })
        await loadOrCreate(`provider_patients:providers:${context.id}`, {patients: patients.map(a => a._id)})(context)
            .catch(err => console.error(`Error adding provider patient cross section: ${err.message}`))
    }
    return context;
}

const removeAutoCreate = (context: HookContext) => {
    if (context.data.org || context.data.$set?.org) {
        context.data.$unset = {...context.data.$unset, auto_created: ''}
    }
    return context;
}
// A configure function that registers the service and its hooks via `app.configure`
export const providers = (app: Application) => {
    // Register our service on the Feathers application
    app.use(providersPath, new ProvidersService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: providersMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(providersPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(providersExternalResolver),
                schemaHooks.resolveResult(providersResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(providersQueryValidator),
                schemaHooks.resolveQuery(providersQueryResolver),
                uploadScrub
            ],
            find: [
                geoQuery,
                hackIdBefore('plan_providers', 'plans', 'providers'),
                prioritizeInternal
            ],
            get: [],
            create: [
                schemaHooks.validateData(providersDataValidator),
                schemaHooks.resolveData(providersDataResolver),
                addContacts(contactPaths),
                relateOrg
            ],
            patch: [
                schemaHooks.validateData(providersPatchValidator),
                schemaHooks.resolveData(providersPatchResolver),
                addContacts(contactPaths),
                relateOrg,
                removeAutoCreate,
                updatePatients,
            ],
            remove: [relateOrg]
        },
        after: {
            all: [uploadScrub],
            find: [
                searchApi,
                hackIdAfter('plan_providers', 'plans')
            ],
            create: [setGeo, relateOrg],
            patch: [setGeo, relateOrg],
            remove: [relateOrg]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [providersPath]: ProvidersService
    }
}
