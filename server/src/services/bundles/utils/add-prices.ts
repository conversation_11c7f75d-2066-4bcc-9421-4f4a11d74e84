import {HookContext} from '../../../declarations.js';
import {CoreCall, loadExists, setExists} from 'feathers-ucan';
import {createParse} from '../../uploads/parser/index.js';
import {AnyObj} from '../../../utils/index.js';
import {ObjectId} from 'mongodb';
import {_set} from 'symbol-ucan';

export const priceUploads = async (context: HookContext): Promise<HookContext> => {
    const {addPrices} = context.params.runJoin || {}
    if (addPrices && context.params.file) {
        if (context.type === 'before') {
            const bundle = context.id;
            const exsts = await loadExists(context);
            context = setExists(context, exsts);
            const provider = exsts.provider;
            context.params.query = {$limit: 1000}
            const {result} = createParse(context);

            const errs: { row: number, data: any, key: string, err: string, throw?: boolean }[] = [];

            const format = (row, key, data) => {
                return {
                    'type': (val) => {
                        if (!val) {
                            errs.push({row, key, err: 'No Type', throw: true, data})
                            return undefined
                        }
                        const map = { p: 'procedures', m: 'meds' }
                        return map[val] || 'other';
                    },
                    'name': (val) => {
                        if (!val) {
                            errs.push({row, key, err: 'No Name', throw: true, data})
                            return undefined;
                        } else return val.trim()
                    },
                    'description': (val) => {
                        return val;
                    },
                    'price': (val) => {
                        return Math.floor(Number(val) / 100)
                    },
                    'code': (val) => {
                        if (!val && !data.rxcui && !data.ndc) {
                            errs.push({row, key, err: 'Need one of RXCUI, Code, or NDC', throw: true, data})
                            return undefined
                        }
                        return val.trim();
                    },
                    'rxcui': (val) => {
                        if (!val && !data.code && !data.ndc) {
                            errs.push({row, key, err: 'Need one of RXCUI, Code, or NDC', throw: true, data})
                            return undefined
                        }
                        return val.trim();
                    },
                    'ndc': (val) => {
                        if (!val && !data.code && !data.rxcui) {
                            errs.push({row, key, err: 'Need one of RXCUI, Code, or nDC', throw: true, data})
                            return undefined
                        }
                        return val.trim();
                    }
                }
            }


            const sheet = addPrices.sheet ? result.filter(a => a.name === addPrices.sheet)[0] : result[0];
            const headers = addPrices.headers;
            const headerKeys = Object.keys(headers);
            const sliceCount = addPrices.omitFirstRow ? 1 : 0;

            const getPrice = async (val, row): Promise<AnyObj> => {
                const obj = {};
                for (const key of headerKeys) {
                    obj[key] = format(row, key, val[headers[key]])[key](val[headers[key]])
                }
                return obj
            }

            const priceList = await Promise.all(sheet.data.slice(sliceCount).map((a, i) => getPrice(a, i)))

            const filteredPrices = priceList.filter((a, i) => !errs.some(b => b.row === i && b.throw));


            const existing = await new CoreCall('prices', context, {skipJoins: true}).find({
                query: {
                    $limit: filteredPrices.length,
                    provider,
                    bundle,
                    code: {$in: filteredPrices.map(a => a.code)}
                },
                admin_pass: true
            });

            const modifiedExisting: any[] = [];

            const procedure_types:any = [];
            const med_types:any = [];
            const codes:any = [];
            const rxcuis:any = [];

            for (let i = filteredPrices.length - 1; i > -1; i--) {
                let skip;
                const ft = filteredPrices[i];
                for(let j = 0; j < existing.data.length; j++) {
                    const ex = existing.data[j];
                    if (ft.code === ex.code) {
                        modifiedExisting.push({...ex, ...ft, _id: ex._id})
                        filteredPrices.splice(i, 1);
                        skip = true;
                        break;
                    }
                }
                if(!skip) {
                    if (ft.type === 'meds') {
                        med_types.push(ft);
                        rxcuis.push(ft.code);
                    } else if (ft.type === 'procedures') {
                        procedure_types.push(ft);
                        codes.push(ft.code);
                    }
                }
            }

            const subjects = {
                meds: {},
                procedures: {},
                other: {}
            }
            const procedures = await new CoreCall('procedures', context, {skipJoins: true}).find({
                query: {
                    $limit: codes.length,
                    code: { $in: codes }
                }
            })
            for(let i = 0; i < procedures.data.length; i++) {
                subjects.procedures[procedures.data[i].code] = procedures.data[i]._id;
            }
            const meds = await new CoreCall('meds', context, {skipJoins: true}).find({
                query: {
                    $limit: rxcuis.length,
                    rxcui: { $in: rxcuis }
                }
            })
            for(let i = 0; i < meds.data.length; i++) {
                subjects.meds[meds.data[i].rxcui] = meds.data[i]._id;
            }


            if (modifiedExisting.length) {
                const patchPrice = async (p) => {
                    const {_id, ...rest} = p;
                    await new CoreCall('prices', context, {skipJoins: true}).patch(_id, rest, {admin_pass: true})
                }
                await Promise.all(modifiedExisting.map(a => patchPrice(a)))
            }

            let status = 200;
            let err = '';
            let prices: any[] = [];
            if (filteredPrices?.length) prices = await new CoreCall('prices', context, {skipJoins: true}).create(filteredPrices.map(a => {
                return {
                    ...a,
                    subject: subjects[a.type][a.code] || undefined
                }
            }))
                .catch(e => {
                    err = `Error adding people: ${e.message.split('').slice(0, 50).join('')}`
                    status = 500;
                    return [];
                })
            let data = {};
            context.result = {};
            context.data = {}
            if (status === 200) {
                const addList: string[] = [...(prices || []).map(a => a._id.toHexString()), ...(existing.data || []).map(a => a._id.toHexString())];
                const data = {$addToSet: {prices: {$each: addList.map(a => ObjectId.createFromHexString(a))}}}
                context.result = await new CoreCall('bundles', context).patch(bundle as any, data, { update_bundle_price: true })
            }
            context.params.addPrices = {
                ...context.params.addPrices,
                added: prices?.length || 0,
                existing,
                errors: errs,
                updated: Date.now(),
                status,
                err,
                data
            }
        } else {
            context.result = _set(context.result, '_fastjoin.addPrices', context.params.addPrices);
        }

    }
    return context;
}
