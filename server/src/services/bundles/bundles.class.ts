// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Bundles, BundlesData, BundlesPatch, BundlesQuery } from './bundles.schema.js'

export type { Bundles, BundlesData, BundlesPatch, BundlesQuery }

export interface BundlesParams extends MongoDBAdapterParams<BundlesQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class BundlesService<ServiceParams extends Params = BundlesParams> extends MongoDBService<
  Bundles,
  BundlesData,
  BundlesParams,
  BundlesPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('bundles')),
    multi: true,
    operators: ['$regex', '$options']
  }
}
