// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import { hooks as schemaHooks } from '@feathersjs/schema'

import {
  marketsDataValidator,
  marketsPatchValidator,
  marketsQueryValidator,
  marketsResolver,
  marketsExternalResolver,
  marketsDataResolver,
  marketsPatchResolver,
  marketsQueryResolver
} from './markets.schema.js'

import type { Application } from '../../declarations.js'
import { MarketsService, getOptions } from './markets.class.js'
import { marketsPath, marketsMethods } from './markets.shared.js'

export * from './markets.class.js'
export * from './markets.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const markets = (app: Application) => {
  // Register our service on the Feathers application
  app.use(marketsPath, new MarketsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: marketsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(marketsPath).hooks({
    around: {
      all: [schemaHooks.resolveExternal(marketsExternalResolver), schemaHooks.resolveResult(marketsResolver)]
    },
    before: {
      all: [schemaHooks.validateQuery(marketsQueryValidator), schemaHooks.resolveQuery(marketsQueryResolver)],
      find: [],
      get: [],
      create: [schemaHooks.validateData(marketsDataValidator), schemaHooks.resolveData(marketsDataResolver)],
      patch: [schemaHooks.validateData(marketsPatchValidator), schemaHooks.resolveData(marketsPatchResolver)],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [marketsPath]: MarketsService
  }
}
