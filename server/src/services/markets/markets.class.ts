// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Markets, MarketsData, MarketsPatch, MarketsQuery } from './markets.schema.js'

export type { Markets, MarketsData, MarketsPatch, MarketsQuery }

export interface MarketsParams extends MongoDBAdapterParams<MarketsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class MarketsService<ServiceParams extends Params = MarketsParams> extends MongoDBService<
  Markets,
  MarketsData,
  MarketsParams,
  MarketsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('markets'))
  }
}
