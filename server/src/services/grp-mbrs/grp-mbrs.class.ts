// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {GrpMbrs, GrpMbrsData, GrpMbrsPatch, GrpMbrsQuery} from './grp-mbrs.schema.js'

export type {GrpMbrs, GrpMbrsData, GrpMbrsPatch, GrpMbrsQuery}

export interface GrpMbrsParams extends MongoDBAdapterParams<GrpMbrsQuery> {
}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class GrpMbrsService<ServiceParams extends Params = GrpMbrsParams> extends MongoDBService<
    GrpMbrs,
    GrpMbrsData,
    GrpMbrsParams,
    GrpMbrsPatch
> {
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        multi: true,
        operators: ['$regex', '$options'],
        Model: app.get('mongodbClient').then((db) => db.collection('grp-mbrs'))
            .then((collection) => {
                collection.createIndex({mbrId: 1}, {unique: true});
                return collection
            })
    }
}
