// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    grpMbrsDataValidator,
    grpMbrsPatchValidator,
    grpMbrsQueryValidator,
    grpMbrsResolver,
    grpMbrsExternalResolver,
    grpMbrsDataResolver,
    grpMbrsPatchResolver,
    grpMbrsQueryResolver
} from './grp-mbrs.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {GrpMbrsService, getOptions} from './grp-mbrs.class.js'
import {grpMbrsPath, grpMbrsMethods} from './grp-mbrs.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, loadExists, noThrow, setExists} from 'feathers-ucan';
import {getJoin, logChange} from '../../utils/index.js';

export * from './grp-mbrs.class.js'
export * from './grp-mbrs.schema.js'

const authenticate = async (context: HookContext) => {
    // Allow explicit special changes for special_change
    const creator = [['grp-mbrs', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['grp-mbrs', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        find: noThrow,
        create: noThrow,
        patch: [...creator],
        update: [...creator],
        remove: deleter
    }
    let orgId = context.data?.org
    if (context.method === 'patch') {
        const ex = await loadExists(context);
        context = setExists(context, ex);
        orgId = ex.org
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        loginPass: [[['_id/owner'], '*']],
        adminPass: ['patch', 'create', 'remove'],
        or: '*',
        specialChange: context.params.specialChange,
        cap_subjects: orgId ? [orgId] : []
    })(context) as any;
}

const addId = (context: HookContext) => {
    context.data.mbrId = `${context.data.group}:${context.data.person}`
    return context;
}

const runJoins = async (context: HookContext) => {
    const { with_person } = context.params.runJoin || {};
    if(with_person) return getJoin({herePath: 'person', service: 'ppls'})(context)
    return context;
}

const protectOrgAndGroup = (context: HookContext) => {
    if(Array.isArray(context.data)) return context;
    const obj = { ...context.data, ...context.data.$set, ...context.data.$unset}
    if (obj.org || obj.group) throw new Error('Cannot change the org or group for a group member');
    return context;
}

const removeIn = async (context: HookContext) => {
    const { person, org, group } = context.result;
    const otherMatches = await new CoreCall('grp-mbrs', context, {skipJoins: true}).find({query: {person, org, $limit: 1}})
        .catch(err => console.error(`Error checking for other group matches for person ${person}: ${err.message}`))
    const patchObj:any = {$pull: {inGroups: group}}
    if(!otherMatches.total) patchObj.$pull.inOrgs = org;
    await new CoreCall('ppls', context).patch(person, patchObj, {admin_pass: true, skip_hooks: true})
        .catch(err => console.error(`Error removing group from person ${person}: ${err.message}`))
    return context;
}

const joinMbr = (mbr:any) => {
    return async (context: HookContext) => {
        await new CoreCall('ppls', context).patch(mbr.person, {
            $addToSet: {
                inGroups: mbr.group,
                inOrgs: mbr.org
            }
        }, {admin_pass: true, skip_hooks: true})
            .catch(err => {
                console.error(`Error adding group to person ${mbr.person}: ${err.message}`)
            })
        return context;
    }
}

const joinMbrs = async (context: HookContext) => {
    if(Array.isArray(context.result)) {
        await Promise.all(context.result.map(a => joinMbr(a)(context)))
    } else await joinMbr(context.result)(context)
    return context;
}

const getProfile = async (context: HookContext) => {
    if(Array.isArray(context.data)) return context;
    if(context.data.name && context.data.email) return context;
    const person = await new CoreCall('ppls', context).get(context.data.person)
        .catch(err => console.error(`Error getting person for group member: ${err.message}`))
    if(person){
        context.data.name = person.name;
        context.data.email = person.email;
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const grpMbrs = (app: Application) => {
    // Register our service on the Feathers application
    app.use(grpMbrsPath, new GrpMbrsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: grpMbrsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(grpMbrsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(grpMbrsExternalResolver),
                schemaHooks.resolveResult(grpMbrsResolver)]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(grpMbrsQueryValidator),
                schemaHooks.resolveQuery(grpMbrsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                addId,
                getProfile,
                schemaHooks.validateData(grpMbrsDataValidator),
                schemaHooks.resolveData(grpMbrsDataResolver)
            ],
            patch: [
                protectOrgAndGroup,
                schemaHooks.validateData(grpMbrsPatchValidator),
                schemaHooks.resolveData(grpMbrsPatchResolver)
            ],
            remove: []
        },
        after: {
            all: [runJoins],
            create: [joinMbrs],
            remove: [removeIn]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [grpMbrsPath]: GrpMbrsService
    }
}
