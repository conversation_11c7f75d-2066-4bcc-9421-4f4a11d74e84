// TypeBox schema for grp-mbrs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const grpMbrsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
,
  // Missing fields from old schema
  group: Type.Optional(ObjectIdSchema()),
  person: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  mbrId: Type.Optional(ObjectIdSchema()),
}, { additionalProperties: false })

export type GrpMbrs = Static<typeof grpMbrsSchema>
export const grpMbrsValidator = getValidator(grpMbrsSchema, dataValidator)
export const grpMbrsResolver = resolve<GrpMbrs, HookContext>({})
export const grpMbrsExternalResolver = resolve<GrpMbrs, HookContext>({})

export const grpMbrsDataSchema = Type.Object({
  ...Type.Omit(grpMbrsSchema, ['_id']).properties
}, { additionalProperties: false })

export type GrpMbrsData = Static<typeof grpMbrsDataSchema>
export const grpMbrsDataValidator = getValidator(grpMbrsDataSchema, dataValidator)
export const grpMbrsDataResolver = resolve<GrpMbrsData, HookContext>({})

export const grpMbrsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(grpMbrsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type GrpMbrsPatch = Static<typeof grpMbrsPatchSchema>
export const grpMbrsPatchValidator = getValidator(grpMbrsPatchSchema, dataValidator)
export const grpMbrsPatchResolver = resolve<GrpMbrsPatch, HookContext>({})

// Allow querying on any field from the main schema
const grpMbrsQueryProperties = grpMbrsSchema
export const grpMbrsQuerySchema = querySyntax(grpMbrsQueryProperties)
export type GrpMbrsQuery = Static<typeof grpMbrsQuerySchema>
export const grpMbrsQueryValidator = getValidator(grpMbrsQuerySchema, queryValidator)
export const grpMbrsQueryResolver = resolve<GrpMbrsQuery, HookContext>({})
