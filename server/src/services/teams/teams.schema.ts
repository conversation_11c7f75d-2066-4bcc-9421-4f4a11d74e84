// TypeBox schema for teams service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const teamsSchema = Type.Object({
  _id: ObjectIdSchema(),
  org: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  department: Type.Optional(Type.String()),
  members: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  leads: Type.Optional(Type.Array(ObjectIdSchema())),
  permissions: Type.Optional(Type.Array(Type.String())),
  roles: Type.Optional(Type.Array(Type.String())),
  projects: Type.Optional(Type.Array(ObjectIdSchema())),
  budget: Type.Optional(Type.Number()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type Teams = Static<typeof teamsSchema>
export const teamsValidator = getValidator(teamsSchema, dataValidator)
export const teamsResolver = resolve<Teams, HookContext>({})
export const teamsExternalResolver = resolve<Teams, HookContext>({})

export const teamsDataSchema = Type.Object({
  ...Type.Omit(teamsSchema, ['_id']).properties
}, { additionalProperties: false })

export type TeamsData = Static<typeof teamsDataSchema>
export const teamsDataValidator = getValidator(teamsDataSchema, dataValidator)
export const teamsDataResolver = resolve<TeamsData, HookContext>({})

export const teamsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(teamsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type TeamsPatch = Static<typeof teamsPatchSchema>
export const teamsPatchValidator = getValidator(teamsPatchSchema, dataValidator)
export const teamsPatchResolver = resolve<TeamsPatch, HookContext>({})

// Allow querying on any field from the main schema
const teamsQueryProperties = teamsSchema
export const teamsQuerySchema = querySyntax(teamsQueryProperties)
export type TeamsQuery = Static<typeof teamsQuerySchema>
export const teamsQueryValidator = getValidator(teamsQuerySchema, queryValidator)
export const teamsQueryResolver = resolve<TeamsQuery, HookContext>({})
