// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Teams, TeamsData, TeamsPatch, TeamsQuery } from './teams.schema.js'

export type { Teams, TeamsData, TeamsPatch, TeamsQuery }

export interface TeamsParams extends MongoDBAdapterParams<TeamsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class TeamsService<ServiceParams extends Params = TeamsParams> extends MongoDBService<
  Teams,
  TeamsData,
  TeamsParams,
  TeamsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('teams')),
    operators: ['$regex', '$options']
  }
}
