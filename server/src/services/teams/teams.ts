// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    teamsDataValidator,
    teamsPatchValidator,
    teamsQueryValidator,
    teamsResolver,
    teamsExternalResolver,
    teamsDataResolver,
    teamsPatchResolver,
    teamsQueryResolver
} from './teams.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {TeamsService, getOptions} from './teams.class.js'
import {teamsPath, teamsMethods} from './teams.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, loadExists, setExists} from 'feathers-ucan';
import {logChange, relate} from '../../utils/index.js';

export * from './teams.class.js'
export * from './teams.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['refs', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['refs', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = []
    let ex: any = {host: context.data?.host};

    if(['patch', 'remove'].includes(context.method)){
        ex = await loadExists(context);
        context = setExists(context, ex);
        cap_subjects.push(ex.host)
        const hostNamespace = `hosts:${ex.host}`
        const hostWrite:CapabilityParts[] = [[hostNamespace, 'hostAdmin'], [hostNamespace, 'teamAdmin']]
        for(const w of hostWrite){
            ucanArgs.patch.unshift(w);
        }
        const teamNamespace = `teams:${ex._id}`
        const teamWrite:CapabilityParts[] = [[teamNamespace, 'WRITE'], [teamNamespace, 'teamAdmin']];
        for(const w of teamWrite){
            ucanArgs.patch.unshift(w);
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        creatorPass: ['*'],
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['person/owner'], '*']],
        cap_subjects,
        or: '*'
    })(context) as any;
}

const relateHost = async (context: HookContext) => {
    return relate('otm', {
        herePath: 'host',
        therePath: 'teams',
        thereService: 'hosts',
        paramsName: 'relateTeamHost'
    })(context)
        .catch(err => {
            console.log(`Error relating team host: ${err.message}`)
            return context;
        })
}

// A configure function that registers the service and its hooks via `app.configure`
export const teams = (app: Application) => {
    // Register our service on the Feathers application
    app.use(teamsPath, new TeamsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: teamsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(teamsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(teamsExternalResolver),
                schemaHooks.resolveResult(teamsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(teamsQueryValidator),
                schemaHooks.resolveQuery(teamsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(teamsDataValidator),
                schemaHooks.resolveData(teamsDataResolver),
                relateHost
            ],
            patch: [
                schemaHooks.validateData(teamsPatchValidator),
                schemaHooks.resolveData(teamsPatchResolver),
                relateHost
            ],
            remove: [relateHost]
        },
        after: {
            all: [],
            create: [relateHost],
            patch: [relateHost],
            remove: [relateHost]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [teamsPath]: TeamsService
    }
}
