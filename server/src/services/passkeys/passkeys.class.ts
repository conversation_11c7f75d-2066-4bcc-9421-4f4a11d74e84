// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Passkeys, PasskeysData, PasskeysPatch, PasskeysQuery } from './passkeys.schema.js'

export type { Passkeys, PasskeysData, PasskeysPatch, PasskeysQuery }

export interface PasskeysParams extends MongoDBAdapterParams<PasskeysQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class PasskeysService<ServiceParams extends Params = PasskeysParams> extends MongoDBService<
  Passkeys,
  PasskeysData,
  PasskeysParams,
  PasskeysPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('passkeys'))
        .then((collection) => {
          collection.createIndex({credentialId: 1}, {unique: true});
          return collection
        })
  }
}
