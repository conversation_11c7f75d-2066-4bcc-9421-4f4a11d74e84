// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    shopsDataValidator,
    shopsPatchValidator,
    shopsQueryValidator,
    shopsResolver,
    shopsExternalResolver,
    shopsDataResolver,
    shopsPatchResolver,
    shopsQueryResolver
} from './shops.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {ShopsService, getOptions} from './shops.class.js'
import {shopsPath, shopsMethods} from './shops.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, loadExists, noThrow, setExists} from 'feathers-ucan';
import {getJoin, logChange, addSessionFp, relate, scrub} from '../../utils/index.js';
import {runCostSim} from './sim/cost-sim.js';
import {shopChat, vectorizeShop} from './chat/shop-chat.js';

export * from './shops.class.js'
export * from './shops.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['shops', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['shops', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        get: noThrow,
        find: noThrow,
        create: noThrow,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };

    if (context.method === 'patch') {

        const existing = await loadExists(context, {params: {runJoin: {shop_person: true}}});
        context = setExists(context, existing);
        if (!existing?._fastjoin?.person?.login) {
            ucanArgs.patch = noThrow
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        or: '*'
    })(context)
}

const runJoins = async (context: HookContext) => {
    if (context.params.runJoin?.shop_person) return getJoin({service: 'ppls', herePath: 'person'})(context)
    return context;
}
const checkPolicy = (context: HookContext) => {
    const other = {'policy': 'coverage', 'coverage': 'policy'}
    for (const k of ['policy', 'coverage']) {
        if (context.data[k] || (context.data.$set || {})[k]) {
            context.data.$unset = {...context.data.$unset, [other[k]]: ''}
        }
    }
    return context;
}

const addPrints = (context: HookContext) => {
    if(context.data.updatedBy?.fingerprint){
        if(context.data.$addToSet) context.data.$addToSet.fingerprints = context.data.updatedBy.fingerprint;
        else context.data.fingerprints = [context.data.updatedBy.fingerprint];
    }
    return context;
}

/** Sync the selected policy or coverage with the enrollment record */
const syncEnrollment = async (context: HookContext) => {
    if (context.result.enrollment && context.result.plan_coverage) {
        if (context.data.policy || context.data.$set?.policy) {
            await new CoreCall('enrollments', context).patch(context.result.enrollment, {
                $set: {[`coverages.${context.result.plan_coverage}.policy`]: context.result.policy},
                $unset: {[`coverages.${context.result.plan_coverage}.individual_coverage`]: ''}
            })
                .catch(err => console.log(`Failed to sync enrollment: ${context.result.enrollment} with shop policy ${context.result._id}: ${err.message}`))
        } else if (context.data.coverage || context.data.$set?.coverage) {
            await new CoreCall('enrollments', context).patch(context.result.enrollment, {
                $set: {[`coverages.${context.result.plan_coverage}.individual_coverage`]: context.result.coverage},
                $unset: {[`coverages.${context.result.plan_coverage}.policy`]: ''}
            })
                .catch(err => console.log(`Failed to sync enrollment: ${context.result.enrollment} with shop coverage ${context.result._id}: ${err.message}`))
        }
    }
    return context;
}

const relateErmt = async (context: HookContext) => {
    return relate('oto', {
        herePath: 'enrollment',
        therePath: 'shop',
        thereService: 'enrollments',
        paramsName: 'shopEnrollment'
    })(context)
}

const scrubFields = ['comments'];


// A configure function that registers the service and its hooks via `app.configure`
export const shops = (app: Application) => {
    // Register our service on the Feathers application
    app.use(shopsPath, new ShopsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: shopsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(shopsPath).hooks({
        around: {
            all: [schemaHooks.resolveExternal(shopsExternalResolver), schemaHooks.resolveResult(shopsResolver)]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(shopsQueryValidator),
                schemaHooks.resolveQuery(shopsQueryResolver)
            ],
            find: [],
            get: [runCostSim()],
            create: [
                schemaHooks.validateData(shopsDataValidator),
                schemaHooks.resolveData(shopsDataResolver),
                addPrints,
                relateErmt,
                addSessionFp,
                scrub(scrubFields)

            ],
            patch: [
                checkPolicy,
                schemaHooks.validateData(shopsPatchValidator),
                schemaHooks.resolveData(shopsPatchResolver),
                addPrints,
                relateErmt,
                addSessionFp,
                scrub(scrubFields)

            ],
            remove: [relateErmt]
        },
        after: {
            all: [
                runJoins
            ],
            get: [shopChat],
            create: [relateErmt],
            patch: [syncEnrollment, relateErmt, vectorizeShop],
            remove: [relateErmt]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [shopsPath]: ShopsService
    }
}
