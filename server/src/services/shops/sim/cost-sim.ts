import {HookContext} from '../../../declarations.js';
import {cmsMarketplaceGet, cmsMarketplaceSearch} from '../../marketplace/cms/index.js';
import {CoreCall} from 'feathers-ucan';
import {getCoverageRate} from '../../enrollments/utils/index.js';
import {pointToGeo, fakeId} from '../../../utils/index.js';
import {ObjectId} from 'mongodb';
import {quickTaxTotal} from '../../../utils/tax/tax-calcs.js';
// import {category_weights, CoCategories} from 'src/services/marketplace/utils/index.ts';
import {Limit, getPrivateOop, getAcaOop, simulateHealthcareSpend, Distribution} from './utils.js'
import { costData, distributionKeys } from './cost-models/major-medical.js'

export const getPeopleSignature = (people:any[]):string => {
    const arr = [...people]
    const relationSignature = arr.map(a => a.relation || (a.child ? 'child' : 'spouse')).sort((a, b) => b.localeCompare(a)).join('|')
    const ageTotal = arr.map(a => Number(a.age || 0)).reduce((a, b) => a + b);
    const genderSignature = arr.map(a => a.gender).sort((a, b) => b.localeCompare(a)).join('|')
    const inactiveSignature = arr.map(a => !!a.inactive).sort((a, b) => String(b).localeCompare(String(a))).join('|')
    return `${relationSignature}-${ageTotal}-${genderSignature}-${inactiveSignature}`

}
export const getResetId = (shot:any) => {
    const { age, people, risk, smoker, place, gender, income, inactive } = shot?.stats || {}
    const peopleSignature = getPeopleSignature([{age, gender, smoker, inactive, relation: 'self' }, ...people || []])
    const fips = place?.countyfips || ''
    return `${peopleSignature}:${fips}:${risk}:${income}`
}



/**
 * RUN COST SIM
 *
 * Configuration options for runJoin.cost_sim:
 * @param {Object} data - Base data for the simulation, will be merged with shop data if available
 * @param {Object} limit - Limits for the simulation results
 * @param {Object} household - Household information containing people with age and child status
 * @param {number} risk - Risk factor for cost simulation (affects spending calculations)
 * @param {Array} exclude_issuers - Array of issuer IDs to exclude from the simulation
 * @param {Array} cms_issuers - Array of CMS issuer IDs to include in the simulation
 * @param {boolean} cms_plans - Whether to include CMS marketplace plans in the simulation
 * @param {boolean} ichra - If true, only include major medical (mm) plans; otherwise include health share (hs) plans too
 * @param {boolean} showPublic - If true, include public plans even when a specific plan is selected
 */
export const runCostSim = () => {
    return async (context: HookContext) => {
        const {cost_sim} = context.params.runJoin || {};
        if (cost_sim) {
            let shopData = cost_sim.data;
            if (context.id !== fakeId) {
                const shop = await new CoreCall('shops', context).get(context.id as any, {admin_pass: true})
                    .catch(err => console.log(`Could not get shop for cost sim: ${err.message}`))
                if (shop) shopData = {...shopData, ...shop};
            }
            let plan: any = {};
            if (shopData.plan) {
                const pl = await new CoreCall('plans', context).get(shopData.plan, {admin_pass: true})
                    .catch(err => console.log(`Could not get plan for cost sim: ${err.message}`))
                if (pl) plan = pl;
            }
            const {limit, household, risk, exclude_issuers, cms_issuers, pass_private_policies, static_plans} = cost_sim;
            context.params.quick_quote = {limit}
            if (cms_issuers?.length) context.params.query.filter = {
                ...context.params.query.filter,
                issuers: cms_issuers
            }
            context.params.query = {...context.params.query, $limit: 60}
            const ctx = cost_sim.cms_plans || !plan.ale ? await cmsMarketplaceSearch(context) : {
                result: {
                    limit_remaining: 1000,
                    gold: {data: []},
                    silver: {data: []},
                    bronze: {data: []}
                }
            };

            let private_policies = { data: (pass_private_policies || []).sort((a, b) => (b.fortyPremium || Infinity) - a.fortyPremium)
            };
            if(!pass_private_policies) {

                const zip = context.params.query.place.zipcode
                const query: any = {
                    $or: [{geo: {$exists: false}}],
                    type: {$in: cost_sim.ichra ? ['mm'] : ['hs', 'mm']},
                    sim: true,
                    group_sim_only: {$ne: true},
                    public: true,
                    $limit: 25,
                    $sort: {fortyPremium: 1},
                    fortyPremium: {$exists: true}
                }
                if (cost_sim.compare_ids) {
                    delete query.group_sim_only
                }
                if (plan._id) {
                    if (!cost_sim.showPublic) delete query.public;
                    if(Object.keys(plan.coverages || {}).length) query._id = {$in: Object.keys(plan.coverages || {}).map(a => ObjectId.createFromHexString(a))}
                }
                if (exclude_issuers) query.carrierName = {$nin: exclude_issuers}
                const drawers = await new CoreCall('junk-drawers', context).find({
                    query: {
                        $limit: 1,
                        itemId: `zips|${zip.substring(0, 3)}`
                    }
                })
                    .catch(err => {
                        console.log(`Error finding cost sim drawer: ${err.message}`)
                        return {}
                    })
                if (drawers.total) {
                    const getObj = (z: any, tries = 0) => {
                        const obj = drawers.data[0].data[z];
                        if (obj && typeof obj === 'object') return obj;
                        else if (tries < 50) return getObj(Number(z) + 1, tries + 1)
                        else return {}
                    }
                    let obj = getObj(zip, 0);
                    query.$or.push({['geo.geometry']: {$geoIntersects: {$geometry: pointToGeo(obj.lngLat)?.geometry}}})
                }

                private_policies = await new CoreCall('coverages', context)._find({
                    skip_hooks: true, admin_pass: true,
                    query
                })
            }

            let children = 0;
            let spouse = -1;
            const enrolled: any = [];
            for (const prsn of household.people) {
                if(prsn.inactive) continue;
                if (prsn.child || spouse > 0) {
                    children++
                    enrolled.push({age: prsn.age, relation: 'child'})
                } else {
                    enrolled.push({age: prsn.age, relation: spouse > -1 ? 'spouse' : 'self'})
                    spouse++
                }
            }

            const key = children ? spouse > 0 ? 'family' : `plus_children${children === 1 ? '' : children > 2 ? '__3' : '__2'}` : spouse > 0 ? 'plus_spouse' : 'single' as any

            const loaded_coverages: any = cost_sim.coverages || [];

            for (let i = 0; i < 10; i++) {
                if ((ctx.result.gold.data || [])[i]?.premium) loaded_coverages.push(ctx.result.gold.data[i])
                if ((ctx.result.silver.data || [])[i]?.premium) loaded_coverages.push(ctx.result.silver.data[i])
                if ((ctx.result.bronze.data || [])[i]?.premium) loaded_coverages.push(ctx.result.bronze.data[i])
            }

            for (let i = 0; i < 25; i++) {
                if (private_policies.data[i]) loaded_coverages.push(private_policies.data[i])
            }



            if (cost_sim.compare_ids) {
                const search_ids: any[] = [];
                const coverageIds = loaded_coverages.map(a => a._id || a.id);
                for (const id of cost_sim.compare_ids) {
                    if (!coverageIds.includes(id)) search_ids.push(id)
                }
                if (search_ids.length) {
                    const got = await cmsMarketplaceGet(search_ids, household, context.params.query.place || household.place)(context)
                        .catch(err => {
                            console.log(`Error getting compare ids for cost sim: ${err.message}`)
                            return []
                        })
                    for (const cov of got) loaded_coverages.push(cov)
                }
            }

            let coverages = static_plans;
            if(!coverages?.length) {
                coverages = [];
                for (const cov of loaded_coverages) {
                    coverages.push(cov);
                }
            }


            type CoverageScores = {
                [key: string]: {
                    top1: number, //had the lowest total spend
                    top3: number, //had top3 lowest spend
                    last: number, //had the worst spend
                    pr: number, //best outcome for this coverage (range min)
                    pr_spend: number, //spend total on best outcome
                    pw: number, //worst outcome for this coverage (range max)
                    pw_spend: number, //spend for worst outcome for this coverage
                    premium: number, //premium for this coverage
                    aptc_eligible_premium: number, //premium for this coverage
                    average: number, //average outcome
                    median: number, //median outcome
                    aptc: number,
                    spend: number,
                    oop: number,
                    oop_ptc: number,
                    total: number,
                    total_ptc: number,
                    premium_ptc: number,
                    tax_savings: number,
                    moop: Limit,
                    deductible: Limit,
                    coinsurance: number
                }
            }
            const coverage_scores: CoverageScores = {}
            const coverage_scores_ptc: CoverageScores = {}

            const distribution: Distribution = {'*': {}}
            const dcount: Distribution = {'*': {}}
            const distribution_ptc: Distribution = {'*': {}}
            const dcount_ptc: Distribution = {'*': {}}
            for (let i = 0; i < distributionKeys.length; i++) {
                distribution[String(distributionKeys[i])] = {};
                dcount[String(distributionKeys[i])] = {};
                distribution_ptc[String(distributionKeys[i])] = {};
                dcount_ptc[String(distributionKeys[i])] = {};
            }

            const coverage_limits: { [key: string]: { moop: Limit, deductible: Limit, coinsurance: number } } = {}
            const coverage_premiums: { [key: string]: number } = {}
            /** set coverage_scores objects beginning with each premium */
            for (let i = 0; i < coverages.length; i++) {
                const coverage = coverages[i];
                coverage_limits[coverage._id] = coverage.acaPlan ? getAcaOop({policy: coverage}) : getPrivateOop({policy: coverage});
                const premium = getCoverageRate({coverage, enrolled}) * 12
                coverage_premiums[coverage._id] = premium;
                const aptc = coverage.acaPlan ? Math.min((ctx.result.aptc || 0) * 12, premium) : 0
                const eligible = coverage.acaPlan ? Math.min((coverage.aptc_eligible_premium || Infinity) * 12, premium) : 0
                coverage_scores[coverage._id] = {...coverage_scores[coverage._id], premium}
                coverage_scores[coverage._id].aptc = aptc
                coverage_scores[coverage._id].aptc_eligible_premium = eligible

                /**PTC init */
                coverage_scores_ptc[coverage._id] = {...coverage_scores_ptc[coverage._id], premium}
                coverage_scores_ptc[coverage._id].aptc = aptc
                coverage_scores_ptc[coverage._id].aptc_eligible_premium = eligible

            }

            const simsCount = 100
            let allSpend = 0;
            const byYear: number[] = []

            const spendList: Array<number> = []
            /** run sims */
            for (let i = 0; i < simsCount; i++) {
                const {spend, byCoverageId} = simulateHealthcareSpend({
                    household,
                    coverages,
                    key,
                    risk,
                    coverage_limits,
                    costData
                })

                allSpend += spend;
                byYear.push(spend);
                let spent = false;
                for (let s = 0; s <= spendList.length; s++) {
                    if (spend < spendList[s]) {
                        spendList.splice(s, 0, spend);
                        spent = true;
                        break;
                    }
                }
                if (!spent) spendList.push(spend);


                let distribution_key = '*';
                for (let di = 0; di < distributionKeys.length; di++) {
                    if (spend <= distributionKeys[di]) {
                        distribution_key = String(distributionKeys[di])
                        break;
                    }
                }

                const best_worst: Array<[string, number]> = [];
                const best_worst_ptc: Array<[string, number]> = [];
                for (const k in byCoverageId) {
                    const oop = byCoverageId[k];
                    const total = oop + coverage_scores[k].premium;
                    const total_ptc = oop + Math.max(coverage_scores[k].premium - Math.min(coverage_scores[k].aptc, coverage_scores[k].aptc_eligible_premium), 0);

                    /** Log distribution for later setting averages */
                    distribution[distribution_key][k] = (distribution[distribution_key][k] || 0) + total;
                    dcount[distribution_key][k] = (dcount[distribution_key][k] || 0) + 1;
                    distribution_ptc[distribution_key][k] = (distribution_ptc[distribution_key][k] || 0) + total_ptc;
                    dcount_ptc[distribution_key][k] = (dcount_ptc[distribution_key][k] || 0) + 1;

                    /** AVERAGE & LOGS */
                    coverage_scores[k].average = (coverage_scores[k].average || 0) + total
                    /** PTC AVERAGE & LOGS */
                    coverage_scores_ptc[k].average = (coverage_scores_ptc[k].average || 0) + total_ptc
                    /** END AVERAGE & LOGS */

                    /** LOG SPEND TOTALS */
                    best_worst.push([k, total])
                    best_worst_ptc.push([k, total_ptc])

                }

                /** Loop through and set ranks by ID to avoid looping through to find rank index on every key */
                const ranks: any = {};
                const ranks_ptc: any = {};

                for (let idx = 0; idx < best_worst.sort((a, b) => a[1] - b[1]).length; idx++) {
                    ranks[best_worst[idx][0]] = idx
                }
                for (let idx = 0; idx < best_worst_ptc.sort((a, b) => a[1] - b[1]).length; idx++) {
                    ranks_ptc[best_worst_ptc[idx][0]] = idx
                }

                /** Loop back through keys and set scores according to rank */
                for (const k in byCoverageId) {
                    const rank = ranks[k];
                    const rank_ptc = ranks_ptc[k];

                    const ten_percent = Math.max(3, Math.ceil(best_worst.length * .1))
                    /** Top 3 values */

                    if (rank < ten_percent) {
                        coverage_scores[k].top3 = (coverage_scores[k].top1 || 0) + 1
                        if (rank === 0) {
                            coverage_scores[k].top1 = (coverage_scores[k].top1 || 0) + 1
                        }
                    }


                    if (rank_ptc < ten_percent) {
                        coverage_scores_ptc[k].top3 = (coverage_scores_ptc[k].top1 || 0) + 1

                        /** Top 3 PTC */
                        if (rank_ptc === 0) {
                            coverage_scores_ptc[k].top1 = (coverage_scores_ptc[k].top1 || 0) + 1
                        }
                    }
                    /** END Top 3 */

                    /** Last */

                    const last3 = best_worst.length - ten_percent;
                    if (rank >= last3) {
                        coverage_scores[k].last = (coverage_scores[k].last || 0) + 1
                    }

                    if (rank_ptc >= last3) {
                        coverage_scores_ptc[k].last = (coverage_scores_ptc[k].last || 0) + 1
                    }

                }

            }
            /**loop through distribution keys and average total*/
            for (const breakpoint in distribution) {
                for (const k in distribution[breakpoint]) {
                    if (isNaN(distribution[breakpoint][k])) {
                        distribution[breakpoint][k] = 0;
                    } else distribution[breakpoint][k] = Math.round(distribution[breakpoint][k] / dcount[breakpoint][k])
                }
                for (const k in distribution_ptc[breakpoint]) {
                    if (isNaN(distribution_ptc[breakpoint][k])) {
                        distribution_ptc[breakpoint][k] = 0;
                    } else distribution_ptc[breakpoint][k] = Math.round(distribution_ptc[breakpoint][k] / dcount_ptc[breakpoint][k])

                }
            }
            /**loop through and divide average total by sims for actual average */
            for (const k in coverage_scores) {
                coverage_scores[k].average = Math.round(coverage_scores[k].average / simsCount);
                coverage_scores_ptc[k].average = Math.round(coverage_scores_ptc[k].average / simsCount);
            }

            const defSpend = () => {
                return {spend: 0, count: 0}
            }
            const spend_dist = {
                '1': defSpend(),
                '5': defSpend(),
                '10': defSpend(),
                '20': defSpend(),
                '50': defSpend(),
                '0': defSpend()
            };
            const spendListLength = spendList.length;
            for (const k of [1, 5, 10, 20, 50]) {
                const percent = Math.ceil(spendListLength * (k / 100))
                const arr = spendList.slice(-percent)
                spend_dist[String(k)] = {spend: Math.round(arr.reduce((acc, v) => v + acc)), count: arr.length}
            }
            spend_dist['0'] = {
                count: spendListLength - spend_dist['50'].count,
                spend: allSpend - spend_dist['50'].spend
            }

            const spend = allSpend / simsCount;

            // const getSpend = (spend:number, pKey:string, { moop, coinsurance, deductible, familyAcc }:{moop:Limit, deductible:Limit, familyAcc:number, coinsurance?:number }) => {
            //     if (pKey === 'single') {
            //         const ded = Math.min(spend, deductible.single || deductible.family)
            //         const coins = (spend - ded) * (Number(coinsurance || 0) <= 1 ? Number(coinsurance) : 0)
            //         return Math.min(moop.single || moop.family, ded + coins)
            //     } else {
            //         const spd = spend;
            //         const familyDed = Math.max(0, deductible.family - (familyAcc || 0));
            //         const ded = Math.min(spd, deductible.single, familyDed)
            //         const coins = (spd - ded) * (Number(coinsurance) <= 1 ? Number(coinsurance) : 0)
            //         const familyMoop = (moop.family || moop.single) - (familyAcc || 0);
            //         return Math.max(0, Math.min(ded + coins, (moop.single || moop.family), familyMoop))
            //     }
            // }
            const isIchra = !!(shopData.gps || plan.hra?.ichra?.active)
            let tax_rate = isIchra ? (cost_sim.tax_rate || 0) : 0;
            if(isIchra) {
                try {
                    const hh_members = household.people
                    const tax_obj = quickTaxTotal({
                        income: household.income,
                        hh_members: hh_members.map(a => {
                            return {
                                age: a.age,
                                dependent: a.child || a.age < 18,
                                dob: a.dob
                            }
                        }),
                        filing_as: hh_members.some(a => a.relation === 'spouse') ? 'mj' : hh_members.length > 1 ? 'hh' : 's'
                    })
                    tax_rate = Math.max(0, tax_obj.rate);
                    if (isNaN(tax_rate)) tax_rate = .06;
                } catch (e: any) {
                    console.log(`Error calculating tax rate in cost sim: ${e.message}`)
                }
            }
            /** add deductible and oop @ average spend*/
            for (let i = 0; i < coverages.length; i++) {
                let use_tax_rate = tax_rate + 0;
                if(coverages[i].type === 'hs') use_tax_rate = 0;
                const premium = coverage_premiums[coverages[i]._id];
                const tax_savings = Math.max(0, premium * (use_tax_rate + .153))
                const premium_ptc = coverages[i].off_exchange ? premium : Math.max(premium - (ctx.result.aptc || 0) * 12, 0);
                const average = coverage_scores[coverages[i]._id].average
                const total = average - tax_savings;
                const total_ptc = coverage_scores_ptc[coverages[i]._id].average;
                coverage_scores[coverages[i]._id] = {
                    ...coverage_scores[coverages[i]._id],
                    spend,
                    oop: average - premium,
                    oop_ptc: total_ptc - premium_ptc,
                    total,
                    total_ptc,
                    premium,
                    tax_savings,
                    premium_ptc: Math.min(premium, premium_ptc),
                    ...coverage_limits[coverages[i]._id],
                }
            }

            const aca_issuers: string[] = [];
            const facets = [...ctx.result.gold?.facet_groups || [], ...ctx.result.silver?.facet_groups || [], ...ctx.result.bronze?.facet_groups || []]
            for (let i = 0; i < facets.length; i++) {
                if (facets[i].name === 'issuers') {
                    for (let idx = 0; idx < facets[i].facets.length; idx++) {
                        aca_issuers.push(facets[i].facets[idx].value);
                    }
                }
            }

            const aCa_issuers = Array.from(new Set(aca_issuers))


            const data = {
                ...cost_sim.data,
                limit_remaining: ctx.result.limit_remaining || 1000,
                issuers: private_policies.data.map(a => a.issuer).filter(a => !!a),
                aca_issuers: aCa_issuers,
                person: shopData.person ? shopData.person : undefined,
                enrollment: shopData.enrollment ? shopData.enrollment : undefined,
                plan: shopData.plan ? shopData.plan : undefined,
                spend_dist,
                allSpend,
                byYear,
                worst10: spendList.slice(-10),
                distribution,
                distribution_ptc,
                coverage_scores,
                coverage_scores_ptc,
                spend,
                tax_rate,
                lastRun: new Date(),
                simsCount,
                coverages: coverages.map(a => {
                    return {
                        _id: a._id,
                        name: a.name,
                        carrierName: a.carrierName,
                        carrierLogo: a.carrierLogo,
                        acaPlan: a.acaPlan,
                        type: a.type,
                        off_exchange: a.off_exchange,
                        on_exchange: a.on_exchange,
                        plan_id: a.plan_id,
                        metal: a.metal,
                        deductible: a.deductible,
                        moop: a.moop,
                        coinsurance: a.coinsurance,
                        copys: a.copays,
                        coins: a.coins,
                        benefits: a.benefits
                    }
                }),
                stats: cost_sim.stats,
                useAptc: !cost_sim.skip_aptc,
                skipAptc: cost_sim.skip_aptc,
                compare_ids: cost_sim.compare_ids || undefined
            }
            data.resetId = getResetId(data)
            if (ctx.result.aptc || ctx.result.aptc === 0) data.aptc = ctx.result.aptc
            if (ctx.result.slcsp?.premium || ctx.result.slcsp?.premium === 0) data.slcsp = ctx.result.slcsp.premium
            if (context.id === fakeId) {
                context.result = await new CoreCall('shops', context).create(data)
            } else context.result = await new CoreCall('shops', context).patch(context.id as any, data)
        }
        return context;
    }
}

