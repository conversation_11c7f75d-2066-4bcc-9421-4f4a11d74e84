// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {Shops, ShopsData, ShopsPatch, ShopsQuery} from './shops.schema.js'

export type {Shops, ShopsData, ShopsPatch, ShopsQuery}

export interface ShopsParams extends MongoDBAdapterParams<ShopsQuery> {
}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ShopsService<ServiceParams extends Params = ShopsParams> extends MongoDBService<
    Shops,
    ShopsData,
    ShopsParams,
    ShopsPatch
> {
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        multi: true,
        operators: ['$regex', '$options'],
        Model: app.get('mongodbClient').then((db) => db.collection('shops'))

    }
}
