import {dollarString} from '../../../utils/index.js';
import {_get} from '../../../utils/dash-utils.js';
import {CoreCall} from 'feathers-ucan';
import {HookContext} from '../../../declarations.js';
import {genCmsHousehold} from '../../marketplace/cms/index.js';

export const productTypes = {
    'mm': {color: 'ir-orange', label: 'Private Major Medical Plan'},
    'aca': {color: 'ir-pink', label: 'ACA Marketplace Major Medical Plan'},
    'hs': {color: 'ir-purple', label: 'Health Share'},
}
export const acaToMarkdown = (plan: any) => {
    const lines: any = [];
    lines.push(`# Plan ID: ${plan._id || plan.plan_id}`)
    lines.push(`## Plan Name: ${plan.name || plan.title}`);
    lines.push(`## ACA Marketplace Plan ID: ${plan._id || plan.plan_id}`)
    lines.push(`## Carrier Name: ${plan.carrierName}`);
    lines.push('### Plan Type: ACA Marketplace Major Medical Plan')

    const field = (label, key) => {
        if (plan[key] !== undefined) {
            lines.push(`**${label}:** ${plan[key]}`);
        }
    };

    lines.push(`## Network Structure: ${plan.plan_type || 'Not Specified'}`)

    lines.push('## Deductibles')
    for (const k of ['medical', 'drug']) {
        lines.push(`### ${k} deductible`);
        lines.push(`- **In-Network:** Single - ${dollarString(plan.deductible[k]?.single?.in_network || 'n/a', '$', 0)} | Family - ${dollarString(plan.deductible[k]?.family?.in_network || 'n/a', '$', 0)}`);
        if (plan.deductible[k]?.single?.in_network2) lines.push(`- **In-Network Tier 2:** Single - ${dollarString(plan.deductible[k].single.in_network2 || 0, '$', 0)} | Family - ${dollarString(plan.deductible[k].family.in_network2 || 'n/a', '$', 0)}`);
        lines.push(`- **Out of Network:** Single - ${dollarString(plan.deductible[k]?.single?.oon || 0, '$', 0)} | Family - ${dollarString(plan.deductible[k]?.family?.oon || 0, '$', 0)}`);
    }
    lines.push('## Max Out Of Pocket (MOOP)')
    for (const k of ['medical', 'drug']) {
        lines.push(`### ${k} moop`);
        lines.push(`- **In-Network:** Single - ${dollarString(plan.moop[k]?.single?.in_network || 'n/a', '$', 0)} | Family - ${dollarString(plan.moop[k]?.family?.in_network || 'n/a', '$', 0)}`);
        if (plan.moop[k]?.single?.in_network2) lines.push(`- **In-Network Tier 2:** Single - ${dollarString(plan.moop[k].single.in_network2 || 0, '$', 0)} | Family - ${dollarString(plan.moop[k].family.in_network2 || 'n/a', '$', 0)}`);
        lines.push(`- **Out of Network:** Single - ${dollarString(plan.moop[k]?.single?.oon || 0, '$', 0)} | Family - ${dollarString(plan.moop[k]?.family?.oon || 0, '$', 0)}`);
    }

    lines.push('## Plan Details')
    field('Metal Level', 'metal');
    field('On Exchange', 'on_exchange');
    field('Off Exchange', 'off_exchange');
    field('HSA Eligible', 'hsa_eligible');

    lines.push('## Plan Benefits')
    for (const k in plan.benefits) {
        if (plan.benefits[k].covered === false) continue;
        const ben = plan.benefits[k];
        lines.push(`**${ben.label}:** ${ben.detail || 'Covered'}`);
    }

    lines.push('## Coinsurance (participant)')
    for (const k of ['in_network', 'in_network2', 'oon']) {
        lines.push(`### Summary: ${plan.coins[k]?.display?.join(', ') || 'n/a'}`)
        if (!plan.coins[k]) continue;
        lines.push(`### Weighted Average: ${dollarString(plan.coins[k].avg || 0, '', 2)}%`);
        for (const cat in plan.coins[k].categories || {}) {
            lines.push(`### ${cat.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}: ${dollarString(plan.coins[k]?.categories[cat] || 0, '', 2)}%`);
        }
    }

    lines.push('## Copays')
    for (const k of ['in_network', 'in_network2', 'oon']) {
        lines.push(`### Summary: ${plan.copay[k]?.display?.join(', ') || 'n/a'}`)
        if (!plan.copay[k]) continue;
        for (const cat in plan.copay[k].categories || {}) {
            lines.push(`### ${cat.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}: ${dollarString(plan.copay[k]?.categories[cat] || 0, '$', 2)}`);
        }
    }

    return lines.join('\n') + '\n';
}

export const coverageToMarkdown = (plan: any) => {
    const lines: any = [];
    lines.push(`# Plan ID: ${plan._id || plan.id}`)
    lines.push(`# Plan Name: ${plan.name || plan.title}`);
    lines.push(`# Carrier Name: ${plan.carrierName}`);
    lines.push(`## Plan Type: ${productTypes[plan.type || 'mm']?.label}`)


    const field = (label, key) => {
        if (plan[key] || plan[key] === 0 || plan[key] === false) {
            lines.push(`**${label}:** ${plan[key]}`);
        }
    };

    lines.push(`## Network Structure: ${plan.plan_type || 'Open Network'}`)

    lines.push('## Deductibles')
    const setDed = (k, label) => {
        const {name, waivable, detail, single, family, type} = (_get(plan, k) || {}) as any;
        lines.push(`### ${label}: ${name || k}`)
        lines.push(`- **Type/Period:** ${type === 'event' ? `Per Event ${label}` : `Annual ${label}`}`)
        if (waivable) lines.push('- **Waive-able:** Yes for preferred network/behavior');
        if (detail) lines.push(`- **Detail:** ${detail}`);
        if (single) lines.push(`- **Single:** ${dollarString(single, '$', 0)}`);
        if (family) lines.push(`- **Family:** ${dollarString(family, '$', 0)}`);
    }
    setDed('deductible', 'Deductible')
    for (const k in plan.deductibles || {}) {
        setDed(`deductibles.${k}`, 'Deductible')
    }

    lines.push('## Max Out Of Pocket (MOOP)')
    setDed('moop', 'Max Out of Pocket')
    for (const k in plan.moops || {}) {
        setDed(`moops.${k}`, 'Max Out of Pocket')
    }

    lines.push('## Plan Details')
    field('HSA Eligible', 'hsaQualified');
    field('Max Age', 'hsaQualified');
    field('Preventive Care Covered At Zero Cost', 'preventive');

    lines.push('## Plan Benefits')
    for (const k in plan.benefits) {
        if (plan.benefits[k].covered === false) continue;
        const ben = plan.benefits[k];
        lines.push(`**${ben.label}:** ${ben.detail || 'Covered'}`);
    }

    const setCoins = (k, label) => {
        const {name, detail, amount} = (_get(plan, k) || {}) as any;
        lines.push(`### ${label}: ${name || k}`)
        if (amount) lines.push(`- **Amount:** ${dollarString(amount, '$', 2)}`)
        if (detail) lines.push(`- **Detail:** ${detail}`)
    }
    lines.push('## Coinsurance (percent participant pays after deductible - before max out of pocket)')
    if (plan.coinsurance) setCoins('coinsurance', 'Coinsurance')
    for (const k in plan.coins || {}) {
        setCoins(`coins.${k}`, 'Coinsurance')
    }

    lines.push('## Copays')
    for (const k in plan.copays || {}) {
        setCoins(`copays.${k}`, 'Copays')
    }

    return lines.join('\n') + '\n';
}

const criticalResultsTable = (plans: any[], ptc?: boolean) => {
    if (!plans || plans.length === 0) {
        return 'No data available.';
    }

    const headers = ['_id', 'premium', 'oop', 'tax_savings', 'total', 'average', 'best', 'top3', 'last'];
    const headerNames = ['Plan ID', 'Premium', 'OOP (avg)', 'Tax Savings', 'Total', 'Average', 'Top 1', 'Top 3', 'Last']
    if (ptc) headers.push('ptc_eligible')
    const headerRow = `| ${headerNames.join(' | ')} |`;
    const dividerRow = `| ${headers.map(() => '---').join(' | ')} |`;

    const rows = plans.map(plan => {
        return `| ${headers.map(key => plan[key] ?? '').join(' | ')} |`;
    });

    return [headerRow, dividerRow, ...rows].join('\n');
}

type ShopResultOptions = {
    compare_coverages?: { [key:string]:any }
}
export const getShopResultTable = (shop: any, { compare_coverages }:ShopResultOptions) => {
    return async (context: HookContext) => {
        const {
            coverages,
            _id,
            stats,
            skipAptc,
            aptc,
            compare_ids,
            coverage_scores,
            coverage_scores_ptc,
            spend,
            useAptc,
            vectorStore
        } = shop || {};

        const acaPlans: any = [];
        const nonAca: any = [];

        for (const cov of coverages) {
            if (cov.acaPlan) acaPlans.push(cov._id);
            else if(cov._id.length === 24 && !(compare_coverages || {})[cov._id]) nonAca.push(cov._id);
        }

        const fetchedNonAca = await new CoreCall('coverages', context).find({
            query: {
                _id: {$in: nonAca},
                $limit: nonAca.length
            }
        })
            .catch(err => {
                throw new Error(`Error fetching non ACA coverages: ${err.message}`)
            });

        const household = genCmsHousehold(stats)
        const fetchedAca = await new CoreCall('marketplace', context).find({
            query: {},
            runJoin: {
                find_by_id: {
                    state: stats.place.state,
                    id: acaPlans,
                    household,
                    place: household.place
                }
            }
        })
            .catch(err => {
                throw new Error(`Error fetching ACA coverages: ${err.message}`)
            });

        const top: any[] = []
        top.push('## Purpose')
        top.push(`Health plan comparison in zip code ${stats.place.zipcode} for a household of ${stats.people.length + 1} people ages ${stats.age}, ${stats.people.map(a => a.age).join(', ')}.`)
        if (compare_ids?.length) {
            top.push(`A main purpose is to compare the current employer sponsored plans (ids: ${compare_ids.join(', ')}) -- against other plans available in this zip code.`)
        }
        top.push('The plan details for each plan are provided below under the Health Plans section with Plan ID for unique identifiers')
        top.push('## Methods')
        top.push(`We used U.S. medical bill data to simulate medical expenses for 100 years for each member of this household and ran those bills against the characteristics of each health plan to determine the spend for each plan in a nuanced manner.`)

        if(compare_coverages){
            top.push('## Current Employer Plans')
            top.push('### A major purpose of this simulation is to compare individual plan options against current options of an employer sponsored plan. The employer sponsored plan options are also detailed below. The IDs and names for those are as follows:')
            Object.keys(compare_coverages || {}).forEach(a => {
                top.push(` - ${a}: ${(compare_coverages || {})[a]?.name}`)
            })
        }

        top.push('## Simulation Results By Plan')
        top.push('### Table Header Definitions')
        top.push(' - Plan ID: Unique ID for the plan - used throughout this document')
        top.push(' - Premium: Plan premium for this household')
        top.push(' - OOP (avg): Average out of pocket spend across all scenarios with this plan in place')
        top.push(' - Tax Savings: Tax savings where applicable due to employer sponsorship')
        top.push(' - Total: Average total spend across all scenarios with this plan in place (oop + premium - tax_savings. This tells you your real winner barring more nuanced issues.')
        top.push(' - Top 1: Percentage of scenarios this plan is the best option')
        top.push(' - Top 3: Percentage of scenarios this plan is in the top 3 (lowest total spend)')
        top.push(' - Last: Percentage of scenarios this plan is the worst option')

        top.push('## Results')
        top.push(criticalResultsTable([...fetchedAca.data, ...fetchedNonAca.data, ...Object.keys(compare_coverages || {}).map(a => (compare_coverages || {})[a])].filter(a => coverage_scores[a]).map(a => {
            a.premium = coverage_scores[a._id].premium
            a.total = coverage_scores[a._id].total
            a.oop = coverage_scores[a._id].oop
            a.tax_savings = coverage_scores[a._id].tax_savings
            a.best = coverage_scores[a._id].top1
            a.top3 = coverage_scores[a._id].top3
            a.last = coverage_scores[a._id].last
            return a;
        })))

        if (useAptc && !skipAptc && aptc) {
            top.push(`## Results Factoring in the premium tax credit (PTC)`)
            top.push(`The PTC is ${dollarString(aptc, '$', 0)}/mo for eligible plans. An additional header was added **ptc_eligible**.`)
            top.push('## PTC Results')
            top.push(criticalResultsTable(coverages.map(a => {
                a.premium = coverage_scores[a._id].premium_ptc
                a.total = coverage_scores[a._id].total_ptc
                a.oop = coverage_scores[a._id].oop_ptc
                a.tax_savings = coverage_scores[a._id].tax_savings
                a.best = coverage_scores_ptc[a._id].top1
                a.top3 = coverage_scores_ptc[a._id].top3
                a.ptc_eligible = a.acaPlan && (!a.off_exchange || a.on_exchange)
                return a;
            })))
        }

        return top.join('\n');
    }
}

export const comparePlanTables = (coverages: Array<any>) => {
    const lines: any = [];
    lines.push('## Plan Info')
    const info_headers = ['Plan ID', 'Plan Name', 'Carrier Name', 'Plan Type', 'Network Type', 'On Exchange', 'Off Exchange', 'Metal Level']
    const info_paths = ['_id', 'name', 'carrierName', 'type', 'plan_type', 'on_exchange', 'off_exchange', 'metal']
    lines.push(`| ${info_headers.join(' | ')} |`);
    lines.push(`| ${info_headers.map(() => '---').join(' | ')} |`);
    for (let i = 0; i < coverages.length; i++) {
        const cov = coverages[i];
        let obj = {};
        if (cov.acaPlan) obj = {...cov, _id: cov._id || cov.id || cov.plan_id, type: 'ACA Marketplace Major Medical'}
        else obj = {
            ...cov,
            on_exchange: false,
            off_exchange: true,
            metal: 'n/a',
            type: productTypes[cov.type || 'mm'].label,
            plan_type: cov.plan_type || 'open network'
        }

        lines.push(`| ${info_paths.map(p => obj[p] ?? '').join(' | ')} |`);
    }
    lines.push('\n');

    lines.push('## Deductibles')
    const ded_headers = ['Plan ID', 'Single', 'Family', 'Type (annual/event)']
    lines.push(`| ${ded_headers.join(' | ')} |`);
    lines.push(`| ${ded_headers.map(() => '---').join(' | ')} |`);
    for (const cov of coverages) {
        if (cov.acaPlan) {
            const {single, family} = cov.deductible?.medical || {};
            lines.push(`| ${cov._id || cov.plan_id} | ${dollarString(single?.in_network, '$', 0)} | ${dollarString(family?.in_network, '$', 0)} | annual |`);
        } else {
            const {single, family, type} = cov.deductible || {};
            lines.push(`| ${cov._id} | ${dollarString(single, '$', 0)} | ${dollarString(family, '$', 0)} | ${type} |`);
        }
    }
    lines.push('\n');

    lines.push('## MOOP (Max Out of Pocket)')
    const moop_headers = ['Plan ID', 'Single', 'Family']
    lines.push(`| ${moop_headers.join(' | ')} |`);
    lines.push(`| ${moop_headers.map(() => '---').join(' | ')} |`);
    for (const cov of coverages) {
        if (cov.acaPlan) {
            const {single, family} = cov.moop?.medical || {};
            lines.push(`| ${cov._id || cov.plan_id} | ${dollarString(single?.in_network, '$', 0)} | ${dollarString(family?.in_network, '$', 0)} | annual |`);
        } else {
            const {single, family, type} = cov.moop || {};
            lines.push(`| ${cov._id} | ${dollarString(single, '$', 0)} | ${dollarString(family, '$', 0)} | ${type} |`);
        }
    }
    lines.push('\n');

    lines.push('## Coinsurance (percent participant pays after deductible - before max out of pocket)')
    const coins_headers = ['Plan ID', 'Percent']
    lines.push(`| ${coins_headers.join(' | ')} |`);
    lines.push(`| ${coins_headers.map(() => '---').join(' | ')} |`);
    for (const cov of coverages) {
        if (cov.acaPlan) {
            lines.push(`| ${cov._id || cov.plan_id} | ${dollarString(cov.coins.in_network?.avg, '', 2)}% |`);

        } else {
            lines.push(`| ${cov._id} | ${dollarString(cov.coinsurance?.amount, '', 2)}% |`);
        }
    }
    lines.push('\n');


    lines.push('## Copays')
    const copay_headers = ['Plan ID', 'Copay Name', 'Amount']
    lines.push(`| ${copay_headers.join(' | ')} |`);
    lines.push(`| ${copay_headers.map(() => '---').join(' | ')} |`);
    for (const cov of coverages) {
        if (cov.acaPlan) {
            const {in_network, in_network2, oon} = cov.copay || {}
            if (in_network?.avg) lines.push(`| ${cov._id || cov.plan_id} | In-Network | ${dollarString(in_network.avg, '$', 2)} |`);
            if (in_network2?.avg) lines.push(`| ${cov._id || cov.plan_id} | Tier 2 Network | ${dollarString(in_network2.avg, '$', 2)} |`);
            if (oon?.avg) lines.push(`| ${cov._id || cov.plan_id} | Out of Network | ${dollarString(oon.avg, '$', 2)} |`);
        } else {
            for (const k in cov.copays || {}) {
                lines.push(`| ${cov._id} | ${cov.copays[k].name} | ${dollarString(cov.copays[k].amount, '$', 2)} |`);
            }
        }
    }
    lines.push('\n');

    // lines.push('### Special Deductibles (use these for specific requests only)')
    // const spec_ded_headers = ['Plan ID', 'Deductible Name', 'Single', 'Family', 'Type (annual/event)', 'Waive-able']
    // lines.push(`| ${spec_ded_headers.join(' | ')} |`);
    // lines.push(`| ${spec_ded_headers.map(() => '---').join(' | ')} |`);

    // const setSpecialDed = (plan, k) => {
    //     const {name, waivable, single, family, type} = (_get(plan, k) || {}) as any;
    //     lines.push(`| ${plan._id} | ${name} | ${dollarString(single, '$', 0)} | ${dollarString(family, '$', 0)} | ${type} | ${waivable} |`);
    // }

    // const orZero = (v) => !!v || v === 0;
    // for (const cov of coverages) {
    //     if (cov.acaPlan) {
    //         const names = {'medical': 'Medical', 'drug': 'Drug'}
    //         for (const k of ['medical', 'drug']) {
    //             const {in_network, in_network2, oon} = (cov.deductible || {})[k]?.single || {}
    //             if (orZero(in_network)) lines.push(`| ${cov._id || cov.plan_id} | ${names[k]} In Network | ${dollarString(in_network, '$', 0)} | ${dollarString(cov.deductible[k].family?.in_network || 'n/a', '$', 0)} | annual | false |`);
    //             if (orZero(in_network2)) lines.push(`| ${cov._id || cov.plan_id} | ${names[k]} In Network Tier 2 | ${dollarString(in_network2, '$', 0)} | ${dollarString(cov.deductible[k].family?.in_network2 || 'n/a', '$', 0)} | annual | false |`);
    //             if (orZero(oon)) lines.push(`| ${cov._id || cov.plan_id} | ${names[k]} Out Of Network | ${dollarString(oon, '$', 0)} | ${dollarString(cov.deductible[k].family?.in_network2 || 'n/a', '$', 0)} | annual | false |`);
    //         }
    //     }
    // } else {
    // for (const k in cov.deductibles || {}) {
    //     setSpecialDed(cov, `deductibles.${k}`)
    // }
    // }
    // }
    // lines.push('\n');
    return lines.join('\n') + '\n';

}
