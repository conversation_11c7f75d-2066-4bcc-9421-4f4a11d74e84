// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    householdsDataValidator,
    householdsPatchValidator,
    householdsQueryValidator,
    householdsResolver,
    householdsExternalResolver,
    householdsDataResolver,
    householdsPatchResolver,
    householdsQueryResolver
} from './households.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {HouseholdsService, getOptions} from './households.class.js'
import {householdsPath, householdsMethods} from './households.shared.js'
import {allUcanAuth, anyAuth, AnyObj, CapabilityParts, CoreCall, loadExists, noThrow, setExists} from 'feathers-ucan';
import {
    checkExisting,
    deepDecrypt,
    deepEncrypt,
    encryptFields,
    findJoin, handleDeepEncrypt,
    logChange,
    relate
} from '../../utils/index.js';
import {compSources} from '../comps/utils/index.js';
import {_set} from 'symbol-ucan';

export * from './households.class.js'
export * from './households.schema.js'

const relatePerson = async (context: HookContext): Promise<HookContext> => {
    const config = {
        herePath: 'person',
        therePath: 'household',
        thereService: 'ppls',
        paramsName: 'householdPerson'
    }
    return relate('oto', config)(context);
}

const totalIncome = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks || context.params.exists_check) return context;
    if (!context.params.core.skipJoins) {
        context = await findJoin({
            service: 'cams',
            herePath: 'person',
            therePath: 'person',
            joinPath: 'cams',
            queryFn: (item: any) => {
                return {
                    person: item.person
                }
            },
        })(context);


        const setTotals = (res: AnyObj) => {
            const sources: any = {};
            for (const cam of (res._fastjoin?.cams || [])) {
                const t = compSources(cam, 'year');
                sources[cam._id] = {...t, _id: cam._id, name: cam.name}
            }
            res = _set(res, '_fastjoin.income_sources', sources);
            return res;
        }
        if (context.method === 'find') context.result.data = context.result.data.map(a => setTotals(a));
        else context.result = setTotals(context.result);
    }
    return context;
}

const guardChanges = async (context: HookContext): Promise<HookContext> => {
    const {members, $unset} = context.data;
    const rmvList: any[] = [];
    const unsetList: any[] = [];
    if (members) {
        const existing = await loadExists(context);
        context = setExists(context, existing);
        for (const mbr in existing.members || {}) {
            if (!members[mbr]) rmvList.push(mbr);
        }
    }
    if ($unset) {
        for (const k in $unset) {
            const splt = k.split('.');
            if (/members\./.test(k) && splt.length === 2) {
                rmvList.push(splt[1]);
                unsetList.push(splt[1]);
            }
        }
    }
    if (rmvList.length) {
        const existing = await loadExists(context);
        context = setExists(context, existing);
        const cams = await new CoreCall('cams', context, {skipJoins: true}).find({
            query: {
                person: existing.person,
                $limit: 25
            }
        })
        if (cams.total) {
            const query: any = {cams: {$in: cams.data.map(a => a._id)}};
            if (rmvList.length === 1) query[`enrolled.${rmvList[0]}`] = {$exists: true};
            else {
                query.$or = [];
                for (const id of rmvList) {
                    query.$or.push({[`enrolled.${id}`]: {$exists: true}})
                }
            }
            const enrollments = await new CoreCall('enrollments', context, {skipJoins: true}).find({query})
            if (enrollments.total) throw new Error('You cannot remove household members who are enrolled in active plans.')
        }
        if (unsetList.length) context.params._unset_hh = unsetList
    }
    return context;

}

const authenticate = async (context: HookContext) => {
    const creator = [['households', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['households', '*']] as Array<CapabilityParts>;

    const ucanArgs:any = {
        create: anyAuth,
        patch: [...creator],
        update: [...creator],
        remove: [...deleter]
    };
    if(context.method === 'create'){
        const person = await new CoreCall('ppls', context, { skipJoins: true }).get(context.data.person, { admin_pass: true })
        if(!person.login) ucanArgs.create = noThrow
    }
    //allow for login of household person to edit
    if (['patch', 'update', 'remove'].includes(context.method)) {
        const existing = await loadExists(context, {skipJoins: false});
        context = setExists(context, existing);
        const {person} = existing?._fastjoin?.person || {person: '*'}
        if (person && context.params.login?._id === person) {
            ucanArgs[context.method] = anyAuth
        } else if(!person?.login && context.params.special_change) {
            ucanArgs.create = noThrow;
            ucanArgs.patch = noThrow;
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        specialChange: context.params.specialChange,
        or: '*'
    })(context) as any;
}

const cleanupMembers = async (context: HookContext): Promise<HookContext> => {
    if (context.params._unset_hh) {
        const rmvMember = async (person) => {
            if (!person.login && !person.inGroups) {
                await new CoreCall('ppls', context).remove(person._id, {disableSoftDelete: true, admin_pass: true})
            }
        }
        const ppls = await new CoreCall('ppls', context, { skipJoins: true }).find({query: {_id: {$in: context.params._unset_hh}}, admin_pass: true})
            .catch(err => console.error(`Error in finding unset members: ${err.message}`));
        await Promise.all(ppls.data.map(a => rmvMember(a)));
    }
    return context;
}

const joinMembers = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks || context.params.exists_check) return context;
    if (context.params.runJoin?.hh_members) {
        return await findJoin({
            findIds: Object.keys(context.result.members || {}),
            therePath: '_id',
            service: 'ppls',
            herePath: 'members'
        })(context)
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const households = (app: Application) => {
    // Register our service on the Feathers application
    app.use(householdsPath, new HouseholdsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: householdsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(householdsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(householdsExternalResolver),
                schemaHooks.resolveResult(householdsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(householdsQueryValidator),
                schemaHooks.resolveQuery(householdsQueryResolver),
                handleDeepEncrypt('members', ['monthsSinceSmoked', 'disabled', 'incarcerated'])
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(householdsDataValidator),
                schemaHooks.resolveData(householdsDataResolver),
                checkExisting(['person'], '_householdExists'),
                relatePerson
            ],
            patch: [
                guardChanges,
                schemaHooks.validateData(householdsPatchValidator),
                schemaHooks.resolveData(householdsPatchResolver),
                relatePerson,

            ],
            remove: [relatePerson]
        },
        after: {
            all: [
                joinMembers,
                totalIncome,
                handleDeepEncrypt('members', ['monthsSinceSmoked', 'disabled', 'incarcerated'])
            ],
            create: [relatePerson],
            patch: [relatePerson, cleanupMembers],
            remove: [relatePerson]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [householdsPath]: HouseholdsService
    }
}
