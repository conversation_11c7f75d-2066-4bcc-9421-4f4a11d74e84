// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Households, HouseholdsData, HouseholdsPatch, HouseholdsQuery } from './households.schema.js'

export type { Households, HouseholdsData, HouseholdsPatch, HouseholdsQuery }

export interface HouseholdsParams extends MongoDBAdapterParams<HouseholdsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class HouseholdsService<ServiceParams extends Params = HouseholdsParams> extends MongoDBService<
  Households,
  HouseholdsData,
  HouseholdsParams,
  HouseholdsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('households'))
        .then((collection) => {
          collection.createIndex({person: 1}, {unique: true});
          return collection
        })
  }
}
