// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Cares, CaresData, CaresPatch, CaresQuery } from './cares.schema.js'

export type { Cares, CaresData, CaresPatch, CaresQuery }

export interface CaresParams extends MongoDBAdapterParams<CaresQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class CaresService<ServiceParams extends Params = CaresParams> extends MongoDBService<
  Cares,
  CaresData,
  CaresParams,
  CaresPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('cares')),
    multi: true
  }
}
