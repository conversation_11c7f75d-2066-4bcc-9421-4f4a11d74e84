// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    caresDataValidator,
    caresPatchValidator,
    caresQueryValidator,
    caresResolver,
    caresExternalResolver,
    caresDataResolver,
    caresPatchResolver,
    caresQueryResolver
} from './cares.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {CaresService, getOptions} from './cares.class.js'
import {caresPath, caresMethods} from './cares.shared.js'
import {findJoin, logChange, logHistory, scrubUploads} from '../../utils/index.js';
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {loadOrCreate} from '../cross-sections/utils/index.js';
import {AnalyticsFilter} from '@aws-sdk/client-s3';
import visit = AnalyticsFilter.visit;
import {ObjectId} from 'mongodb';

export * from './cares.class.js'
export * from './cares.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['cares', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['cares', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = []

    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create' && !context.params.admin_pass) {

            const existing = await loadExists(context);
            context = setExists(context, existing);
            //allow changes before approval
            if (!existing.approvedAt) context.params.admin_pass = true;

            if (existing) {
                const orgNamespace = `orgs:${existing.org || context.data.org}`;
                cap_subjects.push(existing.org || context.data.org)
                const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'planAdmin']]
                for (const w of orgWrite) {
                    ucanArgs.patch.unshift(w);
                    ucanArgs.update.unshift(w);
                }
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['person/owner'], '*']],
        cap_subjects
    })(context) as any;
}

const lastVisit = async (context: HookContext): Promise<HookContext> => {
    let {visits, $set, $addToSet, $pull} = context.data;
    if (!visits?.length && $addToSet?.visits) visits = $addToSet.visits.$each ? $addToSet.visits.$each : [$addToSet.visits];
    if (!visits?.length && $set?.visits) visits = $set?.visits;
    if (visits && !context.params.admin_pass) {
        const ex = await loadExists(context);
        context = setExists(context, ex)
        const v = await new CoreCall('visits', context, {skipJoins: true}).find({
            query: {
                $limit: 1,
                $sort: {date: -1},
                _id: {$in: [...(ex?.visits || []), ...visits]}
            }
        })
        if (v.total) context.data.lastVisit = v.data[0].date;
    } else if ($pull?.visits) {
        const ex = await loadExists(context);
        context = setExists(context, ex)
        const vsts = (ex?.visits || []).filter(a => !$pull.visits.map(b => String(b)).includes(String(a)));
        const v = await new CoreCall('visits', context, {skipJoins: true}).find({
            query: {
                $limit: 1,
                $sort: {date: -1},
                _id: {$in: vsts}
            }
        })
        if (v.total) context.data.lastVisit = v.data[0].date;
    }
    return context;
}

const trackConditions = async (context: HookContext): Promise<HookContext> => {
    let c = context.data.conditions || context.data.$addToSet?.conditions;
    if (c) {
        let ids;
        if (c.$each) ids = c.$each.map(a => a.id);
        else if (Array.isArray(c)) ids = c.map(a => a.id);
        else ids = c.id;
        await loadOrCreate(`plan_conditions:plans:${context.result.plan}`, {conditions: ids})(context);
    }
    return context;
}

const joinProviders = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks || context.params.exists_check) return context;
    if (context.params.runJoin?.care_provider) {
        const queryFn = (item) => {
            return {_id: {$in: item.providers || []}, $select: ['name', 'org', '_id', 'address', 'avatar']}
        }
        return findJoin({service: 'providers', herePath: 'providers', therePath: '_id', queryFn})(context)
    }
    return context;
}

const mapProviders = async (context: HookContext): Promise<HookContext> => {
    const {providers, practitioners, $addToSet} = context.data;
    let run;
    let provList: any = []
    let pracList: any = []
    if (providers) {
        run = true;
        provList = providers
    }
    if ($addToSet?.providers) {
        run = true
        if ($addToSet.providers.$each) provList = [...provList, ...$addToSet.providers.$each]
        else provList.push($addToSet.providers);
    }
    if (practitioners) {
        run = true;
        pracList = practitioners
    }
    if ($addToSet?.practitioners) {
        run = true
        if ($addToSet.practitioners.$each) pracList = [...pracList, ...$addToSet.practitioners.$each]
        else pracList.push($addToSet.practitioners);
    }
    if (run) {
        const hh = await new CoreCall('households', context, { skipJoins: true }).find({query: {$limit: 1, person: context.result.person}})
        if (hh.total) {
            const patchObj: any = {$addToSet: {}};
            if (pracList.length) patchObj.$addToSet.practitioners = {$each: pracList}
            if (provList.length) patchObj.$addToSet.providers = {$each: provList}
            await new CoreCall('households', context).patch(hh.data[0]._id, patchObj, { skipJoins: true })
                .catch(err => console.error(`Couldn't sync household providers: ${err.message}`))
        }
    }
    return context;
}

const scrubEmptyCare = async (context: HookContext): Promise<HookContext> => {
    if (context.params.runJoin?.scrub_empty_care) {
        const removeIds: any[] = [];
        const scrubOne = (c: any, idx: number) => {
            if (!c.visits?.length && !c.conditions?.length) {
                removeIds.push(c._id);
                context.result.data.splice(idx, 1);
            }
        }
        //do in reverse so splice doesn't affect the list;
        for (let i = context.result.data.length - 1; i >= 0; i--) {
            scrubOne(context.result.data[i], i)
        }
        context.result.total -= removeIds.length;
        if (removeIds.length) await new CoreCall('cares', context)._remove(null, {
            admin_pass: true,
            disableSoftDelete: true,
            query: {_id: {$in: removeIds}}
        })
    }
    return context;
}

const protect = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks) return context;
    if (context.method === 'remove' || context.data.deleted || context.data.$set?.deleted) {
        const ex = await loadExists(context);
        context = setExists(context, ex);
        if (ex?.visits?.length) {
            const v = await new CoreCall('visits', context).find({
                query: {
                    $limit: ex.visits.length,
                    _id: {$in: ex.visits}
                }
            })
            let stop;
            for (let i = 0; i < v.data.length; i++) {
                const visit = v.data[i];
                if (visit.claims?.length || visit.claimReqs?.length) {
                    stop = true;
                    break
                }
            }
            if (stop) throw new Error('You cannot remove this care event because there are visits and claims associated with it.')
            else {
                await new CoreCall('visits', context)._remove(null, {query: {_id: {$in: ex?.visits || []}}})
            }
        }
    }
    return context;
}

const syncBalance = async (context: HookContext): Promise<HookContext> => {
    const {balanceSyncedAt} = context.data;
    if (balanceSyncedAt) {
        const related = await new CoreCall('visits', context)._find({
            query: { deleted: { $ne: true }, care: typeof(context.id) === 'string' ? ObjectId.createFromHexString(context.id as string) : context.id },
            skip_hooks: true, admin_pass: true,
            paginate: false,
            pipeline: [
                {
                    $group: {
                        _id: null,
                        claim_total: { $sum: '$total' },
                        claim_subtotal: { $sum: '$subtotal' },
                        claim_balance: { $sum: '$balance' },
                        claim_paid: { $sum: '$paid.amount' },
                        claim_pending: { $sum: '$pending.amount' },
                        claim_ded: { $sum: '$paid.ded' },
                        claim_ded_pending: { $sum: '$pending.ded' },
                        claim_coins: { $sum: '$paid.coins' },
                        claim_coins_pending: { $sum: '$pending.coins' }
                    }
                }
            ]
        });

        if(related[0].claim_total) {
            const {
                claim_total,
                claim_subtotal,
                claim_balance,
                claim_paid,
                claim_ded,
                claim_coins,
                claim_ded_pending,
                claim_coins_pending,
                claim_pending
            } = related[0];
            context.data.$set = {
                ...context.data.$set,
                subtotal: claim_subtotal,
                total: claim_total,
                balance: claim_balance,
                paid: { amount: claim_paid, ded: claim_ded, coins: claim_coins },
                pending: { amount: claim_pending, ded: claim_ded_pending, coins: claim_coins_pending }
            };
        }
    }
    return context;
}


const paths = ['files.*'];
const uploadsConfig = {paths};
// A configure function that registers the service and its hooks via `app.configure`
export const cares = (app: Application) => {
    // Register our service on the Feathers application
    app.use(caresPath, new CaresService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: caresMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(caresPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(caresExternalResolver),
                schemaHooks.resolveResult(caresResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(caresQueryValidator),
                schemaHooks.resolveQuery(caresQueryResolver),
                scrubUploads(uploadsConfig)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(caresDataValidator),
                schemaHooks.resolveData(caresDataResolver)
            ],
            patch: [
                schemaHooks.validateData(caresPatchValidator),
                schemaHooks.resolveData(caresPatchResolver),
                protect,
                syncBalance,
                lastVisit,
                logHistory(['status', 'patientPriority', 'providerPriority', 'targetDate', 'conditions', 'files'])
            ],
            remove: [protect]
        },
        after: {
            all: [
                joinProviders,
                scrubUploads(uploadsConfig)
            ],
            find: [scrubEmptyCare],
            create: [trackConditions],
            patch: [
                mapProviders,
                trackConditions
            ]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [caresPath]: CaresService
    }
}
