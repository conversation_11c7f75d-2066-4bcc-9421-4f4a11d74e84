// TypeBox schema for claim-payments service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const claimPaymentsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
,
  // Missing fields from old schema
  claim: Type.Optional(Type.String()),
  visit: Type.Optional(Type.String()),
  plan: Type.Optional(ObjectIdSchema()),
  person: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  patient: Type.Optional(Type.String()),
  provider: Type.Optional(ObjectIdSchema()),
  coverage: Type.Optional(ObjectIdSchema()),
  enrollment: Type.Optional(ObjectIdSchema()),
  preventive: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type ClaimPayments = Static<typeof claimPaymentsSchema>
export const claimPaymentsValidator = getValidator(claimPaymentsSchema, dataValidator)
export const claimPaymentsResolver = resolve<ClaimPayments, HookContext>({})
export const claimPaymentsExternalResolver = resolve<ClaimPayments, HookContext>({})

export const claimPaymentsDataSchema = Type.Object({
  ...Type.Omit(claimPaymentsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ClaimPaymentsData = Static<typeof claimPaymentsDataSchema>
export const claimPaymentsDataValidator = getValidator(claimPaymentsDataSchema, dataValidator)
export const claimPaymentsDataResolver = resolve<ClaimPaymentsData, HookContext>({})

export const claimPaymentsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(claimPaymentsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type ClaimPaymentsPatch = Static<typeof claimPaymentsPatchSchema>
export const claimPaymentsPatchValidator = getValidator(claimPaymentsPatchSchema, dataValidator)
export const claimPaymentsPatchResolver = resolve<ClaimPaymentsPatch, HookContext>({})

// Allow querying on any field from the main schema
const claimPaymentsQueryProperties = claimPaymentsSchema
export const claimPaymentsQuerySchema = querySyntax(claimPaymentsQueryProperties)
export type ClaimPaymentsQuery = Static<typeof claimPaymentsQuerySchema>
export const claimPaymentsQueryValidator = getValidator(claimPaymentsQuerySchema, queryValidator)
export const claimPaymentsQueryResolver = resolve<ClaimPaymentsQuery, HookContext>({})
