// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { ClaimPayments, ClaimPaymentsData, ClaimPaymentsPatch, ClaimPaymentsQuery } from './claim-payments.schema.js'

export type { ClaimPayments, ClaimPaymentsData, ClaimPaymentsPatch, ClaimPaymentsQuery }

export interface ClaimPaymentsParams extends MongoDBAdapterParams<ClaimPaymentsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ClaimPaymentsService<ServiceParams extends Params = ClaimPaymentsParams> extends MongoDBService<
  ClaimPayments,
  ClaimPaymentsData,
  ClaimPaymentsParams,
  ClaimPaymentsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('claim-payments')),
    multi: true
  }
}
