// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {JunkDrawers, JunkDrawersData, JunkDrawersPatch, JunkDrawersQuery} from './junk-drawers.schema.js'

export type {JunkDrawers, JunkDrawersData, JunkDrawersPatch, JunkDrawersQuery}

export interface JunkDrawersParams extends MongoDBAdapterParams<JunkDrawersQuery> {
}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class JunkDrawersService<ServiceParams extends Params = JunkDrawersParams> extends MongoDBService<
    JunkDrawers,
    JunkDrawersData,
    JunkDrawersParams,
    JunkDrawersPatch
> {
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        multi: true,
        Model: app.get('mongodbClient').then((db) => db.collection('junk-drawers'))
            .then((collection) => {
                collection.createIndex({itemId: 1}, {unique: true});
                return collection
            }),
        operators: ['$regex', '$options']
    }
}
