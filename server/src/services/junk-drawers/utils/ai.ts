import { HookContext} from '../../../declarations.js';
import {loadExists, setExists} from 'feathers-ucan';
import {addFileToVectorStore} from '../../../utils/index.js';

export const aiContext = async (context: HookContext) => {
    const {ai_context} = context.params.runJoin || {};
    if (ai_context) {
        const ex = await loadExists(context);
        context = setExists(context, ex);

        const { _id } = ex;
        const markdown = context.data.$set['data.bias.body'];
        if(!markdown) throw new Error('No markdown data provided');
        const b = Buffer.from(markdown, 'utf-8');
        const mimeType = 'text/markdown';

        const vectorRes = await addFileToVectorStore(context, { mimeType, buffer: b, vectorConfig: ex.data.bias.vectorStore, fileName: 'context_bias.md', storeName: _id, taskName: 'context bias'})

        context.data.$set = { ...context.data.$set, ['data.bias.vectorStore']: { id: vectorRes.vectorStoreId, fileIds: [vectorRes.file.id].filter(a => !!a), updatedAt: new Date() }}

    }
    return context;
}

