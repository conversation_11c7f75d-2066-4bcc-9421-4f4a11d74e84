// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    junkDrawersDataValidator,
    junkDrawersPatchValidator,
    junkDrawersQueryValidator,
    junkDrawersResolver,
    junkDrawersExternalResolver,
    junkDrawersDataResolver,
    junkDrawersPatchResolver,
    junkDrawersQueryResolver
} from './junk-drawers.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {JunkDrawersService, getOptions} from './junk-drawers.class.js'
import {junkDrawersPath, junkDrawersMethods} from './junk-drawers.shared.js'
import {CoreCall, noThrowAuth} from 'feathers-ucan';
import {geoQuery, logChange} from '../../utils/index.js';

export * from './junk-drawers.class.js'
export * from './junk-drawers.schema.js'

const setItemId = (data) => {
    return { ...data, itemId: `${data.drawer}|${data.itemName}`}
}
const handleJunk = async (context: HookContext):Promise<HookContext> => {
    // const existing = await context.app.service(context.path).find({ query: { drawer: 'zips', $limit: 100 }});
    // await Promise.all(existing.data.map(a => context.app.service(context.path).remove(a._id, { disableSoftDelete: true })));
    // throw new Error('here we are');
    if(Array.isArray(context.data.files)){
        context.result = await Promise.all(context.data.files.map(a => new CoreCall(context.path, context, { skipJoins: true }).create(a)));
    } else context.data = setItemId(context.data);
    return context;
}

const runJoins = async (context: HookContext):Promise<HookContext> => {
    const { runJoin } = context.params;
    if(runJoin){
        if(runJoin.stateCounties){
            const joinCounties = (sd) => {
                const counties = {};
                for(const city in sd.data?.cities || {}){
                    const county = sd.data.cities[city]?.county;
                    if(county){
                        counties[county] = { zips: [...counties[county]?.zips || [], ...sd.data.cities[city].zips || [sd.data.cities[city].zip].filter(a => !!a)]}
                    }
                }
                sd._fastjoin = { ...sd._fastjoin, counties }
                return sd;
            }
            if(context.method === 'find') context.result.data = context.result.data.map(a => joinCounties(a));
            else context.result = joinCounties(context.result);
        }
    }
    return context;
}

import {aiContext} from './utils/ai.js';

// A configure function that registers the service and its hooks via `app.configure`
export const junkDrawers = (app: Application) => {
    // Register our service on the Feathers application
    app.use(junkDrawersPath, new JunkDrawersService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: junkDrawersMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(junkDrawersPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(junkDrawersExternalResolver),
                schemaHooks.resolveResult(junkDrawersResolver)
            ]
        },
        before: {
            all: [
                noThrowAuth,
                logChange(),
                schemaHooks.validateQuery(junkDrawersQueryValidator),
                schemaHooks.resolveQuery(junkDrawersQueryResolver)
            ],
            find: [
                geoQuery

            ],
            get: [],
            create: [
                handleJunk,
                schemaHooks.validateData(junkDrawersDataValidator),
                schemaHooks.resolveData(junkDrawersDataResolver)
            ],
            patch: [
                schemaHooks.validateData(junkDrawersPatchValidator),
                schemaHooks.resolveData(junkDrawersPatchResolver),
                aiContext
            ],
            remove: []
        },
        after: {
            all: [runJoins]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [junkDrawersPath]: JunkDrawersService
    }
}
