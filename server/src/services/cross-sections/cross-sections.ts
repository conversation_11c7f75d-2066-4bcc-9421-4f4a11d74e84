// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    crossSectionsDataValidator,
    crossSectionsPatchValidator,
    crossSectionsQueryValidator,
    crossSectionsResolver,
    crossSectionsExternalResolver,
    crossSectionsDataResolver,
    crossSectionsPatchResolver,
    crossSectionsQueryResolver
} from './cross-sections.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {CrossSectionsService, getOptions} from './cross-sections.class.js'
import {crossSectionsPath, crossSectionsMethods} from './cross-sections.shared.js'
import {allUcanAuth, CapabilityParts, loadExists, setExists} from 'feathers-ucan';
import {logChange} from '../../utils/index.js';

export * from './cross-sections.class.js'
export * from './cross-sections.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['cross-sections', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['cross-sections', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: [...writer],
        patch: [...writer],
        remove: [...deleter]
    };
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        or: '*'
    })(context) as any;
}

// A configure function that registers the service and its hooks via `app.configure`
export const crossSections = (app: Application) => {
    // Register our service on the Feathers application
    app.use(crossSectionsPath, new CrossSectionsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: crossSectionsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(crossSectionsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(crossSectionsExternalResolver),
                schemaHooks.resolveResult(crossSectionsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(crossSectionsQueryValidator),
                schemaHooks.resolveQuery(crossSectionsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(crossSectionsDataValidator),
                schemaHooks.resolveData(crossSectionsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(crossSectionsPatchValidator),
                schemaHooks.resolveData(crossSectionsPatchResolver)
            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [crossSectionsPath]: CrossSectionsService
    }
}
