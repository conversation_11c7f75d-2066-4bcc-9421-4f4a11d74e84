// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type {
  CrossSections,
  CrossSectionsData,
  CrossSectionsPatch,
  CrossSectionsQuery
} from './cross-sections.schema.js'

export type { CrossSections, CrossSectionsData, CrossSectionsPatch, CrossSectionsQuery }

export interface CrossSectionsParams extends MongoDBAdapterParams<CrossSectionsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class CrossSectionsService<ServiceParams extends Params = CrossSectionsParams> extends MongoDBService<
  CrossSections,
  CrossSectionsData,
  CrossSectionsParams,
  CrossSectionsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient')
        .then((db) => db.collection('cross-sections'))
        .then((collection) => {
          collection.createIndex({hackId: 1}, {unique: true});
          return collection
        }),
  }
}
