import {CoreCall} from 'feathers-ucan';
import { HookContext} from '../../../declarations.js';
import OpenAi from 'openai';

import { hs_financials_table } from './files.js';
import {coverageToMarkdown, productTypes} from '../../shops/chat/files.js';
import {saveChatHistory} from '../../../utils/index.js';

interface AIChatParams {
    chatId: string;           // Used for saving chat history
    chat_session?: string;    // Optional session ID for grouping messages
    text: string;             // User's input question
    chat_history?: string;    // Markdown or text-formatted history for context
    shop_id?: string;         // Used to fetch a specific 'shop' record

}
export const ai_chat_get = async (context: HookContext) => {
    const {ai_chat} = context.params.runJoin || {};
    if (ai_chat) {
        const {key, org} = context.app.get('openai');
        const openai = new OpenAi({apiKey: key, organization: org})

        const { shop_id } = ai_chat;
        let shop;
        if(shop_id){
            shop = await new CoreCall('shops', context).get(shop_id);
        }

        let vectorid;
        let financial_table;
        const { type, productDetailRef } = context.result;
        if(type === 'hs' && productDetailRef){
            const spl = productDetailRef.split(':');
            const hs = await new CoreCall('health-shares', context).get(spl[0]);
            const { vectorStore } = hs.products[spl[1]] || {};
            if(vectorStore?.id) vectorid = vectorStore.id;
            if(Object.keys(hs.financials || {}).length){
                financial_table = hs_financials_table(hs.financials);
            }
        }

        const drawers = await new CoreCall('junk-drawers', context).find({
            query: {
                $limit: 1,
                itemId: 'ai|context'
            }
        })
        const drawer = drawers.data[0];
        const biasVectorId = drawer?.data?.bias?.vectorStore?.id;
        const vector_store_ids = [biasVectorId];
        if(vectorid) vector_store_ids.unshift(vectorid);
        const tools:any = [
            {
                type: 'file_search',
                vector_store_ids
            }
        ]
        const input = `
        ## 🧠 Assistant Context
        A user is seeing a health plan in a rich UI that provides information and comparative analysis about the plan. They want more details about the plan. The plan is a ${productTypes[type].label} with the name ${context.result.name || context.result.title}. The carrier name is ${context.result.carrierName}.
        
        ## Plan Summary Details
        These details summarize the critical high-level benefit structure of the plan
        ${coverageToMarkdown(context.result)}
        
        ## Plan Documents **For deeper detail**
        If specific questions are asked that would require deeper details the full plan documents/guidelines are ${vectorid ? 'Provided in the **first vector store**.' : 'Not specifically available.'}
        
        ${financial_table ? `## Plan Historical Financials \n For understanding the size and stability of the plan, the following historical financials are available\n${financial_table}` : ''}
        
        ${shop?.vectoreStore?.id ? `## 📊 Comparative Analysis \n The **final vector store** provided has the results of a comparative analysis. You can trust these as the primary source of how well suited this plan is for the user - outside of specific contract issues that would disqualify it as a fit.` : ''}
        
             ${ai_chat.current_coverage ? `## Current Coverage \n - The user is currently enrolled in the plan ID ${ai_chat.current_coverage._id || ai_chat.current_coverage.id} - name: ${ai_chat.current_coverage.name}. A major purpose of this simulation is to compare this plan against individual market options.` : ''}
        
        ## Chat History
        ${ai_chat.chat_history || 'N/A'}
        
        ## Question
        ${ai_chat.text}
        
        ## Behavioral Guide
- Use the **${vectorid ? 'second' : ''} vector store** provided is for behavioral/bias guidance. Do not refer to the behavioral guide in your response, it's just a background guidance for context.
        `
        const response = await openai.responses.create({
            model: 'gpt-4o',
            input,
            tools
        })
            .catch(err => {
                console.error(`Error querying plan doc ai chat: ${err.message}`)
                throw new Error(`Error querying plan doc ai chat: ${err.message}`)
            })
        const output: any = response.output.filter(a => a.type === 'message');
        const content: any = output[0].content.filter(a => a.type === 'output_text')[0];

        saveChatHistory({
            chatId: ai_chat.chatId,
            subjectId: context.result._id,
            chat_session: ai_chat.chat_session,
            chat: { question: ai_chat.text, answer: content.text, annotations: content.annotations }
        })(context)

        context.result = { ...context.result, _fastjoin: { ai_response: content} };

    }
    return context;
}
