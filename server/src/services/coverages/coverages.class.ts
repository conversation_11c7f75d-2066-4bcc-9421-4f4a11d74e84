// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type {Params} from '@feathersjs/feathers'
import {MongoDBService} from '@feathersjs/mongodb'
import type {MongoDBAdapterParams, MongoDBAdapterOptions} from '@feathersjs/mongodb'

import type {Application} from '../../declarations.js'
import type {Coverages, CoveragesData, CoveragesPatch, CoveragesQuery} from './coverages.schema.js'

export type {Coverages, CoveragesData, CoveragesPatch, CoveragesQuery}

export interface CoveragesParams extends MongoDBAdapterParams<CoveragesQuery> {
}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class CoveragesService<ServiceParams extends Params = CoveragesParams> extends MongoDBService<
    Coverages,
    CoveragesData,
    CoveragesParams,
    CoveragesPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        multi: true,
        Model: app.get('mongodbClient')
            .then((db) => db.collection('coverages'))
            .then((collection) => {
                return collection
            }),
        operators: ['$regex', '$options', '$geoIntersects', '$geometry']
    }
}
