// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Hosts, HostsData, HostsPatch, HostsQuery } from './hosts.schema.js'

export type { Hosts, HostsData, HostsPatch, HostsQuery }

export interface HostsParams extends MongoDBAdapterParams<HostsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class HostsService<ServiceParams extends Params = HostsParams> extends MongoDBService<
  Hosts,
  HostsData,
  HostsParams,
  HostsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('hosts'))
        .then((collection) => {
          collection.createIndex({subdomain: 1})
          collection.createIndex({dba: 1})
          return collection;
        }),
      operators: ['$regex', '$options']

  }
}
