// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    expensesDataValidator,
    expensesPatchValidator,
    expensesQueryValidator,
    expensesResolver,
    expensesExternalResolver,
    expensesDataResolver,
    expensesPatchResolver,
    expensesQueryResolver
} from './expenses.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {ExpensesService, getOptions} from './expenses.class.js'
import {expensesPath, expensesMethods} from './expenses.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {cascadeSpent} from '../budgets/hooks/index.js';
import {getJoin, logChange, relate} from '../../utils/index.js';

export * from './expenses.class.js'
export * from './expenses.schema.js'

export const LimitStatus = {
    'active': 'ACTIVE',
    'canceled': 'TERMINATED',
    'inactive': 'SUSPENDED'
}

const authenticate = async (context: HookContext) => {
    const writer = [['expenses', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['expenses', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        get: writer,
        find: writer,
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects: any = []
    if (context.data?.closeMemo) context.params.closeMemo = context.data.closeMemo
    if (context.method !== 'create' && !context.params.admin_pass && !context.params.loopingAuth) {
        const existing = await loadExists(context, {params: {admin_pass: true, loopingAuth: true}});
        context = setExists(context, existing);
        //allow changes before approval
        if (existing) {
            const orgNamespace = `orgs:${existing.owner}`;
            cap_subjects.push(existing.owner);
            const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'providerAdmin'], [orgNamespace, 'WRITE']]
            for (const w of orgWrite) {
                ucanArgs.get.unshift(w);
                ucanArgs.find.unshift(w);
                ucanArgs.patch.unshift(w);
                ucanArgs.update.unshift(w);
                ucanArgs.remove.unshift(w);
            }
            // if((existing.managers || []).includes(context.params.login._id)) context.params.admin_pass = true;
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['get', 'find', 'patch', 'create', 'remove'],
        loginPass: [[['managers/owner'], '*']],
        cap_subjects
    })(context) as any;
}

const relateBudget = async (context: HookContext) => {
    return relate('otm', {
        herePath: 'budget',
        therePath: 'expenses',
        thereService: 'budgets',
        paramsName: 'expenseBudget'
    })(context);
}

const runJoins = async (context: HookContext) => {
    const {
        limit_owner,
        limit_budget,
        limit_budget_owner,
        all_parents,
        ramp_limit,
        limit_cards
    } = context.params.runJoin || {}
    if (ramp_limit && context.result.ramp_limit) {
        const limit = context.params.banking?.ramp_limit || await new CoreCall('banking', context).get(context.result.ramp_limit as any, {
            banking: {
                moov: {
                    method: 'limits',
                    args: ['fetch', context.result.ramp_limit]
                }
            }
        })
            .catch(err => console.error(`Error joining stripe limit to expense: ${err.message}`))
        context.result._fastjoin = {...context.result._fastjoin, ramp_limit: limit}
    }
    if (limit_owner) return getJoin({herePath: 'limit_owner', service: 'ppls'})(context)
    if (limit_budget_owner) {
        context = await getJoin({
            herePath: 'budget',
            service: 'budgets',
            params: {runJoin: {budget_owner: true}}
        })(context)
    }
    if (limit_cards) {
        const limit = context.params.banking?.ramp_limit || await new CoreCall('banking', context).get(context.result.ramp_limit as any, {
            banking: {
                moov: {
                    method: 'limits',
                    args: ['fetch', context.result.ramp_limit]
                }
            }
        })
            .catch(err => console.error(`Error joining stripe limit to expense: ${err.message}`))
        const cards: any = [];

        if (limit.cards) {
            const fetchOne = async (card_id) => {
                const crd = await new CoreCall('banking', context).get(context.result.owner, {
                    banking: {
                        ramp: {
                            method: 'cards',
                            args: ['fetch', card_id]
                        }
                    }
                })
                    .catch(err => {
                        console.error(`Error retrieving card: ${err.message}`)
                    });
                cards.push(crd);
            }
            await Promise.all(limit.cards.map(a => fetchOne(a.card_id)))
        }
        context.result._fastjoin = {...context.result._fastjoin, limit, cards}
    }
    if (limit_budget) return getJoin({herePath: 'budget', service: 'budgets'})(context)
    if (all_parents) return getJoin({
        herePath: 'budget',
        service: 'budgets',
        params: {runJoin: {all_parents: true}}
    })(context)

    return context;
}


export type Amt = { amount: number, currency_code?: string }
export type Restrictions = {
    limit: Amt,
    temporary_limit: Amt,
    transaction_amount_limit: Amt,
    interval: string,
    auto_lock_date: string,
    next_interval_reset: string,
    start_of_interval: string,
    allowed_categories: Array<number>,
    allowed_vendors: Array<string>,
    blocked_categories: Array<number>,
    blocked_mcc_codes: Array<string>,
    blocked_vendors: Array<string>
}
export type UpdateRestrictions = {
    limit: Amt,
    transaction_amount_limit: Amt,
    interval: string,
    allowed_categories: Array<number>,
    allowed_vendors: Array<string>,
    blocked_categories: Array<number>,
    blocked_mcc_codes: Array<string>,
    blocked_vendors: Array<string>,
    is_one_time_edit: boolean,
    lock_date: string
}
/**
 * Extracts only the spend_restrictions fields from a full restrictions object
 * to safely use when updating Ramp limits.
 */
const restrictionsToUpdate = (restrictions: Record<string, any>) => {
    if (!restrictions || typeof restrictions !== 'object') return {};

    const mapping: Record<string, string> = {
        limit: 'limit',
        transaction_amount_limit: 'transaction_amount_limit',
        interval: 'interval',
        allowed_categories: 'allowed_categories',
        allowed_vendors: 'allowed_vendors',
        blocked_categories: 'blocked_categories',
        blocked_mcc_codes: 'blocked_mcc_codes',
        blocked_vendors: 'blocked_vendors',
        is_one_time_edit: '',
        lock_date: 'auto_lock_date'
    };

    const spendRestrictions: Record<string, any> = {};

    for (const k in mapping) {
        if (restrictions[mapping[k]] != null) {
            spendRestrictions[k] = restrictions[mapping[k]];
        }
    }

    return spendRestrictions;
}

/** attempt to $inc the budget for the amount increase or status update */
const incBudget = async (context: HookContext) => {
    const {$inc, updatedAt, updatedBy, updatedByHistory, status, $set, budget, max_transaction} = context.data || {}
    const newStatus = status || $set?.status
    const newBudget = budget || $set?.newBudget
    let {amount = 0, recurs = 0} = $inc || {};
    if (!context.params.skip_hooks && (amount || recurs || newStatus || newBudget)) {
        const handleError = async (err: any, message: string, parentId?: string) => {
            console.error(`Error adding card amount | ${context.id} | ${message} | ${err.message}`)
            if (parentId) await new CoreCall('budgets', context)._patch(parentId as any, {runSync: new Date()}, {
                skip_hooks: true,
                admin_pass: true
            })
            throw new Error(err.message)
        }
        const patchObj: any = {$inc: {}}
        const existing: any = await loadExists(context)
            .catch(err => handleError(err, 'loading existing'));
        context = setExists(context, existing);
        const sTatus = newStatus || existing.status;
        if (sTatus === 'active') {
            //limit changes when incrementing amount - to limit changes to other properties that might cause balances to get out of sync
            context.data = {$inc: {}, updatedAt, updatedBy, updatedByHistory}
            if (newStatus && newStatus !== existing.status) {
                context.data.status = newStatus;
                amount += newStatus === 'active' ? existing.amount || 0 : (existing.amount || 0) * -1
                recurs += newStatus === 'active' ? existing.recurs || 0 : (existing.recurs || 0) * -1
            }
            let update_limit = false;

            if (status) {
                if (newStatus && newStatus !== existing.status) {
                    if (newStatus === 'active') {
                        const lmt = await new CoreCall('banking', context).get(context.result.owner, {
                            banking: {
                                ramp: {
                                    method: 'limits',
                                    args: ['unsuspend', context.result.ramp_limit]
                                }
                            }
                        })
                        context.params.banking = {...context.params.banking, ramp_limit: lmt}

                    } else if (newStatus === 'inactive') {
                        const lmt = await new CoreCall('banking', context).get(context.result.owner, {
                            banking: {
                                ramp: {
                                    method: 'limits',
                                    args: ['suspend', context.result.ramp_limit]
                                }
                            }
                        })
                        context.params.banking = {...context.params.banking, ramp_limit: lmt}

                    } else if (newStatus === 'canceled') {
                        const lmt = await new CoreCall('banking', context).get(context.result.owner, {
                            banking: {
                                ramp: {
                                    method: 'limits',
                                    args: ['terminate', context.result.ramp_limit]
                                }
                            }
                        })
                        context.params.banking = {...context.params.banking, ramp_limit: lmt}
                        context.data.$addToSet = {
                            closedLimitIds: context.result.ramp_limit,
                            closedLimits: {
                                id: context.result.ramp_limit,
                                closedAt: new Date(),
                                memo: 'Limit was terminated'
                            }
                        }
                    }
                }
            }

            let lmt = await new CoreCall('banking', context).get(context.result.owner, {
                banking: {
                    ramp: {
                        method: 'limits',
                        args: ['fetch', existing.ramp_limit]
                    }
                }
            })
            const limit_data: any = {spending_restrictions: restrictionsToUpdate(lmt.restrictions)}

            //prepare patchObj to assign amount and/or recurs to parent or careAccount
            if (amount) {
                patchObj.$inc.assigned_amount = amount;
                lmt = await new CoreCall('banking', context).get(context.result.owner, {
                    banking: {
                        ramp: {
                            method: 'limits',
                            args: ['update', {
                                data: {
                                    spending_restrictions: {
                                        limit: {amount}
                                    },
                                    is_one_time_edit: true
                                },
                                limit_id: context.result.ramp_limit
                            }]
                        }
                    }
                })
                context.params.banking = {...context.params.banking, ramp_limit: lmt}
            }
            if (recurs) {

                patchObj.$inc.assigned_recurs = recurs;
                /** check if there is a limit set - if so, update it. It may not be set because the recurrence dates got out of sync and it was set to zero until the recur reset */
                if (lmt.restrictions.limit.amount) {
                    limit_data.spending_restrictions = {limit: {amount: recurs}}
                    update_limit = true
                }
            }
            if (max_transaction) {
                limit_data.spending_restrictions = {
                    ...limit_data.spending_restrictions,
                    transaction_amount_limit: {amount: max_transaction}
                }
                update_limit = true
            }
            if (update_limit) {
                lmt = await new CoreCall('banking', context).get(context.result.owner, {
                    banking: {
                        ramp: {
                            method: 'limits',
                            args: ['update', {
                                data: limit_data,
                                limit_id: context.result.ramp_limit
                            }]
                        }
                    }
                })
                context.params.banking = {...context.params.banking, ramp_limit: lmt}
            }
            if (newBudget && String(newBudget) !== String(existing.budget)) {
                //ON BUDGET CHANGE
                context.data.budget = newBudget;
                amount += existing.amount || 0
                recurs += existing.recurs || 0
                await new CoreCall('budgets', context, {skipJoins: true}).patch(newBudget, {
                    $inc: {
                        assigned_recurs: recurs,
                        assigned_amount: amount
                    }
                }, {admin_pass: true})
                patchObj.$inc.assigned_recurs = recurs * -1;
                patchObj.$inc.assigned_amount = amount * -1;
            }
            await new CoreCall('budgets', context, {skipJoins: true}).patch(existing.budget, patchObj, {admin_pass: true})
                .catch(err => handleError(err, 'patching parent', existing.budget))

            /** make sure the amount being added doesn't exceed the card limit - otherwise the card needs to be re-added
             * TODO: consider automatically adding a new card if the budget balance exists
             */

        }
    }
    return context;
}

const cascadeLimitSpent = async (context: HookContext) => {
    const {lastSync} = context.data || {};
    if (lastSync) {
        context = await cascadeSpent(true)(context);
        if (lastSync) context.data.lastSync = lastSync
        return context;
    }
    return context;
}

const isSameDay = (date1, date2) => {
    return date1.getFullYear() === date2.getFullYear() &&
        date1.getMonth() === date2.getMonth() &&
        date1.getDate() === date2.getDate();
}

const syncLimit = async (context: HookContext) => {
    // if(context.params.skip_hooks) return context;
    if (!context.result.ramp_limit) return context;
    let lmt = context.params.banking?.ramp_limit || await new CoreCall('banking', context).get(context.result.owner, {
        banking: {
            ramp: {
                method: 'limits',
                args: ['fetch', context.result.ramp_limit]
            }
        }
    })
    context.params.banking = {...context.params.banking, ramp_limit: lmt}
    const budget = await new CoreCall('budgets', context, {skipJoins: true}).get(context.result.budget, {runJoin: {budget_parent: true}})
    /** not to exceed */
    const budgetBalance = (budget.amount || 0) - (budget.spent || 0) - (budget.spent_pending || 0)
    const cardBalance = (context.result.amount || 0);

    const patchObj: any = {};
    let runPatch = false;
    const data: any = {spending_restrictions: restrictionsToUpdate(lmt.restrictions)}
    let update_limit = false;

    let {temporary_limit} = lmt.restrictions || {}
    /** If the limit temporary amount is less than the expense amount - reduce the expense amount, otherwise reduce the limit temporary amount */
    if (temporary_limit && temporary_limit.amount !== context.result.amount) {
        if (temporary_limit.amount < context.result.amount) {
            patchObj.$inc = {amount: temporary_limit.amount - context.result.amount};
            runPatch = true;
        } else {
            lmt = await new CoreCall('banking', context).get(context.result.owner, {
                banking: {
                    ramp: {
                        method: 'limits',
                        args: ['update', {
                            data: {
                                spending_restrictions: {limit: {amount: context.result.amount}},
                                is_one_time_edit: true
                            },
                            limit_id: context.result.ramp_limit
                        }]
                    }
                }
            })
            temporary_limit = lmt.restrictions.temporary_limit;
        }
    }

    const {
        limit,
        auto_lock_date,
        start_interval,
        next_interval_reset,
        transaction_amount_limit
    } = lmt.restrictions || {}
    if (limit && limit.amount !== context.result.recurs) {
        if (limit.amount < context.result.recurs) {
            patchObj.$inc = {recurs: limit.amount - context.result.recurs};
            runPatch = true;
        } else {
            data.spending_restrictions = {limit: {amount: context.result.recurs}}
            update_limit = true;
        }
    }

    /** Ensure auto-lock date matches */
    if (auto_lock_date && !isSameDay(new Date(auto_lock_date), new Date(context.result.lock_date))) {
        data.lock_date = new Date(context.result.lock_date).toISOString();
        update_limit = true;
    }

    /** Ensure if there is a delayed start time - it matches */
    const now = new Date().getTime()
    if (start_interval || context.result.recur_start) {
        const resDt = new Date(context.result.recur_start)
        const lmtDt = new Date(start_interval)
        if (isSameDay(resDt, lmtDt) && ((now < lmtDt.getTime()) || (now < resDt.getTime()))) {
            /** if there is a expense recurrence, set the limit recurrence to zero and it will auto-restart next recurrence date */
            data.spending_restrictions = {...data.spending_restrictions, limit: {amount: 0}}
            update_limit = true;
        }
    }
    if (next_interval_reset || context.result.next_reset) {
        const resDt = new Date(context.result.next_reset)
        const lmtDt = new Date(next_interval_reset)
        if (isSameDay(resDt, lmtDt) && ((now < lmtDt.getTime()) || (now < resDt.getTime()))) {
            /** if there is a expense recurrence, set the limit recurrence to zero and it will auto-restart next recurrence date */
            data.spending_restrictions = {...data.spending_restrictions, limit: {amount: 0}}
            update_limit = true;
        }
    }

    if (temporary_limit) {
        if (temporary_limit.amount !== context.result.amount) {
            if (temporary_limit.amount < (context.result.amount || Infinity)) {
                patchObj.$inc = {amount: temporary_limit.amount - context.result.amount};
                runPatch = true;
            } else {
                data.spending_restrictions = {
                    ...data.spending_restrictions,
                    limit: {amount: context.result.amount},
                    one_time_edit: true
                };
                update_limit = true;
            }
        }
    }

    if (transaction_amount_limit) {
        if (transaction_amount_limit.amount !== context.result.max_transaction) {
            if (transaction_amount_limit.amount < (context.result.max_transaction || Infinity)) {
                patchObj.$inc = {max_transaction: transaction_amount_limit.amount - context.result.max_transaction};
                runPatch = true;
            } else {
                data.spending_restrictions = {
                    ...data.spending_restrictions,
                    transaction_amount_limit: {amount: context.result.max_transaction}
                };
                update_limit = true;
            }
        }
    }

    if (update_limit) {
        lmt = await new CoreCall('banking', context).get(context.result.owner, {
            banking: {
                ramp: {
                    method: 'limits',
                    args: ['update', {
                        data,
                        limit_id: context.result.ramp_limit
                    }]
                }
            }
        })
        context.params.banking = {...context.params.banking, ramp_limit: lmt}
    }
    const {cleared, pending} = lmt.balance || {};

    if (context.result.spent !== cleared.amount) {
        patchObj.$inc = {...patchObj.$inc, spent: cleared.amount - context.result.spent};
        runPatch = true;
    }
    if (context.result.spent_pending !== pending.amount) {
        patchObj.$inc = {...patchObj.$inc, spent_pending: pending.amount - context.result.spent_pending};
        runPatch = true;
    }


    if (runPatch) {
        patchObj.lastSync = new Date();
        context.result = await new CoreCall('expenses', context).patch(context.result._id as any, patchObj)
            .catch(err => {
                console.error(`Could not sync spent from transactions: ${err.message}`)
                throw new Error(`Could not sync spent from transactions: ${err.message}`)
            });
    }
    return context;
}

const checkOwner = async (context: HookContext) => {
    const person = await new CoreCall('ppls', context).get(context.data.limit_owner as any);
    if (!person.ramp_user_id) throw new Error('Cannot create limit for person without ramp user id');
    context.params.banking = {...context.params.banking, limit_owner: person}
    return context;
}

const addLimit = async (context: HookContext) => {
    const owner = context.params.banking?.limit_owner || await new CoreCall('ppls', context).get(context.result.limit_owner)
    const budget = await new CoreCall('budgets', context, {skipJoins: true}).get(context.result.budget, {runJoin: {budget_parent: true}})
    const s_r: any = {}
    if(context.result.amount || context.result.recurs) {
        if (context.result.recurs) {
            s_r.interval = 'MONTHLY';
            s_r.limit = {amount: context.result.recurs}
        } else {
            s_r.limit = {amount: context.result.amount}
            s_r.one_time_edit = true;
        }

        if (context.result.ramp_whitelist?.length) s_r.allowed_categories = context.result.ramp_whitelist;
        if (context.result.vendor_whitelist) {
            const vendors = await new CoreCall('orgs', context).find({
                query: {
                    _id: {$in: context.result.vendor_whitelist},
                    $limit: context.result.vendor_whitelist.length,
                    ramp_vendor_id: {$exists: true}
                }
            })
            s_r.allowed_vendors = vendors.data.map(a => a.ramp_vendor_id);
        }
        if (context.result.ramp_blacklist?.length) s_r.blocked_categories = context.result.ramp_blacklist;
        if (context.result.vendor_blacklist) {
            const vendors = await new CoreCall('orgs', context).find({
                query: {
                    _id: {$in: context.result.vendor_blacklist},
                    $limit: context.result.vendor_blacklist.length,
                    ramp_vendor_id: {$exists: true}
                }
            })
            s_r.blocked_vendors = vendors.data.map(a => a.ramp_vendor_id);
        }
        if (context.result.mcc_blacklist) s_r.blocked_mcc_codes = context.result.mcc_blacklist;
        if (context.result.lock_date) s_r.lock_date = new Date(context.result.lock_date).toISOString();
        if (context.result.max_transaction) s_r.transaction_amount_limit = {amount: context.result.max_transaction};

        const data: any = {
            idempotency_key: String(context.result._id),
            user_id: owner.ramp_user_id,
            display_name: `${budget.name} | ${context.result.name}`,
            is_sharable: true,
        }
        let limit;
        if (Object.keys(s_r).length) {
            data.spending_restrictions = s_r;
            limit = await new CoreCall('banking', context).get(context.result.owner, {
                banking: {
                    ramp: {
                        method: 'limits',
                        args: ['create', data]
                    }
                }
            })
            /** If amount differs from recurs - add a one-time limit */
        }
        if (context.result.amount && context.result.recurs && context.result.amount !== context.result.recurs) {
            if (!limit && context.result.ramp_limit) limit = await new CoreCall('banking', context).get(context.result.owner, {
                banking: {
                    ramp: {
                        method: 'limits',
                        args: ['fetch', context.result.ramp_limit]
                    }
                }
            })
            const args: any = ['update', {
                data: {
                    spending_restrictions: {
                        ...restrictionsToUpdate(limit.restrictions),
                        limit: {amount: context.result.amount},
                        is_one_time_edit: true
                    }
                },
                limit_id: limit.id
            }];
            if (!limit) {
                args[0] = 'create'
                args[1] = {...data, ...args[1].data}
            }
            limit = await new CoreCall('banking', context).get(context.result.owner, {
                banking: {
                    ramp: {
                        method: 'limits', args
                    }
                }
            })
        }


        context.result = await new CoreCall('expenses', context).patch(context.result._id as any, {ramp_limit: limit.id})
    }
    return context;
}

const maybeAddLimit = async (context: HookContext) => {
    if (!context.result.ramp_limit) return addLimit(context);
    return context;
}


// A configure function that registers the service and its hooks via `app.configure`
export const expenses = (app: Application) => {
    // Register our service on the Feathers application
    app.use(expensesPath, new ExpensesService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: expensesMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(expensesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(expensesExternalResolver),
                schemaHooks.resolveResult(expensesResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(expensesQueryValidator),
                schemaHooks.resolveQuery(expensesQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                checkOwner,
                schemaHooks.validateData(expensesDataValidator),
                schemaHooks.resolveData(expensesDataResolver),
                relateBudget
            ],
            patch: [
                schemaHooks.validateData(expensesPatchValidator),
                schemaHooks.resolveData(expensesPatchResolver),
                cascadeLimitSpent,
                incBudget,
                relateBudget
            ],
            remove: []
        },
        after: {
            all: [runJoins],
            get: [],
            create: [relateBudget, addLimit],
            patch: [maybeAddLimit, syncLimit, relateBudget],
            update: [relateBudget],
            remove: [relateBudget]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [expensesPath]: ExpensesService
    }
}
