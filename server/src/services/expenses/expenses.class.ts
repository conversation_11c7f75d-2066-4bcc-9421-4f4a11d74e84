// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Expenses, ExpensesData, ExpensesPatch, ExpensesQuery } from './expenses.schema.js'

export type { Expenses, ExpensesData, ExpensesPatch, ExpensesQuery }

export interface ExpensesParams extends MongoDBAdapterParams<ExpensesQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ExpensesService<ServiceParams extends Params = ExpensesParams> extends MongoDBService<
  Expenses,
  ExpensesData,
  ExpensesParams,
  ExpensesPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('expenses')).then((collection) => {
      collection.createIndex({budget: 1})
      return collection;
    })
  }
}
