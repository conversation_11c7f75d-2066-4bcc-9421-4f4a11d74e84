// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Plans, PlansData, PlansPatch, PlansQuery } from './plans.schema.js'

export type { Plans, PlansData, PlansPatch, PlansQuery }

export interface PlansParams extends MongoDBAdapterParams<PlansQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class PlansService<ServiceParams extends Params = PlansParams> extends MongoDBService<
  Plans,
  PlansData,
  PlansParams,
  PlansPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('plans'))
        .then((collection) => {
          collection.createIndex({org: 1})
          return collection;
        }),
    operators: ['$regex', '$options']
  }
}
