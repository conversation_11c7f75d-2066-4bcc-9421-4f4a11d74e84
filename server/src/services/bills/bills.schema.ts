// TypeBox schema for bills service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const billsSchema = Type.Object({
  _id: ObjectIdSchema(),
  account: ObjectIdSchema(),
  person: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  billNumber: Type.Optional(Type.String()),
  invoiceNumber: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  amount: Type.Number(),
  subtotal: Type.Optional(Type.Number()),
  tax: Type.Optional(Type.Number()),
  fees: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number()),
  currency: Type.Optional(Type.String()),
  dueDate: Type.Optional(Type.Any()),
  issueDate: Type.Optional(Type.Any()),
  paidDate: Type.Optional(Type.Any()),
  status: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  tags: Type.Optional(Type.Array(Type.String())),
  lineItems: Type.Optional(Type.Array(Type.Object({
    description: Type.Optional(Type.String()),
    quantity: Type.Optional(Type.Number()),
    unitPrice: Type.Optional(Type.Number()),
    amount: Type.Optional(Type.Number()),
    category: Type.Optional(Type.String())
  ,
  // Missing fields from old schema
  to: Type.Optional(Type.String()),
  toName: Type.Optional(Type.String()),
}, { additionalProperties: false }))),
  payments: Type.Optional(Type.Array(ObjectIdSchema())),
  notes: Type.Optional(Type.String()),
  recurring: Type.Optional(Type.Boolean()),
  recurringPeriod: Type.Optional(Type.String()),
  nextBillDate: Type.Optional(Type.Any()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type Bills = Static<typeof billsSchema>
export const billsValidator = getValidator(billsSchema, dataValidator)
export const billsResolver = resolve<Bills, HookContext>({})
export const billsExternalResolver = resolve<Bills, HookContext>({})

export const billsDataSchema = Type.Object({
  ...Type.Omit(billsSchema, ['_id']).properties
}, { additionalProperties: false })

export type BillsData = Static<typeof billsDataSchema>
export const billsDataValidator = getValidator(billsDataSchema, dataValidator)
export const billsDataResolver = resolve<BillsData, HookContext>({})

export const billsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(billsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type BillsPatch = Static<typeof billsPatchSchema>
export const billsPatchValidator = getValidator(billsPatchSchema, dataValidator)
export const billsPatchResolver = resolve<BillsPatch, HookContext>({})

// Allow querying on any field from the main schema
const billsQueryProperties = billsSchema
export const billsQuerySchema = querySyntax(billsQueryProperties)
export type BillsQuery = Static<typeof billsQuerySchema>
export const billsQueryValidator = getValidator(billsQuerySchema, queryValidator)
export const billsQueryResolver = resolve<BillsQuery, HookContext>({})
