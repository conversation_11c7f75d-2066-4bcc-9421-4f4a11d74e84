// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    billsDataValidator,
    billsPatchValidator,
    billsQueryValidator,
    billsResolver,
    billsExternalResolver,
    billsDataResolver,
    billsPatchResolver,
    billsQueryResolver
} from './bills.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {BillsService, getOptions} from './bills.class.js'
import {billsPath, billsMethods} from './bills.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {logChange, logHistory, ping} from '../../utils/index.js';

export * from './bills.class.js'
export * from './bills.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['bills', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['bills', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = [];
    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create') {

            const existing = await loadExists(context, {});
            context = setExists(context, existing);
            //allow changes before approval
            if (existing) {
                if (existing.fromModel === 'orgs') {
                    const orgNamespace = `orgs:${existing.from}`;
                    cap_subjects.push(existing.from)
                    const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'providerAdmin'], [orgNamespace, 'billingAdmin'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'planAdmin']]
                    for (const w of orgWrite) {
                        ucanArgs.patch.unshift(w);
                        ucanArgs.update.unshift(w);
                        ucanArgs.remove.unshift(w);
                    }
                }
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['from/owner'], '*']],
        or: '*',
        cap_subjects
    })(context) as any;
}


const setTaxes = async (context:HookContext) => {
    const { taxOverrides } = context.params.$bills || { taxOverrides: false }
    if(taxOverrides) {
        let { taxes = [], settingsPath = 'settings.tax' } = taxOverrides;

        //TODO: with to address we can take the logic from the taxline component and move it entirely to this hook - triggered by a new payment
        if (taxes) {
            let { lineItems } = context.result;
            if (lineItems) {
                const setTaxes = line => {
                    let tax = taxes?.line?._id;
                    if(tax && line[settingsPath]?.taxOverrides) line[settingsPath].taxOverrides = tax;
                    return line;
                };

                let lines = lineItems.map(a => setTaxes(a));
                context.result = await new CoreCall('bills', context).patch(context.result._id, { lineItems: lines })
            }
        }
    }
    return context;
};

import { genPingArgs } from './config/index.js';
const sendPings = async (context: HookContext) => {
    const config = await genPingArgs(context);
    if (Array.isArray(context.result)) {
        const sendOne = async (res: any) => {
            const modifiedContext = {...context, result: res} as any;
            const modifiedConfig = await genPingArgs(modifiedContext)
            return await ping(modifiedConfig)(modifiedContext)
        }
        await Promise.all(context.result.map(a => sendOne(a)))
        return context;
    } else return await ping(config)(context);
}

// A configure function that registers the service and its hooks via `app.configure`
export const bills = (app: Application) => {
    // Register our service on the Feathers application
    app.use(billsPath, new BillsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: billsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(billsPath).hooks({
        around: {
            all: [schemaHooks.resolveExternal(billsExternalResolver), schemaHooks.resolveResult(billsResolver)]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(billsQueryValidator),
                schemaHooks.resolveQuery(billsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(billsDataValidator),
                schemaHooks.resolveData(billsDataResolver),
                sendPings
            ],
            patch: [
                schemaHooks.validateData(billsPatchValidator),
                schemaHooks.resolveData(billsPatchResolver),
                setTaxes,
                logHistory(['to', 'toName', 'toEmail', 'from', 'fromName', 'fromEmail', 'billDate', 'dueDate', 'settings', 'status', 'lineItems']),
                sendPings
            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [billsPath]: BillsService
    }
}
