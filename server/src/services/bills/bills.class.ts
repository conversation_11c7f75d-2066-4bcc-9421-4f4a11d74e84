// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Bills, BillsData, BillsPatch, BillsQuery } from './bills.schema.js'

export type { Bills, BillsData, BillsPatch, BillsQuery }

export interface BillsParams extends MongoDBAdapterParams<BillsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class BillsService<ServiceParams extends Params = BillsParams> extends MongoDBService<
  Bills,
  BillsData,
  BillsParams,
  BillsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('bills'))
  }
}
