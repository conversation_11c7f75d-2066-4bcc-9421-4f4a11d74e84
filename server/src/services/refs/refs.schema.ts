// TypeBox schema for refs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const refsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  reference: Type.Optional(ObjectIdSchema()),
  referenceType: Type.Optional(Type.String()),
  value: Type.Optional(Type.Any()),
  metadata: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type Refs = Static<typeof refsSchema>
export const refsValidator = getValidator(refsSchema, dataValidator)
export const refsResolver = resolve<Refs, HookContext>({})
export const refsExternalResolver = resolve<Refs, HookContext>({})

export const refsDataSchema = Type.Object({
  ...Type.Omit(refsSchema, ['_id']).properties
}, { additionalProperties: false })

export type RefsData = Static<typeof refsDataSchema>
export const refsDataValidator = getValidator(refsDataSchema, dataValidator)
export const refsDataResolver = resolve<RefsData, HookContext>({})

export const refsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(refsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type RefsPatch = Static<typeof refsPatchSchema>
export const refsPatchValidator = getValidator(refsPatchSchema, dataValidator)
export const refsPatchResolver = resolve<RefsPatch, HookContext>({})

// Allow querying on any field from the main schema
const refsQueryProperties = refsSchema
export const refsQuerySchema = querySyntax(refsQueryProperties)
export type RefsQuery = Static<typeof refsQuerySchema>
export const refsQueryValidator = getValidator(refsQuerySchema, queryValidator)
export const refsQueryResolver = resolve<RefsQuery, HookContext>({})
