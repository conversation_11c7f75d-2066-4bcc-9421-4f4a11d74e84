// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Refs, RefsData, RefsPatch, RefsQuery } from './refs.schema.js'

export type { Refs, RefsData, RefsPatch, RefsQuery }

export interface RefsParams extends MongoDBAdapterParams<RefsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class RefsService<ServiceParams extends Params = RefsParams> extends MongoDBService<
  Refs,
  RefsData,
  RefsParams,
  RefsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('refs')),
    operators: ['$regex', '$text', '$search', '$options']
  }
}
