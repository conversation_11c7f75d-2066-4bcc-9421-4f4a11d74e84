// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Claims, ClaimsData, ClaimsPatch, ClaimsQuery } from './claims.schema.js'

export type { Claims, ClaimsData, ClaimsPatch, ClaimsQuery }

export interface ClaimsParams extends MongoDBAdapterParams<ClaimsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ClaimsService<ServiceParams extends Params = ClaimsParams> extends MongoDBService<
  Claims,
  ClaimsData,
  ClaimsParams,
  ClaimsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient')
        .then((db) => db.collection('claims'))
        .then((collection) => {
          collection.createIndex({visit: 1})
          return collection;
        }),
  }
}
