// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    camsDataValidator,
    camsPatchValidator,
    camsQueryValidator,
    camsResolver,
    camsExternalResolver,
    camsDataResolver,
    camsPatchResolver,
    camsQueryResolver
} from './cams.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {CamsService, getOptions} from './cams.class.js'
import {camsPath, camsMethods} from './cams.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {getJoin, logChange, relate} from '../../utils/index.js';
import {compSources} from '../comps/utils/index.js';

export * from './cams.class.js'
export * from './cams.schema.js'

const authenticate = async (context: HookContext) => {
    const creator = [['cams', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['cams', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: [...creator],
        update: [...creator],
        remove: [...deleter]
    };
    const cap_subjects:any = []

    if (!['create', 'get', 'find'].includes(context.method)) {
        const existing = await loadExists(context);
        context = setExists(context, existing);
        if (existing?.org) cap_subjects.push(existing.org)
        const orgWrite:any = existing ? [[`orgs:${existing.org}`, 'WRITE'], [`orgs:${existing.org}`, 'orgAdmin']] : [['orgs', 'WRITE']]
        for(const w of orgWrite) {
            ucanArgs.patch.unshift(w);
            ucanArgs.update.unshift(w);
            ucanArgs.remove.unshift(w);
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        loginPass: [[['person/owner'], '*']],
        adminPass: ['patch', 'create', 'remove'],
        cap_subjects,
        or: '*'
    })(context) as any;
}

const relatePerson = async (context: HookContext): Promise<HookContext> => {
    if (!context.params._init_cam) {
        return await relate('otm', {
            herePath: 'person',
            therePath: 'cams',
            thereService: 'ppls',
            paramsName: 'cams_to_ppls'
        })(context)
            .catch(err => {
                console.error(`Error relating person to cam on ${context.method} - ${context.type}: ${err.message}`)
                if(context.method === 'remove') return context;
                else throw new Error(`Error relating compensation to person: ${err.message}`)
            })
    }
    return context;
}

const runJoins = async (context: HookContext): Promise<HookContext> => {
    const { cams_person, comp_total, cams_enrollments, cams_comp } = context.params.runJoin || {}
    if(cams_person && context.method !== 'remove') context = await getJoin({
        service: 'ppls',
        herePath: 'person',
        params: { query: { $select: ['_id', 'login', 'name', 'phone', 'email', 'cams', 'inOrgs', 'inGroups']}}
    })(context);
    if(comp_total){
        const getTotal = (cam) => {
            const income = compSources(cam, 'year');
            cam._fastjoin = {...cam._fastjoin || {}, income}
            return cam;
        }
        if (context.method === 'find') context.result.data = (context.result.data || [])?.map(a => getTotal(a))
        else context.result = getTotal(context.result);
    }
    if(cams_comp) context = await getJoin({ herePath: 'comp', service: 'comps' })(context)
    if(cams_enrollments) return getJoin({
        service: 'ppls',
        herePath:  'person',
        joinPath: 'enrollments',
        through: {
            'enrollments': {
                joinParent: 'person',
                service: 'enrollments',
                find: true,
                q: (person) => {
                    return {
                        person: person._id,
                        $sort: {version: -1}
                    }
                }
            }
        }
    })(context)
    return context;
}


const removeManyFromClient = async (context:HookContext):Promise<HookContext> => {
    const $in = context.params.query?._id?.$in;
    if(context.id && $in){
        const rmv = async (id) => {
            await new CoreCall('cams', context).remove(id)
        }
        await Promise.all($in.map(a => rmv(a)));
    }
    return context;
}

const dedup = async (context:HookContext):Promise<HookContext> => {
    if(!context.params._skip_dedup) {
        const ex = await new CoreCall('cams', context).find({
            query: {
                $limit: 1,
                org: context.data.org,
                person: context.data.person
            }
        })
        if (ex.total) context.result = ex.data[0];
    }
    return context;
}

//include org from plan groups into the query
const fromGroups = async (context:HookContext):Promise<HookContext> => {
    if(context.params.runJoin?.cams_groups){
        const groupIds = context.params.runJoin.cams_groups;
        const groups = await new CoreCall('groups', context).find({ query: { _id: { $in: groupIds }}, runJoin: { groupOrg: true }})
        context.params.query.org = { $in: groups.data.map(a => a._fastjoin.org?._id ).filter(a => !!a)}
    }
    return context;
}

const handleTermination = async (context:HookContext):Promise<HookContext> => {
    if(context.data.terminated){
        const ex = await loadExists(context, { params: { runJoin: { cams_enrollments: true } }});
        context = setExists(context, ex);
        if(!ex.terminated){
            const { terminated, terminatedAt, terminatedBy } = context.data;
            const removeIds:any = [];
            const termOne = async (cam:any) => {
                const person = cam._fastjoin.person;
                const enrollment = cam._fastjoin.enrollments[0];
                const patchedEnrollment = await new CoreCall('enrollments', context).patch(cam._fastjoin.enrollments[0]._id, { terminated, terminatedAt, terminatedBy })
                    .catch(err => {
                        console.error(`Could not terminate enrollment: ${err.message}`)
                    })
                if(patchedEnrollment) {
                    removeIds.push(cam._id);
                    const groups = await new CoreCall('groups', context).find({
                        query: {
                            org: ex.org,
                            $limit: 25,
                            members: {$in: [person._id]}
                        }
                    })
                        .catch(err => console.error(`Could not remove person from groups - error finding groups: ${err.message}`))
                    await new CoreCall('ppls', context)._patch(cam.person, { $pull: { inGroups: { $each: groups?.data || []}, cams: cam._id }}, { skip_hooks: true, admin_pass: true})
                        .catch(err => {
                            console.error(`Error patching person on cams termination. Person id: ${cam.person}; cam id: ${cam._id}; err: ${err.message}`)
                        })
                    if (groups) {
                        await new CoreCall('groups', context)._patch(null, { $pull: { members: person._id }}, { skip_hooks: true, admin_pass: true, query: { _id: { $in: groups.data.map(a => a._id) } }})
                            .catch(err => console.error(`Could not remove person from groups - error patching groups: ${err.message}`))
                    }
                }
            }
            if(context.params.runJoin?.multi_term){
                await Promise.all(context.params.runJoin.multi_term.ids.map(a => termOne(a)))
                await Promise.all(removeIds.filter(a => a !== context.id).map(a => new CoreCall('cams', context).patch(a, context.data)))
            } else await termOne(ex)
        }
    }
    return context;
}
// A configure function that registers the service and its hooks via `app.configure`
export const cams = (app: Application) => {
    // Register our service on the Feathers application
    app.use(camsPath, new CamsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: camsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(camsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(camsExternalResolver),
                schemaHooks.resolveResult(camsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(camsQueryValidator),
                schemaHooks.resolveQuery(camsQueryResolver)
            ],
            find: [fromGroups],
            get: [],
            create: [
                dedup,
                schemaHooks.validateData(camsDataValidator),
                schemaHooks.resolveData(camsDataResolver),
                relatePerson
            ],
            patch: [
                schemaHooks.validateData(camsPatchValidator),
                schemaHooks.resolveData(camsPatchResolver),
                handleTermination,
                relatePerson
            ],
            remove: [
                removeManyFromClient,
                relatePerson
            ]
        },
        after: {
            all: [runJoins],
            find: [],
            create: [relatePerson],
            patch: [relatePerson],
            remove: [relatePerson]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [camsPath]: CamsService
    }
}
