// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Cams, CamsData, CamsPatch, CamsQuery } from './cams.schema.js'

export type { Cams, CamsData, CamsPatch, CamsQuery }

export interface CamsParams extends MongoDBAdapterParams<CamsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class CamsService<ServiceParams extends Params = CamsParams> extends MongoDBService<
  Cams,
  CamsData,
  CamsParams,
  CamsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('cams'))
  }
}
