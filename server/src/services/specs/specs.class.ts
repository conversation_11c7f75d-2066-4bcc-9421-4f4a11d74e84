// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Specs, SpecsData, SpecsPatch, SpecsQuery } from './specs.schema.js'

export type { Specs, SpecsData, SpecsPatch, SpecsQuery }

export interface SpecsParams extends MongoDBAdapterParams<SpecsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class SpecsService<ServiceParams extends Params = SpecsParams> extends MongoDBService<
  Specs,
  SpecsData,
  SpecsParams,
  SpecsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient')
        .then((db) => db.collection('specs'))
  }
}
