// TypeBox schema for specs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const specsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  properties: Type.Optional(Type.Record(Type.String(), Type.Any())),
  version: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
,
  // Missing fields from old schema
  org: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  planYear: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Specs = Static<typeof specsSchema>
export const specsValidator = getValidator(specsSchema, dataValidator)
export const specsResolver = resolve<Specs, HookContext>({})
export const specsExternalResolver = resolve<Specs, HookContext>({})

export const specsDataSchema = Type.Object({
  ...Type.Omit(specsSchema, ['_id']).properties
}, { additionalProperties: false })

export type SpecsData = Static<typeof specsDataSchema>
export const specsDataValidator = getValidator(specsDataSchema, dataValidator)
export const specsDataResolver = resolve<SpecsData, HookContext>({})

export const specsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(specsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type SpecsPatch = Static<typeof specsPatchSchema>
export const specsPatchValidator = getValidator(specsPatchSchema, dataValidator)
export const specsPatchResolver = resolve<SpecsPatch, HookContext>({})

// Allow querying on any field from the main schema
const specsQueryProperties = specsSchema
export const specsQuerySchema = querySyntax(specsQueryProperties)
export type SpecsQuery = Static<typeof specsQuerySchema>
export const specsQueryValidator = getValidator(specsQuerySchema, queryValidator)
export const specsQueryResolver = resolve<SpecsQuery, HookContext>({})
