// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    contractsDataValidator,
    contractsPatchValidator,
    contractsQueryValidator,
    contractsResolver,
    contractsExternalResolver,
    contractsDataResolver,
    contractsPatchResolver,
    contractsQueryResolver
} from './contracts.schema.js'

import type {Application} from '../../declarations.js'
import {ContractsService, getOptions} from './contracts.class.js'
import {contractsPath, contractsMethods} from './contracts.shared.js'
import {HookContext} from '../../declarations.js';
import {allUcanAuth, anyAuth, CapabilityParts, loadExists, setExists} from 'feathers-ucan'
import { logChange } from '../../utils/index.js';

export * from './contracts.class.js'
export * from './contracts.schema.js'

const authenticate = async (context: HookContext) => {
    const deleter = [['contracts', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: deleter,
        update: deleter,
        remove: deleter
    };
    const cap_subjects:any = []
    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create') {

            const existing = await loadExists(context);
            context = setExists(context, existing);
            if (existing.ownerService === 'orgs') {
                if (existing.owner) {
                    const orgNamespace = `orgs:${existing.owner}`;
                    cap_subjects.push(existing.owner)
                    const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin']]
                    for (const w of orgWrite) {
                        ucanArgs.patch.unshift(w);
                        ucanArgs.update.unshift(w);
                        ucanArgs.remove.unshift(w);
                    }
                }
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        loginPass: [[['owner/owner'], '*']],
        adminPass: ['patch', 'create', 'remove'],
        cap_subjects
    })(context) as any;
}

const protect = async (context: HookContext) => {
    const ex = await loadExists(context);
    if (ex.status !== 'open' && !context.params.admin_pass) throw new Error('You cannot change a contract once it has been sent - make a copy to edit');
    else if(context.data.status && context.data.status !== 'open') context.data.public = false;
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const contracts = (app: Application) => {
    // Register our service on the Feathers application
    app.use(contractsPath, new ContractsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: contractsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(contractsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(contractsExternalResolver),
                schemaHooks.resolveResult(contractsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(contractsQueryValidator),
                schemaHooks.resolveQuery(contractsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(contractsDataValidator),
                schemaHooks.resolveData(contractsDataResolver)
            ],
            patch: [
                protect,
                schemaHooks.validateData(contractsPatchValidator),
                schemaHooks.resolveData(contractsPatchResolver)
            ],
            update: [protect],
            remove: [protect]
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [contractsPath]: ContractsService
    }
}
