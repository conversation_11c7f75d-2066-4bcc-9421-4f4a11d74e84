// TypeBox schema for contracts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, MandateSchema } from '../../utils/common/typebox-schemas.js'

export const contractsSchema = Type.Object({
  _id: ObjectIdSchema(),
  org: ObjectIdSchema(),
  name: Type.String(),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  parties: Type.Optional(Type.Array(ObjectIdSchema())),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  networks: Type.Optional(Type.Array(ObjectIdSchema())),
  providers: Type.Optional(Type.Array(ObjectIdSchema())),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  renewalDate: Type.Optional(Type.Any()),
  autoRenew: Type.Optional(Type.Boolean()),
  terms: Type.Optional(Type.String()),
  conditions: Type.Optional(Type.Array(Type.String())),
  documents: Type.Optional(Type.Array(ImageSchema)),
  signatures: Type.Optional(Type.Array(MandateSchema)),
  status: Type.Optional(Type.String()),
  value: Type.Optional(Type.Number()),
  currency: Type.Optional(Type.String()),
  paymentTerms: Type.Optional(Type.String()),
  penalties: Type.Optional(Type.Record(Type.String(), Type.Any())),
  amendments: Type.Optional(Type.Array(Type.Object({
    date: Type.Optional(Type.Any()),
    description: Type.Optional(Type.String()),
    document: Type.Optional(ImageSchema)
  ,
  // Missing fields from old schema
  public: Type.Optional(Type.Boolean()),
}, { additionalProperties: false }))),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
}, { additionalProperties: false })

export type Contracts = Static<typeof contractsSchema>
export const contractsValidator = getValidator(contractsSchema, dataValidator)
export const contractsResolver = resolve<Contracts, HookContext>({})
export const contractsExternalResolver = resolve<Contracts, HookContext>({})

export const contractsDataSchema = Type.Object({
  ...Type.Omit(contractsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ContractsData = Static<typeof contractsDataSchema>
export const contractsDataValidator = getValidator(contractsDataSchema, dataValidator)
export const contractsDataResolver = resolve<ContractsData, HookContext>({})

export const contractsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(contractsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type ContractsPatch = Static<typeof contractsPatchSchema>
export const contractsPatchValidator = getValidator(contractsPatchSchema, dataValidator)
export const contractsPatchResolver = resolve<ContractsPatch, HookContext>({})

// Allow querying on any field from the main schema
const contractsQueryProperties = contractsSchema
export const contractsQuerySchema = querySyntax(contractsQueryProperties)
export type ContractsQuery = Static<typeof contractsQuerySchema>
export const contractsQueryValidator = getValidator(contractsQuerySchema, queryValidator)
export const contractsQueryResolver = resolve<ContractsQuery, HookContext>({})
