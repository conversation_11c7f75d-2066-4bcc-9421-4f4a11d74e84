// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Contracts, ContractsData, ContractsPatch, ContractsQuery } from './contracts.schema.js'

export type { Contracts, ContractsData, ContractsPatch, ContractsQuery }

export interface ContractsParams extends MongoDBAdapterParams<ContractsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ContractsService<ServiceParams extends Params = ContractsParams> extends MongoDBService<
  Contracts,
  ContractsData,
  ContractsParams,
  ContractsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('contracts')),
    operators: ['$regex', '$options']
  }
}
