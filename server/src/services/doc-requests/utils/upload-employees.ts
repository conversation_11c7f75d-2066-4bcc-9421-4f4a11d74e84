import { HookContext} from '../../../declarations.js';
import xlsx from 'node-xlsx'
import {_set} from '../../../utils/index.js';
export const uploadEmployees = async (context: HookContext) => {
    const {upload_employees} = context.params.runJoin || {};
    if (upload_employees) {
        if (context.type === 'before') {
            const {sheet, headers, omitFirstRow} = upload_employees;
            const sheets = xlsx.parse(context.params.file.buffer);
            const csvData = sheets.filter(a => a.name === sheet)[0].data.slice(omitFirstRow ? 1 : 0);

            const errs: { row: number, data: any, key: string, err: string, throw?: boolean }[] = [];

            // Parse date string to age or return the date
            const parseDate = (dateStr: string) => {
                try {
                    const parts = dateStr.split('/');
                    if (parts.length === 3) {
                        const month = parseInt(parts[0]);
                        const day = parseInt(parts[1]);
                        const year = parseInt(parts[2]);

                        if (month > 0 && month <= 12 && day > 0 && day <= 31 && year > 1900) {
                            const birthDate = new Date(year, month - 1, day);
                            const today = new Date();
                            let age = today.getFullYear() - birthDate.getFullYear();
                            const monthDiff = today.getMonth() - birthDate.getMonth();

                            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                                age--;
                            }

                            return age;
                        }
                    }
                    return dateStr;
                } catch (error) {
                    return dateStr;
                }
            };

            // Format function to validate and transform data
            const format = (row: number, key: string, data: any) => {
                return {
                    'age': async (val: any) => {
                        if (!val && val !== 0) return undefined;

                        let result;
                        let spl: any = [];
                        if (typeof val === 'string') spl = val.split('/');

                        if (spl.length > 2) {
                            result = parseDate(val);
                        } else {
                            result = Number(val);
                            if (isNaN(result)) result = val;
                        }

                        // Validation
                        if (typeof result !== 'number' || result < 0 || result > 120) {
                            errs.push({row, key, err: 'Invalid Age', data});
                            return undefined;
                        }

                        return result;
                    },
                    'wage': async (val: any) => {
                        if (!val && val !== 0) return undefined;

                        let result;
                        if (typeof val === 'number') {
                            result = val;
                        } else {
                            result = Number(String(val).replace(/[^\d.]/g, ''));
                        }

                        // Validation
                        if (isNaN(result)) {
                            errs.push({row, key, err: 'Invalid Income', data});
                            return undefined;
                        }

                        return result;
                    },
                    'hourly': async (val: any) => {
                        if (!val) return 'N';

                        if (typeof val === 'string') {
                            return val.charAt(0).toUpperCase() === 'Y' ? 'Y' : 'N';
                        }

                        return val ? 'Y' : 'N';
                    },
                    'hours': async (val: any) => {
                        if (!val && val !== 0) return undefined;

                        let result;
                        if (typeof val === 'number') {
                            result = val;
                        } else {
                            result = Number(String(val).replace(/[^\d.]/g, ''));
                        }

                        // Validation
                        if (isNaN(result) || result < 0 || result > 168) {
                            errs.push({row, key, err: 'Invalid Hours', data});
                            return undefined;
                        }

                        return result;
                    },
                    'married': async (val: any) => {
                        if (!val) return 'N'
                        if (typeof val === 'string') {
                            const v = val.trim().slice(0, 1).toUpperCase()
                            if (['Y', 'N'].includes(v)) return v;
                        }
                        return 'N'
                    },
                    'deps': async (val: any) => {
                        if (!val) return 0;
                        return Number(val)
                    },
                    'zip': async (val: any) => {
                        const v = val ? String(val) : '';
                        return v.replace(/\D/g, '');
                    },
                    'firstName': async (val: any) => {
                        if(!val) return row[headers.name]?.split(',').join('').split(' ')[0] || ''
                        return val;
                    },
                    'lastName': async (val: any) => {
                        if(!val) return row[headers.name]?.split(',').join('').slice(1).join(' ') || ''
                        return val;
                    },
                    'email': async (val: any) => {
                        if(!val) return undefined
                        if(/^(([^<>()[\]\\.,;:\s@']+(\.[^<>()[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/.test(val)) return val;
                        return undefined;
                    },
                    'role': async (val: any) => {
                        if(!val) return ''
                        return val;
                    },
                    'w9': async (val: any) => !!val
                }[key];
            };

            // Process each row of data
            const processedData: any = [];
            for (let i = 0; i < csvData.length; i++) {
                const row = csvData[i];
                if (!row || row.length === 0) continue;

                // Convert row array to object using headers
                const rowData: any = {};
                for (const key in headers) {
                    const colIndex = headers[key];
                    if (colIndex < row.length) {
                        rowData[key] = row[colIndex];
                    }
                }

                // Process each field with the format function
                const processedRow: any = {};
                for (const key in headers) {
                    const formatter = format(i, key, rowData) as any;
                    if (formatter) {
                        processedRow[key] = await formatter(rowData[key], rowData);
                    } else {
                        processedRow[key] = rowData[key];
                    }
                }

                processedData.push(processedRow);
            }

            const errors = errs.filter(a => a.throw)
            // Check if there are any errors
            if (errors.length > 0) {
                throw new Error(`Errors found in data: ${errors[0].err}`);
            }

            const employees: any = []
            const t = new Date().getTime();
            for (let i = 0; i < processedData.length; i++) {
                const ee = processedData[i] as any
                employees.push({
                    ...ee,
                    uid: `${i}-${t}`
                })
            }
            context.params._uploaded = {
                errors: errs,
                added: employees
            }
            // Add the processed data to the context
            context.data.employees = employees
        } else {
            context.result = _set(context.result, '_fastjoin.uploaded', context.params._uploaded)
        }
    }
    return context;

}
