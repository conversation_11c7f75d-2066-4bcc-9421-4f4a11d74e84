// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { DocRequests, DocRequestsData, DocRequestsPatch, DocRequestsQuery } from './doc-requests.schema.js'

export type { DocRequests, DocRequestsData, DocRequestsPatch, DocRequestsQuery }

export interface DocRequestsParams extends MongoDBAdapterParams<DocRequestsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class DocRequestsService<ServiceParams extends Params = DocRequestsParams> extends MongoDBService<
  DocRequests,
  DocRequestsData,
  DocRequestsParams,
  DocRequestsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('DocRequests'))
  }
}
