// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Challenges, ChallengesData, ChallengesPatch, ChallengesQuery } from './challenges.schema.js'

export type { Challenges, ChallengesData, ChallengesPatch, ChallengesQuery }

export interface ChallengesParams extends MongoDBAdapterParams<ChallengesQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class ChallengesService<ServiceParams extends Params = ChallengesParams> extends MongoDBService<
  Challenges,
  ChallengesData,
  ChallengesParams,
  ChallengesPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('challenges'))
  }
}
