// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    medsDataValidator,
    medsPatchValidator,
    medsQueryValidator,
    medsResolver,
    medsExternalResolver,
    medsDataResolver,
    medsPatchResolver,
    medsQueryResolver
} from './meds.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {MedsService, getOptions} from './meds.class.js'
import {medsPath, medsMethods} from './meds.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall} from 'feathers-ucan';
import {_flatten, logChange} from '../../utils/index.js';

export * from './meds.class.js'
export * from './meds.schema.js'

import {searchApi} from '../procedures/utils/search.js';
import axios from 'axios';
import {hackIdAfter, hackIdBefore} from '../cross-sections/utils/index.js';

export const getMedNdcs = async (rxcui: string) => {
    const ndcRes = await axios.get(`https://rxnav.nlm.nih.gov/REST/rxcui/${rxcui}/ndcs.json`)
        .catch(err => {
            console.error(`Error calling ndc api for rxcui ${rxcui}: ${err.message}`);
            return;
        });
    const res = ndcRes?.data?.ndcGroup?.ndcList?.ndc || [];
    return res
}

export const getRelatedNdcs = async (ndc: string, concept:'drug'|'concept'|'product' = 'drug') => {
    const ndcRes = await axios.get(`https://rxnav.nlm.nih.gov/REST/relatedndc.json?ndc=${ndc}&relation=${concept}&status=active`)
        .catch(err => {
            console.error(`Error calling related ndcs for ${ndc}: ${err.message}`);
            return;
        });
    const res = ndcRes?.data?.ndcInfoList?.ndcInfo || [];
    return res
}

export const getMedInfo = async (rxcui: string) => {
    const related = await axios.get(`https://rxnav.nlm.nih.gov/REST/rxcui/${rxcui}/allrelated.json`)
        .catch(err => {
            console.error(`Error calling ndc info for rxcui ${rxcui}: ${err.message}`);
            return;
        });
    ;
    const obj = {};
    const arr = related?.data?.allRelatedGroup?.conceptGroup || []
    for (const tty of arr) {
        const subObj = {};
        let run;
        for (const sub of tty.conceptProperties || []) {
            run = true;
            subObj[sub.rxcui] = sub;
        }
        if (run) obj[tty.tty] = subObj;
    }
    return obj;
}
const addAllInfo = async (context: HookContext): Promise<HookContext> => {
    const {rxcui, info, ndcs} = context.data;

    if (rxcui && !info || !ndcs) {
        const related = info ? info : await getMedInfo(rxcui);
        let ndcRes;
        if (ndcs) ndcRes = ndcs;
        else ndcRes = await getMedNdcs(rxcui);
        context.data.info = related;
        context.data.ndcs = ndcRes;
    }
    return context;
}

const authenticate = async (context: HookContext) => {
    const deleter = [['meds', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: deleter,
        update: deleter,
        remove: deleter
    };
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove']
    })(context) as any;
}

const ndcsDeeper = async (context: HookContext) => {
    if (context.params.runJoin?.ndc_lookup) {
        const rxcuis = Object.keys(context.result.info?.SBD || {});
        const byId = {};
        const ex = await new CoreCall('meds', context).find({query: {rxcui: {$in: rxcuis}, $limit: rxcuis.length}})
        for (let i = 0; i < ex.data.length; i++) {
            byId[ex.data[i].rxcui] = ex.data[i];
        }

        const allNdcs: Array<string> = []
        const promises:any = []
        for (const rxcui of rxcuis) {
            const v = context.result.info.SBD[rxcui];
            const fn = async () => {
                const ndcs = await getMedNdcs(rxcui);
                if (ndcs.length) {
                    if (byId[rxcui]) {
                        if (byId[rxcui].ndcs?.length ! == ndcs.length) {
                            try {
                                await new CoreCall('meds', context).patch(byId[rxcui]._id, {$addToSet: {ndcs: {$each: ndcs}}});
                            } catch(e:any) {
                                console.log(`Error patching med for ndcs:${ndcs} - ${e.message}`)
                            }
                        }
                    } else {
                        try {
                            await new CoreCall('meds', context).create({
                                name: v.name,
                                synonyms: v.synonym,
                                rxcui,
                                ndcs,
                                sbdOf: context.result._id
                            })
                        } catch(e:any) {
                            console.log(`Error creating new med for ndcs: ${ndcs} - ${e.message}`)
                        }
                    }
                    for(let i = 0; i < ndcs.length; i++) {
                        allNdcs.push(ndcs[i])
                    }
                }

            }
            promises.push(fn())
        }
        await Promise.all(promises);
        if (allNdcs.filter(a => !!a).length) context.result = await new CoreCall('meds', context).patch(context.result._id, {ndcs:allNdcs.filter(a => !!a)})
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const meds = (app: Application) => {
    // Register our service on the Feathers application
    app.use(medsPath, new MedsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: medsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(medsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(medsExternalResolver),
                schemaHooks.resolveResult(medsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(medsQueryValidator),
                schemaHooks.resolveQuery(medsQueryResolver)
            ],
            find: [hackIdBefore('plan_meds', 'plans', 'meds')],
            get: [],
            create: [
                addAllInfo,
                schemaHooks.validateData(medsDataValidator),
                schemaHooks.resolveData(medsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(medsPatchValidator),
                schemaHooks.resolveData(medsPatchResolver)
            ],
            remove: []
        },
        after: {
            all: [],
            find: [searchApi('meds'), hackIdAfter('plan_meds', 'plans')],
            get: [ndcsDeeper],
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [medsPath]: MedsService
    }
}
