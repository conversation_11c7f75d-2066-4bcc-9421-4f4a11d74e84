// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Meds, MedsData, MedsPatch, MedsQuery } from './meds.schema.js'

export type { Meds, MedsData, MedsPatch, MedsQuery }

export interface MedsParams extends MongoDBAdapterParams<MedsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class MedsService<ServiceParams extends Params = MedsParams> extends MongoDBService<
  Meds,
  MedsData,
  MedsParams,
  MedsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('meds'))
        .then((collection) => {
          collection.createIndex({rxcui: 1}, {unique: true})
          return collection;
        }),
      multi: true,
      operators: ['$regex', '$options']

  }
}
