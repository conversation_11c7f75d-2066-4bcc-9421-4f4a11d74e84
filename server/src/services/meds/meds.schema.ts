// TypeBox schema for meds service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const medsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  genericName: Type.Optional(Type.String()),
  brandName: Type.Optional(Type.String()),
  ndc: Type.Optional(Type.String()),
  rxcui: Type.Optional(Type.String()),
  strength: Type.Optional(Type.String()),
  dosageForm: Type.Optional(Type.String()),
  route: Type.Optional(Type.String()),
  manufacturer: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  class: Type.Optional(Type.String()),
  controlled: Type.Optional(Type.Boolean()),
  schedule: Type.Optional(Type.String()),
  indications: Type.Optional(Type.Array(Type.String())),
  contraindications: Type.Optional(Type.Array(Type.String())),
  sideEffects: Type.Optional(Type.Array(Type.String())),
  interactions: Type.Optional(Type.Array(ObjectIdSchema())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
,
  // Missing fields from old schema
  standard: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Meds = Static<typeof medsSchema>
export const medsValidator = getValidator(medsSchema, dataValidator)
export const medsResolver = resolve<Meds, HookContext>({})
export const medsExternalResolver = resolve<Meds, HookContext>({})

export const medsDataSchema = Type.Object({
  ...Type.Omit(medsSchema, ['_id']).properties
}, { additionalProperties: false })

export type MedsData = Static<typeof medsDataSchema>
export const medsDataValidator = getValidator(medsDataSchema, dataValidator)
export const medsDataResolver = resolve<MedsData, HookContext>({})

export const medsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(medsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type MedsPatch = Static<typeof medsPatchSchema>
export const medsPatchValidator = getValidator(medsPatchSchema, dataValidator)
export const medsPatchResolver = resolve<MedsPatch, HookContext>({})

// Allow querying on any field from the main schema
const medsQueryProperties = medsSchema
export const medsQuerySchema = querySyntax(medsQueryProperties)
export type MedsQuery = Static<typeof medsQuerySchema>
export const medsQueryValidator = getValidator(medsQuerySchema, queryValidator)
export const medsQueryResolver = resolve<MedsQuery, HookContext>({})
