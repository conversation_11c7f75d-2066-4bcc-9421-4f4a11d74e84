// // import <PERSON><PERSON> from 'stripe';
// import {HookContext} from '../../../../declarations';
// import {CoreCall} from 'feathers-ucan';
// import OpenAi from 'openai'
//
// export const getStripe = (context: HookContext, config?: Stripe.StripeConfig) => {
//     const key = context.app.get('banking').stripe.secret_key;
//     return new Stripe(key, config)
// }
//
// import {addressToStripe, orgToCompany} from './utils/index.js'
//
// type CreateAccount = {
//     email?: string
// }
//
// // merchant category code - a requirement for restricting credit card transactions
//
// // accounts in moov - treasury account in stripe - it's a business profile
// /** org is our orgs service*/
// export const accounts_create = (org: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context)
//         const company = orgToCompany(org);
//         const account: any = await stripe.accounts.create({
//             email: org.email,
//             business_type: org.structure === 'NONPROFIT' ? 'non_profit' : 'company',
//             // controller: {
//             //     fees: {
//             //         payer: 'application',
//             //     },
//             //     losses: {
//             //         payments: 'application',
//             //     },
//             //     stripe_dashboard: {
//             //         type: 'none',
//             //     }
//             // },
//             type: 'custom',
//             capabilities: {
//                 // card_issuing: { requested: true },
//                 // card_payments: { requested: true },
//                 transfers: {requested: true}
//                 // us_bank_account_ach_payments: { requested: true },
//                 // us_bank_transfer_payments: { requested: true }
//             },
//             company
//         })
//             .catch(err => {
//                 console.log(err);
//                 throw new Error(`Error creating stripe account: ${err.message}`)
//             })
//         await new CoreCall('orgs', context, {skipJoins: true}).patch(org._id, {$set: {'treasury.id': account.id}})
//         // context.data.$set = { ...context.data.$set, ['stripe.accounts.account.id']: account.id }
//         context.result = account;
//         return context;
//     }
// }
//
// // self explanatory
// /** accountId is the treasuryId - moov account id */
// export const add_capabilities = (accountId, caps?: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context)
//         const capabilities = caps || {
//             // treasury: {
//             //     requested: true,
//             // },
//             card_issuing: {
//                 requested: true,
//             },
//             us_bank_account_ach_payments: {
//                 requested: true,
//             }
//         }
//         context.result = await stripe.accounts.update(accountId, {capabilities})
//             .catch(err => {
//                 console.error(`Error requesting treasury capabiliteis for account id ${accountId}: ${err.message}`)
//             })
//         return context;
//     }
// }
//
// //I think moov had something like this as well which is creating a verification link
// /**the arguments are what was required by stripe */
// export const account_link = ({account, refresh_url, return_url, type}: Stripe.AccountLinkCreateParams) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context);
//         return stripe.accountLinks.create({account, refresh_url, return_url, type})
//             .catch(err => {
//                 console.log(err);
//             })
//     }
// }
//
// //this is just a consolidation of several of the above steps
// export const account_setup = (org: any, {refresh_url, return_url, linkType, capabilities}) => {
//     return async (context: HookContext) => {
//         context = await accounts_create(org)(context);
//         context = await add_capabilities(context.result.id, capabilities)(context);
//         const link = await account_link({account: context.result.id, return_url, refresh_url, type: linkType})(context)
//             .catch(err => {
//                 console.log(err);
//             })
//         context.result = {account: context.result, link}
//         return context;
//     }
// }
//
// //just get treasury account - moov account
// export const get_account = () => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context);
//         context.result = await stripe.accounts.retrieve(context.id as string);
//         return context;
//     }
// }
//
//
// export const get_account_link = ({account, return_url, refresh_url, linkType}) => {
//     return async (context: HookContext) => {
//         context.result = await account_link({account: account, return_url, refresh_url, type: linkType})(context)
//         return context;
//     }
// }
//
// export const account_update = (params: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context);
//         context.result = await stripe.accounts.update(context.id as string, params)
//         return context;
//     }
// }
//
// export const account_get = (id) => {
//     return async (context) => {
//         const stripe = getStripe(context);
//         const acct = await stripe.accounts.retrieve(id || context.id);
//         context.result = acct;
//         return context;
//     }
// }
//
//
// export const list_persons = (accountId: string, opts?: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context);
//         context.result = await stripe.accounts.listPersons(accountId, opts);
//     }
// }
// export const get_person = (accountId: string, personId: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context);
//         context.result = await stripe.accounts.retrievePerson(accountId, personId);
//         return context;
//     }
// }
//
// export const create_person = (accountId: string, pplId: string, person: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context);
//         context.result = await stripe.accounts.createPerson(accountId, person);
//         await new CoreCall('ppls', context, {skipJoins: true}).patch(pplId, {
//             $set: {
//                 [`stripeAccounts.${context.result.account}`]: {
//                     id: context.result.id,
//                     representative: context.result.relationship.representative
//                 }
//             }
//         }, {admin_pass: true});
//         return context;
//     }
// }
//
// export const update_person = (accountId: string, personId: string, person: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context);
//         context.result = await stripe.accounts.updatePerson(accountId, personId, person);
//         return context;
//     }
// }
//
// export const delete_person = (accountId: string, personId: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context);
//         context.result = await stripe.accounts.deletePerson(accountId, personId);
//         return context
//     }
// }
//
// export const get_external_account = (bankAccountId:string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.accounts.retrieveExternalAccount(context.id as string, bankAccountId);
//         return context;
//     }
// }
// export const update_external_account = (accountId: string, bankAccountId: string, params: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.accounts.updateExternalAccount(accountId, bankAccountId, params)
//         return context;
//     }
// }
// export const create_external_account = (params: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.accounts.createExternalAccount(context.id as string, params)
//         return context;
//     }
// }
// export const delete_external_account = (accountId: string, bankAccountId: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.accounts.deleteExternalAccount(accountId, bankAccountId)
//         return context;
//     }
// }
//
// export const disconnect_fca = (fca:string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.financialConnections.accounts.disconnect(fca);
//         return context;
//     }
// }
//
// export const cancel_setup_intent = (si:string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.setupIntents.cancel(si);
//         return context;
//     }
// }
//
// //financial account = moov wallet
// export const create_financial_account = (params: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.treasury.financialAccounts.create({supported_currencies: ['usd'], ...params});
//         return context;
//     }
// }
//
// export const update_financial_account = (faId: string, params: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.treasury.financialAccounts.update(faId, params);
//         return context;
//     }
// }
//
// export const get_financial_account = (id: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.treasury.financialAccounts.retrieve(id, {expand: ['financial_addresses.aba.account_number']});
//         return context;
//     }
// }
//
// export const get_multiple_fas = (ids: Array<string>) => {
//     return async (context: HookContext) => {
//         context.result = {data: []};
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         const loadAcct = async (id) => {
//             return await stripe.treasury.financialAccounts.retrieve(id, {expand: ['financial_addresses.aba.account_number']})
//                 .catch(err => {
//                     console.error(`Issue loading multiple fas ${id}: ${err.message}`)
//                     return undefined
//                 });
//         }
//         const data = await Promise.all(ids.map(a => loadAcct(a)))
//         context.result.data = data.filter(a => !!a);
//         return context;
//     }
// }
//
// export const connection_session = (data?:any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.financialConnections.sessions.create({
//             account_holder: {
//                 type: 'account',
//                 account: context.id as string
//             },
//             permissions: ['payment_method', 'balances', 'ownership', 'balances'],
//             ...data,
//             filters: {
//                 account_subcategories: ['checking', 'savings'],
//                 countries: ['us'],
//                 ...data?.filters
//             },
//         });
//     }
// }
// export const get_setup_intent = (id:string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.setupIntents.retrieve(id);
//         return context;
//     }
// }
// export const setup_intent = (accountId: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         const acct = await new CoreCall('accounts', context).get(accountId, {
//             encrypt: {unprotect: {accountNumber: true}},
//             runJoin: {account_owner: true}
//         });
//         if(acct.setupIntent){
//             const si = await get_setup_intent(acct.setupIntent)(context);
//             if(si?.status === 'succeeded') throw new Error(`This account has already been successfully verified - update account features or delete account, you cannot un-verify it.`)
//         }
//         const types = {'business': 'company', 'personal': 'individual'}
//         const owner = acct._fastjoin.owner;
//         const billing_details: any = {name: owner.name};
//         if (owner.address) billing_details.address = addressToStripe(owner.address);
//         if (owner.email) billing_details.email = owner.treasury?.business_profile?.support_email || owner.support_email || owner.email;
//         if (owner.phone) billing_details.phone = owner.phone.number.e164;
//         context.result = await stripe.setupIntents.create({
//             confirm: true,
//             attach_to_self: true,
//             mandate_data: {
//                 customer_acceptance: {
//                     type: 'online',
//                     accepted_at: new Date(acct.mandate.acceptedAt).getTime(),
//                     online: {
//                         ip_address: acct.mandate.ip,
//                         user_agent: acct.mandate.ua
//                     }
//                 }
//             },
//             usage: 'off_session',
//             payment_method_types: ['us_bank_account'],
//             flow_directions: ['inbound', 'outbound'],
//             payment_method_data: {
//                 type: 'us_bank_account',
//                 billing_details,
//                 us_bank_account: {
//                     account_holder_type: types[acct.type],
//                     account_number: acct.accountNumber,
//                     account_type: acct.accountType || 'checking',
//                     routing_number: acct.routingNumber
//                 }
//             }
//         })
//             .catch(err => {
//                 throw new Error(`Error verifying account: ${err.message}`)
//             })
//
//
//         if (context.result.id) await new CoreCall('accounts', context).patch(accountId, {$set: {stripePm: context.result.payment_method, setupIntent: context.result.id }}, {admin_pass: true})
//             .catch(err => console.error(`Error adding stripePm to accountId: ${accountId}: ${err.message}`))
//         return context;
//     }
// }
//
// export const add_otp = (acct: any, owner: any, amount:number) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         const billing_details: any = {name: owner.name};
//         if (owner.address) billing_details.address = addressToStripe(owner.address);
//         if (owner.email) billing_details.email = owner.treasury?.business_profile?.support_email || owner.support_email || owner.email;
//         if (owner.phone) billing_details.phone = owner.phone.number.e164;
//         context.result = await stripe.paymentIntents.create({
//             amount,
//             currency: 'usd',
//             payment_method_types: ['us_bank_account'],
//             confirm: true,
//             setup_future_usage: 'on_session',
//             mandate_data: {
//                 customer_acceptance: {
//                     type: 'online',
//                     accepted_at: new Date(acct.mandate.acceptedAt).getTime(),
//                     online: {
//                         ip_address: acct.mandate.ip,
//                         user_agent: acct.mandate.ua
//                     }
//                 }
//             },
//             payment_method_data: {
//                 type: 'us_bank_account',
//                 billing_details,
//                 us_bank_account: {
//                     account_holder_type: 'company',
//                     account_number: acct.accountNumber,
//                     account_type: acct.accountType || 'checking',
//                     routing_number: acct.routingNumber
//                 }
//             }
//             })
//         if(context.result?.id && acct._id) await new CoreCall('accounts', context, { skipJoins: true }).patch(acct._id, { latestPaymentIntent: context.result.id})
//         return context;
//     }
// }
//
// type InbountTransferParams = {
//     amount: number,
//     financial_account: string,
//     currency: 'usd',
//     origin_payment_method: string,
//     description: string,
//     statement_descriptor: string
// }
//
// //Use for Financial Account to Financial Account
// export const outbound_payment = (data:any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.treasury.outboundPayments.create({currency: 'usd', ...data})
//             .catch((err: any) => {
//                 throw new Error(`Error creating outbound payment: ${err.message}`);
//             });
//         return context;
//     }
// }
//
// export const inbound_transfer = ({
//                                      amount,
//                                      financial_account,
//                                      currency = 'usd',
//                                      origin_payment_method,
//                                      description = 'Plan Wallet Transfer',
//                                      statement_descriptor = 'CareAcct'
//                                  }: InbountTransferParams) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         // TODO: stripe go live
//         context.result = await stripe.treasury.inboundTransfers.create({
//             amount,
//             financial_account,
//             currency,
//             origin_payment_method,
//             description,
//             statement_descriptor
//         })
//             .catch((err: any) => {
//                 throw new Error(`Error creating inbound transfer: ${err.message}`);
//             });
//         // context.result = await stripe.testHelpers.treasury.receivedCredits.create({
//         //     amount,
//         //     financial_account,
//         //     currency,
//         //     network: 'ach'
//         // })
//         return context;
//     }
// }
//
// type TransactionSort = { gt?: number, gte?: number, lt?: number, lte?: number }
// type TransactionListOptions = {
//     limit: number,
//     created?: TransactionSort,
//     order_by?: 'created' | 'posted_at',
//     status: 'open' | 'posted' | 'void',
//     status_transitions?: { posted_at: TransactionSort }
//
// }
// export const list_transactions = (accountId: string, query: TransactionListOptions) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.treasury.transactions.list({
//             financial_account: accountId,
//             ...query
//         })
//         return context;
//     }
// }
//
// export const create_cardholder = (data: any, personId: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.issuing.cardholders.create({type: 'individual', ...data});
//         await new CoreCall('ppls', context, {skipJoins: true}).patch(personId, {$set: {stripeCardholderId: context.result.id}}, {admin_pass: true})
//             .catch(err => {
//                 console.error(`Error adding cardholder to person profile: ${err.message}`);
//                 throw new Error(`Could not add cardholder id to person profile: ${err.message}`);
//             })
//         return context;
//     }
// }
// export const update_cardholder = (id: string, data: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.issuing.cardholders.update(id, data)
//         return context;
//     }
// }
//
// export const get_cardholder = (id: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.issuing.cardholders.retrieve(id)
//         return context;
//     }
// }
//
// export const view_card = (id: string, nonce: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.ephemeralKeys.create({
//             nonce,
//             issuing_card: id
//         }, {apiVersion: context.app.get('banking').stripe.apiVersion})
//             .catch(err => {
//                 throw new Error(`Error generating ephemeral key: ${err.message}`)
//             })
//         return context;
//     }
// }
//
// export const get_card = (id: string, nonce?: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.issuing.cards.retrieve(id);
//         if (nonce) {
//             const key = await view_card(id, nonce)(context);
//             context.result.ephemeralKey = key.result
//         }
//         return context;
//     }
// }
//
// export const create_card = (data: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.issuing.cards.create({
//             type: 'virtual',
//             status: 'active',
//             currency: 'usd',
//             spending_controls: {spending_limits: [{amount: 1, interval: 'all_time'}]}, ...data
//         })
//             .catch(err => {
//                 console.error(`Error creating stripe card: ${err.message}`)
//                 throw new Error(`Error creating stripe card: ${err.message}`);
//             })
//         return context;
//     }
// }
// export const update_card = (id: string, data: any, careCardId?: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.issuing.cards.update(id, data);
//         if (context.result && careCardId) {
//             const patchObj: any = {};
//             if (data.status) patchObj.status = data.status;
//             if (Object.keys(patchObj).length) {
//                 await new CoreCall('care-cards', context).patch(careCardId, patchObj)
//                     .catch(err => {
//                         throw new Error(`Failed to update card ${context.result.id} at care card ${careCardId}: ${err.message}`)
//                     })
//             }
//         }
//         return context
//     }
// }
//
// export const card_transactions = (query: any) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.issuing.transactions.list({
//             ...query
//         })
//         return context;
//     }
// }
// export const test_transaction = (id: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         const t = await stripe.testHelpers.issuing.transactions.createForceCapture({
//             amount: 5000,
//             card: id,
//             currency: 'usd'
//         })
//         const r = await stripe.testHelpers.issuing.transactions.createUnlinkedRefund({
//             amount: 2000,
//             card: id,
//             currency: 'usd'
//         })
//         const pr = await stripe.testHelpers.issuing.transactions.refund(t.id, {refund_amount: 1000})
//         console.log('t', t.id, 'r', r.id, 'pr', pr.id);
//         return context;
//     }
// }
// export const test_authorization = (id: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         const authorization: any = await stripe.testHelpers.issuing.authorizations.create({
//             card: id,
//             amount: 100,
//             merchant_data: {
//                 category: 'automated_fuel_dispensers',
//             },
//             is_amount_controllable: true,
//         })
//             .catch(err => {
//                 console.log('error getting authorization', err.message);
//             })
//         console.log('authorization', authorization.id);
//     }
// }
//
// export const find_authorizations = (ids: Array<string>) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         const getAuth = async (id: string) => {
//             return await stripe.issuing.authorizations.retrieve(id)
//         }
//         const all = await Promise.all(ids.map(a => getAuth(a)));
//         context.result = {data: all, total: all.length}
//         return context;
//     }
// }
//
// type PI = {
//     amount: number,
//     currency: 'usd',
//     confirm?: boolean,
//     customer?: string,
//     description?: string,
//     off_session?: boolean,
//     payment_method?: string,
//     receipt_email?: string,
//     setup_future_usage?: 'off_session' | 'on_session',
//     statement_descriptor?: string,
//     on_behalf_of: string
//     return_url?: string
// }
// export const create_payment_intent = (pi: PI) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.paymentIntents.create(pi as Stripe.PaymentIntentCreateParams)
//         return context;
//     }
// }
// export const update_payment_intent = (id: string, pi: PI) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.paymentIntents.update(id, pi)
//         return context;
//     }
// }
//
// export const get_payment_intent = (id: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.paymentIntents.retrieve(id);
//         return context;
//     }
// }
// export const cancel_payment_intent = (id: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.paymentIntents.cancel(id);
//         return context;
//     }
// }
//
// export const capture_payment_intent = (id: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.paymentIntents.capture(id);
//         return context;
//     }
// }
//
// type Confirm = {
//     payment_method?: string,
//     receipt_email?: string,
//     setup_future_usage?: string,
//     return_url?: string
// }
// export const confirm_payment_intent = (id: string, opts?: Confirm) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.paymentIntents.confirm(id, opts as Stripe.PaymentIntentConfirmParams);
//     }
// }
//
// export const add_card = (customer: string, {source}: { source: string }) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.customers.createSource(customer, {source})
//         return context;
//     }
// }
// export const remove_card = (customer: string, pm: string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.customers.deleteSource(customer, pm);
//         return context;
//     }
// }
//
// export const create_customer = (orgId: string, {payment_method}: Stripe.CustomerCreateParams) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         const org = await new CoreCall('orgs', context, {skipJoins: true}).get(orgId, {admin_pass: true});
//         const customer: Stripe.CustomerCreateParams = {name: org.name, payment_method}
//         if (org.address) customer.address = addressToStripe(org.address)
//         if (org.email) customer.email = org.email;
//         if (org.phone) customer.phone = org.phone.number.e164
//         context.result = await stripe.customers.create(customer)
//         return context;
//     }
// }
//
// type LineItem = {
//     adjustable_quantity?: {
//         enabled: boolean,
//         maximum?: number,
//         minimum?: number
//     },
//     dynamic_tax_rates: Array<string>,
//     price?: string,
//     price_data?: {
//         currency: 'usd',
//         product?: string,
//         product_data?: {
//             name: string,
//             description?: string,
//             images?: Array<string>,
//             tax_code?: string
//         }
//     },
//     recurring?: {
//         interval: 'day' | 'week' | 'month' | 'year',
//         interval_count?: number
//     },
//     tax_behavior?: 'inclusive' | 'exclusive' | 'unspecified',
//     tax_rates?: Array<string>,
// }
// type Session = {
//     customer?: string,
//     customer_email?: string,
//     line_items?: Array<LineItem>,
//     mode: 'payment' | 'setup' | 'subscription',
//     return_url?: string,
//     success_url?: string
// }
// export const create_session = (paymentId:string, service:string, session: Session) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         const sesh = await stripe.checkout.sessions.create(session);
//         await new CoreCall(service, context, { skipJoins: true })._patch(paymentId, { $set: { checkoutSession: sesh.id }})
//         context.result = sesh;
//         return context;
//     }
// }
// export const expire_session = (sessionId:string) => {
//     return async (context: HookContext) => {
//         const stripe = getStripe(context, {stripeAccount: context.id as string});
//         context.result = await stripe.checkout.sessions.expire(sessionId);
//         return context;
//     }
// }
