// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type {
  PriceEstimates,
  PriceEstimatesData,
  PriceEstimatesPatch,
  PriceEstimatesQuery
} from './price-estimates.schema.js'

export type { PriceEstimates, PriceEstimatesData, PriceEstimatesPatch, PriceEstimatesQuery }

export interface PriceEstimatesParams extends MongoDBAdapterParams<PriceEstimatesQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class PriceEstimatesService<
  ServiceParams extends Params = PriceEstimatesParams
> extends MongoDBService<PriceEstimates, PriceEstimatesData, PriceEstimatesParams, PriceEstimatesPatch> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('price-estimates'))
        .then((collection) => {
          collection.createIndex({locationCode: 1}, {unique: true})
          return collection;
        }),
      multi: true
  }
}
