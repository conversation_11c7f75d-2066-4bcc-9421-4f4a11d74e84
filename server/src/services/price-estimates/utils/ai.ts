import OpenAi from 'openai';
import {HookContext} from '../../../declarations.js';
import {CoreCall} from 'feathers-ucan';
import {estimateBodySchema, priceKeys} from '../../price-estimates/price-estimates.schema.js';

type Options = {
    rerun?: boolean,
    searchList:Array<{ code: string, description?:string }>
    locationKey?:string
}
export const aiPriceSearch = ({rerun, searchList, locationKey}: Options) => {
    return async (context: HookContext) => {
        let list: any = [];
        const getLocationCode = (code:string) => {
            if(!locationKey) return code;
            return `${locationKey}:${code}`;
        }
        const locationCodes = searchList.map(a => getLocationCode(a.code)).filter(a => !!a);
        let existing = await new CoreCall('price-estimates', context)._find({ skip_hooks: true, admin_pass: true, query: { $limit: locationCodes.length, locationCode: { $in: locationCodes }}});
        const data:any = [];
        const remainingCodes:any[] = []
        const time = new Date().getTime();
        const timeout = 1000 * 60 * 60 * 24;
        const patchIds:any = {};
        const exCodes = existing.data.map(a => a.locationCode);
        for(let i = 0; i < searchList.length; i++) {
            const idx = exCodes.indexOf(getLocationCode(searchList[i].code));
            if(idx > -1){
                //recheck procedures without prices every 24 hours
                const { estimatedAt, code } = existing.data[idx];
                //add a day for every alt price already present.
                const addHours = (existing.data[idx].alts || []).length * (1000 * 60 * 60 * 24)
                if(!estimatedAt || time - new Date(estimatedAt).getTime() > (timeout + addHours)) {
                    //re-run estimate if no price exists
                    patchIds[code] = existing.data[idx];
                    remainingCodes.push(searchList[i])
                } else data.push(existing.data[idx]);
            } else remainingCodes.push(searchList[i]);
        }
        const runList = rerun ? searchList : remainingCodes;
        if (runList.length) {

            const {key, org} = context.app.get('openai');
            const openai = new OpenAi({apiKey: key, organization: org});
            //TODO: location specific prompts not yet implemented
            const result = await openai.chat.completions.create({
                messages: [{
                    role: 'system',
                    content: 'You are a resourceful medical procedure price search tool that finds sources such as medicare price files, cash-pay providers and surgery centers that publish pricing openly, and other data resources to provide reasonable price estimates for procedures and drugs by code or by description if no standard code is available.'
                },{
                    role: 'user',
                    content: `Return a JSON array containing price estimate information for each of the following objects - which contains { "code": CPT code, "description": description }. Here is the list as a JSON string ${JSON.stringify(runList)}. The estimates should consist of objects with the the following properties: { "medicare": the bundled price that medicare pays for this procedure, "cash": see if there are any published cash prices such as private medical providers would publish to their website for this procedure, "description": a short description of how you came up with a cash estimate - or why you couldn't find one, "code": CPT code - infer from description if there is good probability for a match, "rxcui": if this is a drug, the best rxcui code for it }. In the case that there is no reasonable price estimate known for any of these properties (such as a physician fee for a lab-based CPT code), simply return "n/a" and provide no description. Prices won't be completely accurate, but if you don't have a reasonable estimate based on some factual data - return n/a. Return the list as a JSON list. No commentary and no markdown - just a JSON list`
                }],
                model: "gpt-4o"
            })
                .catch(err => {
                    console.log(`Error searching ai procedures: ${err.message}`)
                })

            // console.log('result', result);
            list = result ? JSON.parse((result.choices || [])[0]?.message?.content?.replace('```json', '').replace('```', '')?.trim().replace(/[\n']/g, '') || "[]") : [];
        }

        const format = (val: any, key?:string) => {
            const obj: any = key ? { [key]: val[key] } : {
                code: val.code,
                source: 'openai',
                estimatedAt: new Date(),
                description: val.description,
                //TODO: set locationCode logic from params
                locationCode: val.code
            }
            if(key && val[key] === 'n/a' && obj[key]) delete obj[key]
            //allow for one key only
            for (const k in priceKeys.properties) {
                if (val[k]) {
                    if (typeof val[k] === 'number') obj[k] = val[k] * 100;
                    else if(val[k] !== 'n/a') {
                        if (typeof val === 'string') {
                            const spl = val[k].split('-');
                            if (spl.length > 1) {
                                if (spl.length === 2) {
                                    obj[`${k}_low`] = Number(spl[0].replace(/[^\d.]/g, '')) * 100;
                                    obj[`${k}_high`] = Number(spl[1].replace(/[^\d.]/g, '')) * 100;
                                }
                                let total = 0;
                                let count = 0;
                                for (let i = 0; i < spl.length; i++) {
                                    count++
                                    total += Number(spl[i].replace(/[^\d.]/g, ''))
                                }
                                obj[k] = Math.floor(total / count) * 100;
                            } else obj[k] = Number(val[k].replace(/[^\d.]/g, '')) * 100;
                        } else if(obj[k]) delete obj[k];
                    } else if(obj[k]) delete obj[k];
                }
            }
            return obj;
        }

        const formatted:any = [];
        const formattedCodes:any = [];
        const runPatch:any = {};
        const setRunPatch = (listItem:any, id:string) => {
            for(const k in priceKeys.properties) {
                /**patch if we found new data - set runPatch to include only properties with changes*/
                if(!(patchIds[listItem.code] || {})[k] && listItem[k] && listItem[k] !== 'n/a'){
                    rerun = true
                    /**format the patch-able result - the format function only formats the price fields, otherwise it will simply return a one-key object (k) with the val[k] value passed to it*/
                    runPatch[id] = { ...runPatch[id], ...format({[k]: listItem[k] }, k)}
                }
            }
        }
        for (let i = 0; i < list.length; i++) {
            if (list[i].code && list[i].code !== 'n/a') {
                //set list of new price estimates to create
                if (!patchIds[list[i].code]){
                    //account for multiple entries for the same code (must have unique locationCode
                    const idx = formattedCodes.indexOf(list[i].code);
                    if(idx > -1){
                        //add additional code as alt if existing
                        formatted[idx].alts = [format(list[i]), ...formatted[idx].alts || []].slice(0, 50);
                    } else {
                        formatted.push(format(list[i]));
                        formattedCodes.push(list[i].code)
                    }
                } else {
                   setRunPatch(list[i], patchIds[list[i].code]._id);
                }
            }
        }

        let added:any = []
        //create new estimates
        if(formatted.length) {
            const mixedList = Array.from(new Set([...formatted.map(a => getLocationCode(a.code)), ...locationCodes]))
            existing = await new CoreCall('price-estimates', context)._find({ skip_hooks: true, admin_pass: true, query: { $limit: mixedList.length, locationCode: { $in: mixedList }}})
                .catch(err => {
                    console.log(`Error re-getting existing price estimates: ${err.message}`)
                    return existing;
                })
            const exCodes = existing.data.map(a => a.locationCode);
            for(let i = 0; i < formatted.length; i++) {
                const idx = exCodes.indexOf(formatted[i].locationCode)
                if(idx === -1) {
                    const c = await new CoreCall('price-estimates', context).create(formatted[i])
                        .catch(err => {
                            console.log(`Could not create price estimate with code ${formatted[i].code} - ${err.message}`)
                            return formatted[i]
                        })
                    if (c) added.push(c);
                } else {
                   setRunPatch(formatted[i], existing.data[idx]._id);
                   data.push(existing.data[idx])
                }
            }
        }
        //if rerun, patch estimate data
        if (rerun) {
            for (const k in runPatch) {
                //build alt price entry
                const oldPrice = existing.data.filter(a => String(a._id) === k)[0];
                const altPrice:any = {};
                for(const key in estimateBodySchema.properties){
                    if(oldPrice[key] && oldPrice[key] !== 'n/a') altPrice[key] = oldPrice[key]
                }
                const alts = [altPrice, ...oldPrice.alts || []].slice(0, 50)
                // console.log('patching price estimate', runPatch[k], alts);
                const patched = await new CoreCall('price-estimates', context).patch(oldPrice._id, { ...runPatch[k], alts, estimatedAt: new Date() }, { admin_pass: true })
                    .catch(err => {
                        console.log(`Error patching price estimate: ${oldPrice._id} - ${err.message}`);
                        return oldPrice
                    })
                added.push(patched);
            }

        }

        context.result = { ...context.result, data: [...data.map((a:any) => JSON.parse(JSON.stringify(a))), ...added], total: added.length + data.length };
        return context;
    }
}
