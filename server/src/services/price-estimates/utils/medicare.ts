import puppeteer from 'puppeteer';
import {HookContext} from '../../../declarations.js';
import {CoreCall} from 'feathers-ucan';
import {_get} from '../../../utils/index.js';

export const medicareCheck = async (context: HookContext): Promise<HookContext> => {
    const {medicare_check} = context.params.runJoin || {};
    if (medicare_check) {

        const {code} = medicare_check;
        const url = `https://www.medicare.gov/procedure-price-lookup/cost/${code}/`
        const browser = await puppeteer.launch({headless: true})
        const page = await browser.newPage();
        const data: any = {hospital: {}, amb: {}};
        const delay = (time: number) => {
            return new Promise(function (resolve) {
                setTimeout(resolve, time)
            });
        }
        try {
            await page.goto(url, {waitUntil: 'domcontentloaded'});

            const buttonSelector = '.ds-l-row.expand-button';
            await page.waitForSelector(buttonSelector);

            await delay(1000);
            await page.click(buttonSelector);

// Wait for the elements indicating the content has loaded
            await page.waitForSelector('.Total.Cost');


// Retrieve all ".Total.Cost" elements
            const totals = await page.$$('.Total.Cost');
            let hTotal = 0;
            let aTotal = 0;

            for (let i = 0; i < totals.length; i++) {
                const el = totals[i];
                const amt = await el.$(':scope > :nth-child(2)');
                if (amt) {
                    const amtText = await amt.evaluate(node => node.textContent);
                    if (amtText) {
                        const num = Number(amtText.replace('$', '').replace(',', ''));
                        if (i === 0) aTotal = num;
                        else if (aTotal >= num) {
                            hTotal = aTotal;
                            aTotal = num;
                        } else hTotal = num;
                    }
                }
            }
            data.hospital.total = hTotal;
            data.amb.total = aTotal;

// Retrieve all ".totalCostBreakdown" elements
            const breakdown = await page.$$('.totalCostBreakdown');

            let hDoc = 0;
            let hFac = 0;
            let aDoc = 0;
            let aFac = 0;

            for (let i = 0; i < breakdown.length; i++) {
                const el = breakdown[i];

                // Get facility fee
                const fac = await el.$(':scope > :nth-child(2)');
                if (fac) {
                    const facFeeEl = await fac.$(':scope > :nth-child(2)');
                    if (facFeeEl) {
                        const facFeeText = await facFeeEl.evaluate(node => node.textContent);
                        if (facFeeText) {
                            const num = Number(facFeeText.replace('$', '').replace(',', ''));
                            if (i === 0) aFac = num;
                            else if (aFac >= num) {
                                hFac = aFac;
                                aFac = num;
                            } else hFac = num;
                        }
                    }
                }

                // Get doctor fee
                const doc = await el.$(':scope > :first-child');
                if (doc) {
                    const docFeeEl = await doc.$(':scope > :nth-child(2)');
                    if (docFeeEl) {
                        const docFeeText = await docFeeEl.evaluate(node => node.textContent);
                        if (docFeeText) {
                            const num = Number(docFeeText.replace('$', '').replace(',', ''));
                            if (i === 0) aDoc = num;
                            else if (aDoc >= num) {
                                hDoc = aDoc;
                                aDoc = num;
                            } else hDoc = num;
                        }
                    }
                }
            }
            data.hospital.doctor = hDoc;
            data.hospital.facility = hFac;
            data.amb.doctor = aDoc;
            data.amb.facility = aFac;
            if (aDoc && aFac && !data.amt.total) data.amt.total = aDoc + aFac;
            if (hDoc && hFac && !data.hospital.total) data.hospital.total = hDoc + hFac;

        } catch (err: any) {
            console.log(`Error scraping page: ${err.message}`)
        } finally {
            await browser.close();
        }
        console.log('return medicare', data);

        //add price-estimate data
        if (data.hospital.total || data.amb.total) {
            const map = {
                medicare_low: (v) => Number(_get(v, 'amb.total') || 0) * 100,
                medicare_high: (v) => Number(_get(v, 'hospital.total') || 0) * 100,
                medicare: (v) => Number(_get(v, 'hospital.total') || 0) * 100,
                cash_low: (v) => Number(_get(v, 'amb.total', 0)) * 2 * 100,
                cash_high: (v) => Number(_get(v, 'hospital.total', 0)) * 2.5 * 100,
                cash: (v) => Number(_get(v, 'hospital.total', 0)) * 2 * 100,
                source: () => 'medicare_lookup',
                description: () => 'derived from medicare price files',
            }
            const ex = await new CoreCall('price-estimates', context).find({query: {$limit: 1, locationCode: code}})
                .catch(err => console.log(`Couldn't get price-estimate for code ${code} in medicare lookup: ${err.message}`))
            let obj: any = {estimatedAt: new Date(), code, locationCode: code};

            if (ex.total) {
                const alt:any = {}
                for (const k in map) {
                    const val = map[k](data);
                    if(val && val !== 'n/a') {
                        if (!ex.data[0][k]) obj[k] = val
                        alt[k] = val;
                    }
                }
                context.result = await new CoreCall('price-estimates', context).patch(ex.data[0]._id, { ...obj, $addToSet: { alts: alt } })
            } else {
                for (const k in map) {
                    if (!obj[k]) {
                        obj[k] = map[k](data)
                    }
                    context.result = await new CoreCall('price-estimates', context).create(obj)
                }
            }
        } else context.result = data;
    }
    return context;

}
