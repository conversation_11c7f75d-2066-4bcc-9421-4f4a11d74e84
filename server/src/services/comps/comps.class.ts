// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Comps, CompsData, CompsPatch, CompsQuery } from './comps.schema.js'

export type { Comps, CompsData, CompsPatch, CompsQuery }

export interface CompsParams extends MongoDBAdapterParams<CompsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class CompsService<ServiceParams extends Params = CompsParams> extends MongoDBService<
  Comps,
  CompsData,
  CompsParams,
  CompsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient')
        .then((db) => db.collection('comps'))
        .then((collection) => {
          collection.createIndex({key: 1}, {unique: true});
          return collection
        }),
    operators: ['$regex', '$options']
  }
}
