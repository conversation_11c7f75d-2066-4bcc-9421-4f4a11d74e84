// For more information about this file see https://dove.feathersjs.com/guides/cli/application.html
import { feathers } from '@feathersjs/feathers'
import { _get } from './utils/index.js';
import v8 from 'node:v8'
import { monitorEventLoopDelay } from 'node:perf_hooks';

import express, {
    rest,
    json,
    urlencoded,
    cors,
    serveStatic,
    notFound,
    errorHandler
} from '@feathersjs/express'
import configuration from '@feathersjs/configuration'
import socketio from '@feathersjs/socketio'
import { startWatchdog, sampleAllocations } from './utils/watchdog/index.js';
import { requestTimeout, rateLimit, memoryGuard } from './utils/request-safety.js';
import { startProcessMonitoring } from './utils/resource-monitor.js';
import { configureRedisAdapter } from './utils/socketio-redis.js';
import { startSocketIOMonitoring } from './utils/socketio-monitor.js';

import type { Application } from './declarations.js'
import { configurationValidator } from './configuration.js'
import { logger } from './logger.js'
import { mongodb } from './mongodb.js'
import { services } from './services/index.js'
import { channels } from './channels.js';
import authentication from './authentication/index.js';
import appHooks from './app.hooks.js';

const app: Application = express(feathers())

// LIVENESS: quick "is the process alive?"
app.use('/healthz', (_req, res) => res.status(200).send('ok'));

// READINESS: only "ready" when healthy AND dependencies are reachable
app.use('/readyz', async (_req, res) => {
    // Add timeout to prevent health check from hanging
    const healthCheckTimeout = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Health check timeout')), 5000);
    });

    try {
        // Check critical deps (DB) with timeout protection
        const dbCheck = async () => {
            const mongoClient = app.get('mongoClient');
            if (mongoClient) {
                await mongoClient.admin().ping();
            } else {
                throw new Error('MongoDB client not available');
            }
        };

        await Promise.race([dbCheck(), healthCheckTimeout]);

        // Check Redis status but don't fail readiness if Redis is down
        // Redis is for clustering/scaling, not core functionality
        const redisHealthy = app.get('redisHealthy');
        const redisClients = app.get('redisClients');

        if (redisClients && !redisHealthy) {
            console.warn('[Health Check] Redis is unhealthy but continuing - app can run in single-server mode');
        }

        return res.status(200).send('ready');

    } catch (e:any) {
        console.error('[Health Check] Failed:', e.message);

        // For temporary DB issues, still return ready to prevent unnecessary restarts
        // Only fail if the app is fundamentally broken
        if (e.message === 'Health check timeout') {
            console.error('[Health Check] Timeout - this may indicate system overload');
            return res.status(503).send('timeout');
        }

        // For MongoDB connection issues, log but don't immediately fail
        // This prevents restarts due to temporary network issues
        console.warn('[Health Check] DB issue detected but returning ready to prevent restart');
        return res.status(200).send('ready-db-warning');
    }
});

// Socket.IO debug endpoint
app.use('/socket-debug', (_req, res) => {
    const redisClients = app.get('redisClients');
    const redisHealthy = app.get('redisHealthy');

    let redisStatus = 'single-server';
    if (redisClients) {
        redisStatus = redisHealthy ? 'connected' : 'unhealthy';
    }

    res.json({
        socketio: 'configured',
        timestamp: new Date().toISOString(),
        redis: redisStatus,
        redisHealthy: redisHealthy ?? null,
        message: 'Socket.IO server is running',
        port: app.get('port'),
        host: app.get('host'),
        env: process.env.NODE_ENV
    });
});

// Diagnostic endpoint for monitoring system health
app.use('/diagnostics', (_req, res) => {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const uptime = process.uptime();

    res.json({
        timestamp: new Date().toISOString(),
        uptime: Math.round(uptime) + 's',
        memory: {
            rss: Math.round(memUsage.rss / 1024 / 1024) + 'MB',
            heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
            heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB',
            external: Math.round(memUsage.external / 1024 / 1024) + 'MB'
        },
        cpu: {
            user: cpuUsage.user,
            system: cpuUsage.system
        },
        redis: {
            configured: !!app.get('redisClients'),
            healthy: app.get('redisHealthy') ?? null
        },
        watchdog: {
            readinessBad: 'check /healthz endpoint'
        },
        environment: {
            nodeEnv: process.env.NODE_ENV,
            port: process.env.PORT,
            hostname: process.env.HOSTNAME
        }
    });
});

// Test endpoint to check if we can reach the server at all
app.use('/test-connection', (_req, res) => {
    res.json({
        message: 'Server is reachable',
        timestamp: new Date().toISOString(),
        userAgent: _req.headers['user-agent'],
        ip: _req.ip || _req.connection?.remoteAddress,
        headers: _req.headers
    });
});



// Load app configuration
app.configure(configuration(configurationValidator))

// Apply safety middleware early
app.use(requestTimeout(45000)) // 45 second timeout
app.use(rateLimit)
app.use(memoryGuard(1500)) // Reject requests when heap > 1.5GB (more reasonable for production)

app.use(cors())
app.use(json({
    verify: (req:any, res, buf) => {
        const rawEndpoints: string[] = ["/banking"];

        if (req.url && rawEndpoints.includes(req.url)) {
            req.rawBody = buf;
        }
    }
}))
app.use(urlencoded({ extended: true }))
// Host the public folder
app.use('/', serveStatic(app.get('public')))

// Configure services and real-time functionality
app.configure(rest())

app.configure(
    socketio({
        cors: {
            origin: true, // Allow all origins for now - can be restricted later
            credentials: true
        },
        // Connection limits and timeouts
        pingTimeout: 60000,
        pingInterval: 25000,
        upgradeTimeout: 10000,
        maxHttpBufferSize: 1e6, // 1MB
        // Prevent too many connections from same IP
        connectionStateRecovery: {
            maxDisconnectionDuration: 2 * 60 * 1000, // 2 minutes
            skipMiddlewares: true,
        }
    }, async (io) => {
        // Configure Redis adapter for clustering (with error handling)
        // This should never cause the application to fail - always fall back to single-server mode
        try {
            await configureRedisAdapter(app, io);
            console.log('[Socket.IO] Redis adapter configuration completed');
        } catch (error) {
            console.error('[Socket.IO] Redis adapter configuration failed:', error);
            console.log('[Socket.IO] Continuing with single-server mode - this is normal for single-instance deployments');

            // Ensure Redis health tracking is properly initialized even on failure
            app.set('redisHealthy', false);
            app.set('redisClients', null);
        }

        // Connection rate limiting
        const connectionCounts = new Map<string, number>();
        const MAX_CONNECTIONS_PER_IP = 10;

        // Add connection logging for debugging
        io.on('connection', (socket) => {
            console.log('[Socket.IO] New connection:', socket.id);
        });

        io.use(function (socket, next) {
            const ip = _get(socket, 'handshake.headers.x-forwarded-for', _get(socket, ['request', 'connection', 'remoteAddress'])) || 'unknown';

            console.log('[Socket.IO] Connection attempt from IP:', ip);

            // Rate limit connections per IP
            const currentCount = connectionCounts.get(ip) || 0;
            if (currentCount >= MAX_CONNECTIONS_PER_IP) {
                console.log('[Socket.IO] Rate limit exceeded for IP:', ip);
                return next(new Error('Too many connections from this IP'));
            }

            connectionCounts.set(ip, currentCount + 1);

            // Store IP info
            (socket as { [key: string]: any }).feathers.ip = ip;
            (socket as { [key: string]: any }).feathers.headers.ip = ip;

            // Clean up connection count on disconnect
            socket.on('disconnect', () => {
                const count = connectionCounts.get(ip) || 0;
                if (count <= 1) {
                    connectionCounts.delete(ip);
                } else {
                    connectionCounts.set(ip, count - 1);
                }
                console.log('[Socket.IO] Disconnected:', socket.id);
            });

            next();
        });

        // Start Socket.IO monitoring
        startSocketIOMonitoring(app);
    })
);
app.configure(mongodb)
app.configure(services)


app.configure(authentication)
app.configure(channels)

// Configure a middleware for 404s and the error handler
app.use(notFound())
app.use(errorHandler({ logger }))

// Register hooks that run on all service methods
app.hooks({
        ...{
            around: {
                all: [],
            }
        },
        ...appHooks
    }
);
// Register application setup and teardown hooks here
app.hooks({
    setup: [],
    teardown: [],
});

const hist = monitorEventLoopDelay({ resolution: 20 }); hist.enable();

// Start process monitoring with app instance for Redis health checks
const stopProcessMonitoring = startProcessMonitoring(app);

// Start the watchdog AFTER your deps are initialized
startWatchdog({
    // override thresholds via env if you like
    onLeakSuspect: async (info:any, apP:any) => {
        console.warn('[leak suspect]', info)
        try {
            const mu = process.memoryUsage();
            const hs = v8.getHeapStatistics();
            const spaces = v8.getHeapSpaceStatistics();
            const eldP95 = hist.percentile(95) / 1e6; // ms
            await apP.service('errs').create({
                path: 'watchdog',
                method: 'leak-suspect',
                type: 'manual',
                error: {message: 'leak suspect'},
                data: {
                    info,
                    mem: {
                        rssMB: mu.rss / 1048576, heapUsedMB: mu.heapUsed / 1048576,
                        externalMB: mu.external / 1048576
                    },
                    heap: {
                        usedMB: hs.used_heap_size / 1048576,
                        totalMB: hs.total_heap_size / 1048576,
                        limitMB: hs.heap_size_limit / 1048576,
                        spaces: spaces.map(s => ({space: s.space_name, usedMB: s.space_used_size / 1048576}))
                    },
                    eventLoopDelayP95ms: eldP95
                },
                result: {},
                createdAt: new Date()
            })
        } catch (e) {
            console.error('watchdog logging failed on suspect leak', e);
        }
    },
    onHardTrip: async (info:any, apP:any) => {
        console.warn('[hard leak]', info)
        try {
            const profile = await sampleAllocations(10000); // 10s sample window
            // Store a compact summary
            const top = profile.samples
                .slice(0, 1000) // limit, just in case
                .reduce((m, s) => (m[s.nodeId] = (m[s.nodeId]||0) + s.size, m), {});
            console.error('[alloc-top]', Object.entries(top).slice(0,10));
            await apP.service('errs').create({
                path: 'watchdog',
                method: 'hard-trip',
                type: 'manual',
                error: {message: 'hard trip'},
                data: {
                    info,
                    top,
                    profile
                },
                result: {},
                createdAt: new Date()
            })
            // Or save whole `profile` JSON to Mongo/GridFS/S3 if you want
        } catch (e) {
            console.error('sampling-profiler failed on hard trip', e);
        }
    },
}, app);

// // OPTIONAL: admin-only manual heapdump trigger
// app.post('/ops/heapdump', async (req, res) => {
//     if (!process.env.OPS_TOKEN || req.get('x-ops-token') !== process.env.OPS_TOKEN) {
//         return res.status(401).send('unauthorized');
//     }
//     const file = await triggerHeapdump('manual');
//     res.status(200).json({ file });
// });
console.log('[APP] Application setup complete, exporting app');

export {app}
