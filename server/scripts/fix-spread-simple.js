#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function fixService(serviceName) {
  const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  
  if (!fs.existsSync(schemaPath)) {
    return false
  }
  
  try {
    let content = fs.readFileSync(schemaPath, 'utf8')
    const camelName = toCamelCase(serviceName)
    
    // Skip if already using spread pattern
    if (content.includes('...Type.Omit(')) {
      console.log(`✅ ${serviceName}: Already using spread pattern`)
      return false
    }
    
    // 1. Fix dataSchema - simple replacement
    const dataSchemaStart = content.indexOf(`export const ${camelName}DataSchema = Type.Object({`)
    if (dataSchemaStart !== -1) {
      const dataSchemaEnd = content.indexOf('}, { additionalProperties: false })', dataSchemaStart) + 35
      
      if (dataSchemaEnd > dataSchemaStart) {
        const newDataSchema = `export const ${camelName}DataSchema = Type.Object({
  ...Type.Omit(${camelName}Schema, ['_id']).properties
}, { additionalProperties: false })`
        
        content = content.substring(0, dataSchemaStart) + newDataSchema + content.substring(dataSchemaEnd)
      }
    }
    
    // 2. Fix patchSchema - extract MongoDB operators first
    const patchSchemaStart = content.indexOf(`export const ${camelName}PatchSchema = Type.Object({`)
    if (patchSchemaStart !== -1) {
      const patchSchemaEnd = content.indexOf('}, { additionalProperties: false })', patchSchemaStart) + 35
      
      if (patchSchemaEnd > patchSchemaStart) {
        const patchContent = content.substring(patchSchemaStart, patchSchemaEnd)
        
        // Extract MongoDB operators (lines containing $)
        const mongoOperators = []
        const lines = patchContent.split('\n')
        
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim()
          if (line.startsWith('$')) {
            // This is a MongoDB operator - extract it
            if (line.includes('Type.Object({')) {
              // Multi-line operator - find the end
              let operatorLines = [line]
              for (let j = i + 1; j < lines.length; j++) {
                operatorLines.push(lines[j])
                if (lines[j].includes('})),') || lines[j].includes('}, { additionalProperties: false }))')) {
                  i = j // Skip these lines in main loop
                  break
                }
              }
              mongoOperators.push('  ' + operatorLines.join('\n  '))
            } else {
              // Single-line operator
              mongoOperators.push('  ' + line)
            }
          }
        }
        
        let newPatchSchema = `export const ${camelName}PatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(${camelName}Schema, ['_id'])).properties`
        
        if (mongoOperators.length > 0) {
          newPatchSchema += ',\n' + mongoOperators.join(',\n')
        }
        
        newPatchSchema += '\n}, { additionalProperties: false })'
        
        content = content.substring(0, patchSchemaStart) + newPatchSchema + content.substring(patchSchemaEnd)
      }
    }
    
    fs.writeFileSync(schemaPath, content)
    console.log(`✅ Fixed ${serviceName}`)
    return true
    
  } catch (error) {
    console.log(`❌ Error fixing ${serviceName}: ${error.message}`)
    return false
  }
}

// Get all services
const servicesDir = path.join(serverDir, 'src', 'services')
const services = fs.readdirSync(servicesDir)
  .filter(dir => {
    const schemaPath = path.join(servicesDir, dir, `${dir}.schema.ts`)
    return fs.existsSync(schemaPath)
  })

console.log(`🚀 Fixing spread pattern for ${services.length} services...\n`)

let successCount = 0
services.forEach(serviceName => {
  if (fixService(serviceName)) {
    successCount++
  }
})

console.log(`\n📊 Fixed: ${successCount}/${services.length} services`)
console.log('🎯 Pattern: Only omit _id, spread everything else!')
