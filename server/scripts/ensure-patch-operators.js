#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

// Services that should have MongoDB operators
const requiredOperators = {
  "ai-chats": ["$push"],
  "bill-erasers": ["$push", "$addToSet", "$pull"],
  "bundles": ["$addToSet", "$pull"],
  "cams": ["$inc"],
  "caps": ["$addToSet", "$pull"],
  "cares": ["$addToSet", "$pull"],
  "cats": ["$addToSet", "$pull"],
  "claim-payments": ["$addToSet", "$pull"],
  "claims": ["$addToSet", "$pull"],
  "coverages": ["$addToSet"],
  "cross-sections": ["$addToSet"],
  "drops": ["$addToSet", "$pull"],
  "enrollments": ["$addToSet"],
  "expenses": ["$inc"],
  "fbs": ["$addToSet", "$pull"],
  "gps": ["$addToSet"],
  "groups": ["$addToSet"],
  "hosts": ["$addToSet", "$pull"],
  "households": ["$addToSet"],
  "ims": ["$pull", "$addToSet"],
  "issues": ["$addToSet"],
  "logins": ["$push"],
  "meds": ["$addToSet", "$pull"],
  "networks": ["$addToSet", "$pull"],
  "plans": ["$addToSet", "$pull", "$inc"],
  "ppls": ["$addToSet", "$pull", "$push"],
  "price-estimates": ["$addToSet"],
  "prices": ["$addToSet", "$push", "$pull"],
  "procedures": ["$addToSet"],
  "providers": ["$addToSet", "$pull"],
  "se-plans": ["$addToSet"],
  "shops": ["$pull"],
  "teams": ["$addToSet", "$pull"],
  "uploads": ["$addToSet"],
  "visits": ["$addToSet", "$pull"]
}

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function ensurePatchOperators(serviceName) {
  const operators = requiredOperators[serviceName]
  if (!operators) {
    return false // Service doesn't need operators
  }
  
  const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  if (!fs.existsSync(schemaPath)) {
    console.log(`❌ ${serviceName}: Schema file not found`)
    return false
  }
  
  try {
    let content = fs.readFileSync(schemaPath, 'utf8')
    const camelName = toCamelCase(serviceName)
    
    // Check which operators are missing
    const missingOperators = operators.filter(op => !content.includes(`${op}:`))
    
    if (missingOperators.length === 0) {
      console.log(`✅ ${serviceName}: All required operators present`)
      return false
    }
    
    console.log(`🔧 ${serviceName}: Adding missing operators: ${missingOperators.join(', ')}`)
    
    // Add imports if needed
    const needsAddToSet = operators.some(op => ['$push', '$addToSet'].includes(op))
    const needsPull = operators.includes('$pull')
    
    if ((needsAddToSet || needsPull) && !content.includes('addToSet, pull')) {
      const importMatch = content.match(/import { ([^}]+) } from '\.\.\/\.\.\/utils\/common\/typebox-schemas\.js'/)
      if (importMatch) {
        const currentImports = importMatch[1]
        let newImports = currentImports
        
        if (needsAddToSet && !currentImports.includes('addToSet')) {
          newImports += ', addToSet'
        }
        if (needsPull && !currentImports.includes('pull')) {
          newImports += ', pull'
        }
        
        content = content.replace(importMatch[0], `import { ${newImports} } from '../../utils/common/typebox-schemas.js'`)
      }
    }
    
    // Find the patch schema and add missing operators
    const patchSchemaPattern = new RegExp(`(export const ${camelName}PatchSchema = Type\\.Object\\({[\\s\\S]*?)(}\\s*,\\s*{[\\s\\S]*?}\\))`)
    const patchMatch = content.match(patchSchemaPattern)
    
    if (patchMatch) {
      const beforeClosing = patchMatch[1]
      const afterClosing = patchMatch[2]
      
      // Build missing operators
      const missingOpsStr = missingOperators.map(op => {
        if (op === '$inc') {
          return `  ${op}: Type.Optional(Type.Record(Type.String(), Type.Number())),`
        } else if (op === '$push' || op === '$addToSet') {
          return `  ${op}: Type.Optional(addToSet([])),`
        } else if (op === '$pull') {
          return `  ${op}: Type.Optional(pull([])),`
        }
        return `  ${op}: Type.Optional(Type.Record(Type.String(), Type.Any())),`
      }).join('\n')
      
      // Insert missing operators before the closing brace
      const newPatchContent = beforeClosing + ',\n  // Missing MongoDB operators\n' + missingOpsStr + '\n' + afterClosing
      
      content = content.replace(patchMatch[0], newPatchContent)
      
      fs.writeFileSync(schemaPath, content)
      console.log(`✅ ${serviceName}: Added ${missingOperators.length} missing operators`)
      return true
    } else {
      console.log(`❌ ${serviceName}: Could not find patch schema pattern`)
      return false
    }
    
  } catch (error) {
    console.log(`❌ ${serviceName}: Error adding operators - ${error.message}`)
    return false
  }
}

// Process all services that should have operators
console.log(`🔍 Ensuring MongoDB operators for ${Object.keys(requiredOperators).length} services...\n`)

let fixedCount = 0
Object.keys(requiredOperators).forEach(serviceName => {
  if (ensurePatchOperators(serviceName)) {
    fixedCount++
  }
})

console.log(`\n📊 Results:`)
console.log(`✅ Added missing operators to: ${fixedCount} services`)
console.log(`🎯 All patch schemas now have required MongoDB operators!`)

if (fixedCount > 0) {
  console.log(`\n🔍 Run compilation test to verify the fixes`)
}
