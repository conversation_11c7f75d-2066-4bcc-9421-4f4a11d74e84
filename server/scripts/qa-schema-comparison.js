#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function extractJsonSchemaKeys(content, schemaName) {
  // Extract keys from JSON schema properties
  const keys = new Set()
  
  // Look for the main schema definition
  const schemaMatch = content.match(new RegExp(`export const ${schemaName} = {[\\s\\S]*?properties:\\s*{([\\s\\S]*?)}[\\s\\S]*?}`))
  if (schemaMatch) {
    const propertiesContent = schemaMatch[1]
    
    // Extract property names
    const propertyMatches = propertiesContent.match(/^\s*(\w+):/gm) || []
    propertyMatches.forEach(match => {
      const key = match.trim().replace(':', '')
      if (key && !key.startsWith('//')) {
        keys.add(key)
      }
    })
  }
  
  return keys
}

function extractTypeBoxSchemaKeys(content, schemaName) {
  // Extract keys from TypeBox schema
  const keys = new Set()
  
  // Look for the main schema definition
  const schemaMatch = content.match(new RegExp(`export const ${schemaName} = Type\\.Object\\({([\\s\\S]*?)}\\s*,\\s*{[\\s\\S]*?}\\)`))
  if (schemaMatch) {
    const propertiesContent = schemaMatch[1]
    
    // Extract property names (excluding spread operators)
    const propertyMatches = propertiesContent.match(/^\s*(\w+):/gm) || []
    propertyMatches.forEach(match => {
      const key = match.trim().replace(':', '')
      if (key && !key.startsWith('//') && !key.startsWith('...')) {
        keys.add(key)
      }
    })
    
    // Also extract keys from commonFields if used
    if (propertiesContent.includes('...commonFields')) {
      keys.add('_id')
      keys.add('createdAt')
      keys.add('updatedAt')
      keys.add('createdBy')
      keys.add('updatedBy')
      keys.add('env')
      keys.add('host')
      keys.add('ref')
      keys.add('changeLog')
    }
  }
  
  return keys
}

function compareSchemas(serviceName) {
  const camelName = toCamelCase(serviceName)
  
  try {
    // Get old schema from redis-on branch
    const oldSchemaContent = execSync(`git show redis-on:src/services/${serviceName}/${serviceName}.schema.ts`, { encoding: 'utf8' })
    
    // Get new schema from current branch
    const newSchemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
    if (!fs.existsSync(newSchemaPath)) {
      console.log(`❌ ${serviceName}: New schema file not found`)
      return false
    }
    
    const newSchemaContent = fs.readFileSync(newSchemaPath, 'utf8')
    
    // Extract keys from both schemas
    const oldKeys = extractJsonSchemaKeys(oldSchemaContent, camelName)
    const newKeys = extractTypeBoxSchemaKeys(newSchemaContent, camelName)
    
    // Compare keys
    const missingKeys = [...oldKeys].filter(key => !newKeys.has(key))
    const extraKeys = [...newKeys].filter(key => !oldKeys.has(key))
    
    if (missingKeys.length === 0 && extraKeys.length === 0) {
      console.log(`✅ ${serviceName}: All keys match perfectly`)
      return true
    } else {
      console.log(`⚠️  ${serviceName}: Key differences found`)
      if (missingKeys.length > 0) {
        console.log(`   Missing keys: ${missingKeys.join(', ')}`)
      }
      if (extraKeys.length > 0) {
        console.log(`   Extra keys: ${extraKeys.join(', ')}`)
      }
      return false
    }
    
  } catch (error) {
    console.log(`❌ ${serviceName}: Error comparing schemas - ${error.message}`)
    return false
  }
}

// Get all services that have schema files
const servicesDir = path.join(serverDir, 'src', 'services')
const services = fs.readdirSync(servicesDir)
  .filter(dir => {
    const schemaPath = path.join(servicesDir, dir, `${dir}.schema.ts`)
    return fs.existsSync(schemaPath)
  })

console.log(`🔍 QA: Comparing old vs new schemas for ${services.length} services...\n`)

let perfectCount = 0
let issueCount = 0

services.forEach(serviceName => {
  if (compareSchemas(serviceName)) {
    perfectCount++
  } else {
    issueCount++
  }
})

console.log(`\n📊 QA Results:`)
console.log(`✅ Perfect matches: ${perfectCount}/${services.length}`)
console.log(`⚠️  Issues found: ${issueCount}/${services.length}`)

if (issueCount > 0) {
  console.log(`\n🔧 Next steps: Fix missing/renamed keys in services with issues`)
} else {
  console.log(`\n🎉 All schemas have perfect key matching!`)
}
