#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function extractSchemaProperties(content, serviceName) {
  const camelName = toCamelCase(serviceName)
  const properties = {}

  try {
    // Look for the main schema definition: export const serviceName = { ... properties: { ... } }
    const schemaPattern = new RegExp(`export const ${camelName}Schema = {[\\s\\S]*?properties:\\s*{([\\s\\S]*?)}[\\s\\S]*?}`)
    const schemaMatch = content.match(schemaPattern)

    if (schemaMatch) {
      const propertiesContent = schemaMatch[1]

      // Simple property extraction - look for property: value patterns
      const lines = propertiesContent.split('\n')

      for (const line of lines) {
        const trimmed = line.trim()

        // Skip comments, empty lines, and spread operators
        if (trimmed.startsWith('//') || trimmed === '' || trimmed.startsWith('...')) continue

        // Look for simple property definitions
        const simpleMatch = trimmed.match(/^(\w+):\s*{type:\s*'(\w+)'}/)
        if (simpleMatch) {
          properties[simpleMatch[1]] = simpleMatch[2]
          continue
        }

        // Look for ObjectIdSchema properties
        const objectIdMatch = trimmed.match(/^(\w+):\s*ObjectIdSchema\(\)/)
        if (objectIdMatch) {
          properties[objectIdMatch[1]] = 'ObjectId'
          continue
        }

        // Look for complex property definitions (just capture the key)
        const complexMatch = trimmed.match(/^(\w+):\s*{/)
        if (complexMatch) {
          properties[complexMatch[1]] = 'object'
          continue
        }

        // Look for array properties
        const arrayMatch = trimmed.match(/^(\w+):\s*{type:\s*'array'/)
        if (arrayMatch) {
          properties[arrayMatch[1]] = 'array'
          continue
        }

        // Fallback for any other property pattern
        const fallbackMatch = trimmed.match(/^(\w+):/)
        if (fallbackMatch && !fallbackMatch[1].startsWith('$')) {
          properties[fallbackMatch[1]] = 'any'
        }
      }
    }

    return properties

  } catch (error) {
    console.log(`❌ Error extracting ${serviceName}: ${error.message}`)
    return {}
  }
}

async function extractAllSchemas() {
  const allSchemas = {}
  
  try {
    // Get list of services from current branch (they should be the same)
    const servicesDir = path.join(serverDir, 'src', 'services')
    const services = fs.readdirSync(servicesDir)
      .filter(dir => {
        const schemaPath = path.join(servicesDir, dir, `${dir}.schema.ts`)
        return fs.existsSync(schemaPath)
      })

    console.log(`🔍 Extracting properties from ${services.length} services in redis-on branch...\n`)

    for (const serviceName of services) {
      try {
        // Get the old schema content from redis-on branch
        const oldSchemaContent = execSync(`git show redis-on:server/src/services/${serviceName}/${serviceName}.schema.ts`, { encoding: 'utf8' })
        
        // Extract properties
        const properties = extractSchemaProperties(oldSchemaContent, serviceName)
        
        if (Object.keys(properties).length > 0) {
          allSchemas[serviceName] = properties
          console.log(`✅ ${serviceName}: ${Object.keys(properties).length} properties extracted`)
        } else {
          console.log(`⚠️  ${serviceName}: No properties found`)
        }
        
      } catch (error) {
        console.log(`❌ ${serviceName}: ${error.message}`)
      }
    }
    
    // Save the extracted schemas to a JSON file
    const outputPath = path.join(serverDir, 'scripts', 'old-schema-properties.json')
    fs.writeFileSync(outputPath, JSON.stringify(allSchemas, null, 2))
    
    console.log(`\n📄 Saved all schema properties to: ${outputPath}`)
    console.log(`📊 Total services extracted: ${Object.keys(allSchemas).length}`)
    
    return allSchemas
    
  } catch (error) {
    console.log(`❌ Error extracting schemas: ${error.message}`)
    return {}
  }
}

// Run the extraction
extractAllSchemas().then(schemas => {
  console.log('\n🎯 Ready for comparison! Use the generated JSON file to compare against TypeBox schemas.')
})
