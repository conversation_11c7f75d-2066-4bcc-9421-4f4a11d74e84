#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function extractTypeBoxSchemaKeys(content, serviceName) {
  const camelName = toCamelCase(serviceName)
  const keys = new Set()
  
  try {
    // Look for the main schema definition: export const serviceSchema = Type.Object({
    const schemaPattern = new RegExp(`export const ${camelName}Schema = Type\\.Object\\({([\\s\\S]*?)}\\s*,\\s*{[\\s\\S]*?}\\)`)
    const schemaMatch = content.match(schemaPattern)
    
    if (schemaMatch) {
      const propertiesContent = schemaMatch[1]
      
      // Extract property names (excluding spread operators)
      const propertyMatches = propertiesContent.match(/^\s*(\w+):/gm) || []
      propertyMatches.forEach(match => {
        const key = match.trim().replace(':', '')
        if (key && !key.startsWith('//') && !key.startsWith('...')) {
          keys.add(key)
        }
      })
      
      // Add commonFields if used
      if (propertiesContent.includes('...commonFields')) {
        keys.add('_id')
        keys.add('createdAt')
        keys.add('updatedAt')
        keys.add('createdBy')
        keys.add('updatedBy')
        keys.add('env')
        keys.add('host')
        keys.add('ref')
        keys.add('changeLog')
      }
    }
    
    return keys
    
  } catch (error) {
    console.log(`❌ Error extracting TypeBox keys for ${serviceName}: ${error.message}`)
    return new Set()
  }
}

function compareSchemas() {
  // Load the old schema properties
  const oldSchemasPath = path.join(serverDir, 'scripts', 'old-schema-properties.json')
  if (!fs.existsSync(oldSchemasPath)) {
    console.log('❌ old-schema-properties.json not found. Run extract-old-schema-properties.js first.')
    return
  }
  
  const oldSchemas = JSON.parse(fs.readFileSync(oldSchemasPath, 'utf8'))
  
  console.log(`🔍 QA: Comparing ${Object.keys(oldSchemas).length} schemas...\n`)
  
  let perfectCount = 0
  let issueCount = 0
  const issues = []
  
  for (const [serviceName, oldProperties] of Object.entries(oldSchemas)) {
    const camelName = toCamelCase(serviceName)
    
    // Get current TypeBox schema
    const newSchemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
    if (!fs.existsSync(newSchemaPath)) {
      console.log(`❌ ${serviceName}: TypeBox schema file not found`)
      issueCount++
      continue
    }
    
    const newSchemaContent = fs.readFileSync(newSchemaPath, 'utf8')
    const newKeys = extractTypeBoxSchemaKeys(newSchemaContent, serviceName)
    
    // Compare keys
    const oldKeySet = new Set(Object.keys(oldProperties))
    const missingKeys = [...oldKeySet].filter(key => !newKeys.has(key))
    const extraKeys = [...newKeys].filter(key => !oldKeySet.has(key))
    
    if (missingKeys.length === 0 && extraKeys.length === 0) {
      console.log(`✅ ${serviceName}: Perfect match (${oldKeySet.size} properties)`)
      perfectCount++
    } else {
      console.log(`⚠️  ${serviceName}: Key differences found`)
      if (missingKeys.length > 0) {
        console.log(`   🔴 Missing keys: ${missingKeys.join(', ')}`)
      }
      if (extraKeys.length > 0) {
        console.log(`   🟡 Extra keys: ${extraKeys.join(', ')}`)
      }
      
      issues.push({
        service: serviceName,
        missing: missingKeys,
        extra: extraKeys,
        oldCount: oldKeySet.size,
        newCount: newKeys.size
      })
      
      issueCount++
    }
  }
  
  console.log(`\n📊 QA Results:`)
  console.log(`✅ Perfect matches: ${perfectCount}/${Object.keys(oldSchemas).length}`)
  console.log(`⚠️  Issues found: ${issueCount}/${Object.keys(oldSchemas).length}`)
  
  if (issues.length > 0) {
    console.log(`\n🔧 Services needing fixes:`)
    issues.forEach(issue => {
      console.log(`\n${issue.service}:`)
      if (issue.missing.length > 0) {
        console.log(`  Missing: ${issue.missing.join(', ')}`)
      }
      if (issue.extra.length > 0) {
        console.log(`  Extra: ${issue.extra.join(', ')}`)
      }
    })
    
    // Save detailed issues to file
    const issuesPath = path.join(serverDir, 'scripts', 'schema-issues.json')
    fs.writeFileSync(issuesPath, JSON.stringify(issues, null, 2))
    console.log(`\n📄 Detailed issues saved to: ${issuesPath}`)
  } else {
    console.log(`\n🎉 All schemas have perfect key matching!`)
  }
}

// Run the comparison
compareSchemas()
