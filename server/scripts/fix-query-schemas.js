#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function fixQuerySchema(serviceName) {
  const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  
  if (!fs.existsSync(schemaPath)) {
    return false
  }
  
  try {
    let content = fs.readFileSync(schemaPath, 'utf8')
    const camelName = toCamelCase(serviceName)
    
    // Look for restrictive Type.Pick query patterns
    const pickPattern = new RegExp(
      `const ${camelName}QueryProperties = Type\\.Pick\\(${camelName}Schema, \\[[\\s\\S]*?\\], { additionalProperties: false }\\)`,
      'g'
    )
    
    if (pickPattern.test(content)) {
      // Replace with unrestricted query schema
      content = content.replace(
        pickPattern,
        `// Allow querying on any field from the main schema
const ${camelName}QueryProperties = ${camelName}Schema`
      )
      
      fs.writeFileSync(schemaPath, content)
      console.log(`✅ Fixed ${serviceName} query schema - removed field restrictions`)
      return true
    } else {
      console.log(`⚠️  ${serviceName}: No restrictive query pattern found`)
      return false
    }
    
  } catch (error) {
    console.log(`❌ Error fixing ${serviceName}: ${error.message}`)
    return false
  }
}

// Get all services
const servicesDir = path.join(serverDir, 'src', 'services')
const services = fs.readdirSync(servicesDir)
  .filter(dir => {
    const schemaPath = path.join(servicesDir, dir, `${dir}.schema.ts`)
    return fs.existsSync(schemaPath)
  })

console.log(`🚀 Removing query field restrictions from ${services.length} services...\n`)

let successCount = 0
services.forEach(serviceName => {
  if (fixQuerySchema(serviceName)) {
    successCount++
  }
})

console.log(`\n📊 Fixed: ${successCount}/${services.length} services`)
console.log('🎯 Query schemas now allow querying on any field!')
console.log('💡 No more "additionalProperty" validation errors on queries')
