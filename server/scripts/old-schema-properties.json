{"ai-chats": {"_id": "ObjectId", "subject": "ObjectId", "chatName": "object"}, "bank-accounts": {"_id": "ObjectId", "owner": "ObjectId", "nickname": "object"}, "bill-erasers": {"_id": "ObjectId", "person": "ObjectId", "plan": "ObjectId", "provider": "ObjectId", "session": "object"}, "bills": {"_id": "ObjectId", "to": "ObjectId", "toName": "object"}, "budgets": {"_id": "ObjectId", "amount": "object"}, "bundles": {"_id": "ObjectId", "provider": "ObjectId", "public": "object"}, "calendars": {"_id": "ObjectId", "name": "object"}, "cams": {"_id": "ObjectId", "person": "ObjectId", "comp": "ObjectId", "hireDate": "object"}, "caps": {"_id": "ObjectId", "subject": "ObjectId", "subjectService": "object"}, "care-accounts": {"_id": "ObjectId", "amount": "object"}, "cares": {"_id": "ObjectId", "person": "ObjectId", "org": "ObjectId", "plan": "ObjectId", "status": "object"}, "cats": {"_id": "ObjectId", "avatar": "any", "org": "ObjectId", "managers": "object"}, "challenges": {"_id": "ObjectId", "kind": "object"}, "change-logs": {"_id": "ObjectId", "service": "object"}, "claim-payments": {"_id": "ObjectId", "claim": "ObjectId", "visit": "ObjectId", "plan": "ObjectId", "person": "ObjectId", "org": "ObjectId", "patient": "ObjectId", "provider": "ObjectId", "coverage": "ObjectId", "enrollment": "ObjectId", "preventive": "object"}, "claim-reqs": {"_id": "ObjectId", "plan": "ObjectId", "org": "ObjectId", "patient": "ObjectId", "person": "ObjectId", "care": "ObjectId", "visit": "ObjectId", "claim": "ObjectId", "provider": "ObjectId", "practitioner": "ObjectId", "providerOrg": "ObjectId", "threads": "object"}, "claims": {"_id": "ObjectId", "visit": "ObjectId", "plan": "ObjectId", "patient": "ObjectId", "person": "ObjectId", "practitioner": "ObjectId", "provider": "ObjectId", "procedure": "ObjectId", "med": "ObjectId", "coverage": "ObjectId", "date": "object"}, "cobras": {"_id": "ObjectId", "enrollment": "ObjectId", "participant": "ObjectId", "household": "ObjectId", "spec": "ObjectId", "event_type": "object"}, "comps": {"_id": "ObjectId", "key": "object"}, "conditions": {"_id": "ObjectId", "standard": "object"}, "contracts": {"_id": "ObjectId", "public": "object"}, "coverages": {"_id": "ObjectId", "vectorIds": "object", "type": "any", "uploadIds": "object"}, "cross-sections": {"_id": "ObjectId", "hackId": "object"}, "doc-requests": {"_id": "ObjectId", "types": "object"}, "doc-templates": {"_id": "ObjectId", "name": "object"}, "drops": {"_id": "ObjectId", "tags": "object"}, "enrollments": {"_id": "ObjectId", "org": "ObjectId", "person": "ObjectId", "group": "ObjectId", "plan": "ObjectId", "idempotency_key": "object"}, "errs": {"_id": "ObjectId", "path": "object"}, "expenses": {"_id": "ObjectId", "amount": "object"}, "fb-res": {"_id": "ObjectId", "person": "ObjectId", "form": "ObjectId", "formData": "object"}, "fbs": {"_id": "ObjectId", "active": "object"}, "fingerprints": {"_id": "ObjectId", "turnstile": "object", "type": "any", "additionalProperties": "any", "properties": "object", "success": "object"}, "flow-charts": {"_id": "ObjectId", "name": "object"}, "funds": {"_id": "ObjectId", "name": "object"}, "funds-requests": {"_id": "ObjectId"}, "gps": {"_id": "ObjectId", "org": "ObjectId", "plan": "ObjectId", "runRequest": "object"}, "groups": {"_id": "ObjectId", "org": "ObjectId", "key": "object"}, "grp-mbrs": {"_id": "ObjectId", "group": "ObjectId", "person": "ObjectId", "org": "ObjectId", "mbrId": "object"}, "health-shares": {"_id": "ObjectId", "name": "object"}, "hosts": {"_id": "ObjectId", "appDefault": "object"}, "households": {"_id": "ObjectId", "person": "ObjectId", "filingAs": "object"}, "ims": {"_id": "ObjectId", "subject": "ObjectId", "subjectService": "object"}, "issues": {"_id": "ObjectId", "service": "object"}, "junk-drawers": {"_id": "ObjectId", "drawer": "object"}, "leads": {"_id": "ObjectId"}, "ledgers": {"_id": "ObjectId", "plan": "ObjectId", "org": "ObjectId", "person": "ObjectId", "planYear": "object"}, "logins": {"_id": "ObjectId", "name": "object"}, "markets": {"_id": "ObjectId", "name": "object"}, "mbrs": {"_id": "ObjectId", "coverage": "ObjectId", "person": "ObjectId", "enrollment": "ObjectId", "plan": "ObjectId", "provider": "ObjectId", "pm": "ObjectId", "inactive": "object"}, "meds": {"_id": "ObjectId", "standard": "object"}, "networks": {"_id": "ObjectId", "avatar": "any", "access": "object"}, "offers": {"_id": "ObjectId", "plan": "ObjectId", "contract": "ObjectId", "role": "object"}, "orgs": {"_id": "ObjectId", "bankAccounts": "object", "type": "any", "patternProperties": "object", "properties": "object", "name": "object"}, "passkeys": {"_id": "ObjectId", "login": "ObjectId", "rpID": "object"}, "pings": {"_id": "ObjectId", "subject": "ObjectId", "subjectService": "object"}, "plan-docs": {"_id": "ObjectId", "plan": "ObjectId", "smb": "object"}, "plans": {"_id": "ObjectId", "org": "ObjectId", "parent": "ObjectId", "doc": "ObjectId", "spd": "ObjectId", "ale": "object"}, "ppls": {"_id": "ObjectId", "name": "object"}, "practitioners": {"_id": "ObjectId", "person": "ObjectId", "avatar": "any", "firstName": "object"}, "price-estimates": {"_id": "ObjectId", "alts": "object"}, "prices": {"_id": "ObjectId", "provider": "ObjectId", "bundle": "ObjectId", "source": "object"}, "procedures": {"_id": "ObjectId", "standard": "object"}, "providers": {"_id": "ObjectId", "org": "ObjectId", "avatar": "any", "name": "object"}, "rates": {"_id": "ObjectId", "coverage": "ObjectId", "state": "object"}, "refs": {"_id": "ObjectId", "name": "object"}, "reqs": {"_id": "ObjectId", "fingerprint": "object"}, "sales-taxes": {"_id": "ObjectId", "country": "object"}, "se-plans": {"_id": "ObjectId", "org": "ObjectId", "public": "object"}, "shops": {"_id": "ObjectId", "person": "ObjectId", "plan": "ObjectId", "enrollment": "ObjectId", "plan_coverage": "ObjectId", "aiChatCount": "object"}, "specs": {"_id": "ObjectId", "org": "ObjectId", "plan": "ObjectId", "planYear": "object"}, "teams": {"_id": "ObjectId", "name": "object"}, "threads": {"_id": "ObjectId", "upVotes": "object"}, "uploads": {"_id": "ObjectId", "name": "object"}, "visits": {"_id": "ObjectId", "provider": "ObjectId", "patient": "ObjectId", "person": "ObjectId", "care": "ObjectId", "plan": "ObjectId", "preventive": "object"}, "wallets": {"_id": "ObjectId", "owner": "ObjectId", "ownerService": "object"}}