#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function addMissingFields() {
  // Load the schema issues
  const issuesPath = path.join(serverDir, 'scripts', 'schema-issues.json')
  if (!fs.existsSync(issuesPath)) {
    console.log('❌ schema-issues.json not found. Run compare-schemas.js first.')
    return
  }
  
  const issues = JSON.parse(fs.readFileSync(issuesPath, 'utf8'))
  
  // Filter to only services with missing keys
  const servicesWithMissingKeys = issues.filter(issue => issue.missing.length > 0)
  
  console.log(`🔧 Adding missing fields to ${servicesWithMissingKeys.length} services...\n`)
  
  let fixedCount = 0
  
  for (const issue of servicesWithMissingKeys) {
    const serviceName = issue.service
    const missingKeys = issue.missing
    const camelName = toCamelCase(serviceName)
    
    console.log(`🔧 ${serviceName}: Adding ${missingKeys.length} missing fields: ${missingKeys.join(', ')}`)
    
    try {
      const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
      let content = fs.readFileSync(schemaPath, 'utf8')
      
      // Find the main schema definition
      const schemaPattern = new RegExp(`(export const ${camelName}Schema = Type\\.Object\\({[\\s\\S]*?)(}\\s*,\\s*{[\\s\\S]*?}\\))`)
      const schemaMatch = content.match(schemaPattern)
      
      if (schemaMatch) {
        const beforeClosing = schemaMatch[1]
        const afterClosing = schemaMatch[2]
        
        // Add missing fields before the closing brace
        const missingFieldsStr = missingKeys.map(key => {
          // Determine appropriate TypeBox type based on field name patterns
          if (key.includes('Id') || key === 'person' || key === 'org' || key === 'plan' || key === 'provider' || key === 'enrollment' || key === 'household' || key === 'coverage' || key === 'group') {
            return `  ${key}: Type.Optional(ObjectIdSchema()),`
          } else if (key === 'public' || key === 'active' || key === 'inactive' || key === 'success') {
            return `  ${key}: Type.Optional(Type.Boolean()),`
          } else if (key.includes('Date') || key.includes('At')) {
            return `  ${key}: Type.Optional(Type.Any()),`
          } else if (key.includes('Count') || key === 'state') {
            return `  ${key}: Type.Optional(Type.Number()),`
          } else if (key === 'tags' || key === 'properties') {
            return `  ${key}: Type.Optional(Type.Array(Type.String())),`
          } else {
            return `  ${key}: Type.Optional(Type.String()),`
          }
        }).join('\n')
        
        // Insert missing fields before the closing brace
        const newSchemaContent = beforeClosing + ',\n  // Missing fields from old schema\n' + missingFieldsStr + '\n' + afterClosing
        
        content = content.replace(schemaMatch[0], newSchemaContent)
        
        fs.writeFileSync(schemaPath, content)
        console.log(`✅ ${serviceName}: Added ${missingKeys.length} missing fields`)
        fixedCount++
        
      } else {
        console.log(`❌ ${serviceName}: Could not find schema pattern`)
      }
      
    } catch (error) {
      console.log(`❌ ${serviceName}: Error adding fields - ${error.message}`)
    }
  }
  
  console.log(`\n📊 Results:`)
  console.log(`✅ Fixed: ${fixedCount}/${servicesWithMissingKeys.length} services`)
  console.log(`🎯 Missing service-specific fields have been added back!`)
  
  if (fixedCount > 0) {
    console.log(`\n🔍 Run compare-schemas.js again to verify the fixes`)
  }
}

// Run the fix
addMissingFields()
