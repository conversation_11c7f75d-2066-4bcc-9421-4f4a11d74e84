[{"service": "ai-chats", "missing": [], "extra": ["chatId", "person", "chats", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 14}, {"service": "bank-accounts", "missing": [], "extra": ["name", "accountNumber", "routingNumber", "accountType", "bankName", "verified", "primary", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 19}, {"service": "bill-erasers", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 5, "newCount": 19}, {"service": "bills", "missing": [], "extra": ["account", "person", "org", "bill<PERSON><PERSON><PERSON>", "invoiceNumber", "description", "amount", "subtotal", "tax", "fees", "total", "currency", "dueDate", "issueDate", "paidDate", "status", "category", "tags", "lineItems", "quantity", "unitPrice"], "oldCount": 3, "newCount": 24}, {"service": "budgets", "missing": [], "extra": ["account", "name", "spent", "period", "category", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "bundles", "missing": [], "extra": ["name", "description", "type", "category", "plans", "coverages", "procedures", "medications", "services", "price", "discount", "discountType", "effectiveDate", "terminationDate", "conditions", "eligibility", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 28}, {"service": "calendars", "missing": [], "extra": ["description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 15}, {"service": "cams", "missing": [], "extra": ["name", "description", "type", "url", "status", "settings", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 4, "newCount": 19}, {"service": "caps", "missing": [], "extra": ["name", "description", "type", "limit", "used", "remaining", "period", "resetDate", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 20}, {"service": "care-accounts", "missing": [], "extra": ["approvers", "assigned_amount", "assigned_recurs", "budgets", "connect_id", "stripe_id", "moov_id", "wallet_id", "last4", "lastInc", "lastSync", "managers", "members", "ramp_whitelist", "mcc_whitelist", "mcc_blacklist", "name", "owner", "recurs", "runSync", "status", "statusNote", "syncHistory", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 33}, {"service": "cares", "missing": [], "extra": ["patient", "provider", "practitioner", "name", "description", "type", "category", "priority", "startDate", "endDate", "goals", "interventions", "outcomes", "notes", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 5, "newCount": 28}, {"service": "cats", "missing": [], "extra": ["name", "description", "type", "category", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 4, "newCount": 18}, {"service": "challenges", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "change-logs", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "claim-payments", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 11, "newCount": 25}, {"service": "claim-reqs", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 12, "newCount": 26}, {"service": "claims", "missing": [], "extra": ["misc", "enteredBy", "log", "category", "description", "notes", "preventive", "adj", "paid", "paidAt", "paidBy", "paidMethod", "paidNotes", "amount", "subtotal", "total", "qty", "balanceSyncedAt", "balance", "adjHistory", "taxes", "fees", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 11, "newCount": 41}, {"service": "cobras", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 6, "newCount": 20}, {"service": "comps", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "conditions", "missing": [], "extra": ["name", "description", "code", "codeSystem", "category", "severity", "chronic", "symptoms", "treatments", "medications", "procedures", "relatedConditions", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 23}, {"service": "contracts", "missing": [], "extra": ["org", "name", "type", "category", "parties", "plans", "coverages", "networks", "providers", "effectiveDate", "terminationDate", "renewalDate", "autoRenew", "terms", "conditions", "documents", "signatures", "status", "value", "currency", "paymentTerms", "penalties", "amendments", "date", "description", "document"], "oldCount": 2, "newCount": 28}, {"service": "coverages", "missing": [], "extra": ["createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 4, "newCount": 12}, {"service": "cross-sections", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "doc-requests", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "doc-templates", "missing": [], "extra": ["description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 15}, {"service": "drops", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "enrollments", "missing": [], "extra": ["coverage", "household", "effectiveDate", "terminationDate", "status", "premium", "subsidyAmount", "employerContribution", "deductibleMet", "oopMet", "mandate", "notes", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 6, "newCount": 27}, {"service": "errs", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "expenses", "missing": [], "extra": ["account", "category", "vendor", "description", "date", "receipt", "approved", "approvedBy", "reimbursed", "reimbursedAt", "tags", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 22}, {"service": "fb-res", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 4, "newCount": 18}, {"service": "fbs", "missing": [], "extra": ["name", "description", "type", "status", "data", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 15}, {"service": "fingerprints", "missing": [], "extra": ["hash", "userAgent", "ip", "browser", "os", "device", "screen", "timezone", "language", "plugins", "fonts", "canvas", "webgl", "audio", "lastSeen", "visits", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 6, "newCount": 31}, {"service": "flow-charts", "missing": [], "extra": ["description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 15}, {"service": "funds", "missing": [], "extra": ["account", "amount", "source", "purpose", "description", "date", "type", "category", "reference", "allocated", "allocatedTo", "restrictions", "expiresAt", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 24}, {"service": "funds-requests", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 1, "newCount": 15}, {"service": "gps", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 4, "newCount": 18}, {"service": "groups", "missing": [], "extra": ["name", "description", "type", "category", "members", "managers", "plans", "coverages", "enrollments", "effectiveDate", "terminationDate", "renewalDate", "minimumParticipation", "currentParticipation", "premiumContribution", "<PERSON><PERSON><PERSON><PERSON>", "eligibilityRules", "settings", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 30}, {"service": "grp-mbrs", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 5, "newCount": 19}, {"service": "health-shares", "missing": [], "extra": ["description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 15}, {"service": "hosts", "missing": [], "extra": ["name", "hostname", "ip", "port", "protocol", "status", "<PERSON><PERSON><PERSON><PERSON>", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 18}, {"service": "households", "missing": [], "extra": ["name", "head", "members", "dependents", "address", "income", "size", "type", "status", "taxFilingStatus", "poverty", "fpl", "magi", "aptc", "csr", "medicaidEligible", "chipEligible", "employerCoverage", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 29}, {"service": "ims", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 17}, {"service": "issues", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "junk-drawers", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "leads", "missing": [], "extra": ["firstName", "lastName", "email", "phone", "address", "dateOfBirth", "gender", "household", "income", "householdSize", "source", "campaign", "referrer", "status", "stage", "priority", "assignedTo", "notes", "tags", "interests", "preferences", "lastContact", "nextFollowUp", "converted", "convertedAt", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 1, "newCount": 35}, {"service": "ledgers", "missing": [], "extra": ["account", "amount", "type", "category", "reference", "referenceType", "description", "date", "balance", "runningBalance", "reconciled", "reconciled<PERSON><PERSON>", "tags", "metadata", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 5, "newCount": 28}, {"service": "logins", "missing": [], "extra": ["email", "did", "password", "phone", "person", "fingerprint", "verified", "isVerified", "emailVerified", "phoneVerified", "twoFactorEnabled", "lastLogin", "loginCount", "failedAttempts", "lockedUntil", "resetToken", "resetTokenExpires", "verificationToken", "verificationTokenExpires", "roles", "permissions", "orgs", "groups", "teams", "preferences", "settings", "active", "suspended", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 38}, {"service": "markets", "missing": [], "extra": ["state", "region", "type", "category", "plans", "coverages", "providers", "networks", "serviceArea", "counties", "zipCodes", "effectiveDate", "terminationDate", "openEnrollmentStart", "openEnrollmentEnd", "specialEnrollmentPeriods", "start", "end", "reason"], "oldCount": 2, "newCount": 21}, {"service": "mbrs", "missing": [], "extra": ["firstName", "lastName", "middleName", "suffix", "prefix", "nickname", "dateOfBirth", "gender", "ssn", "email", "phone", "address", "emergencyContact", "name", "relationship"], "oldCount": 8, "newCount": 23}, {"service": "meds", "missing": [], "extra": ["name", "description", "genericName", "brandName", "ndc", "rxcui", "strength", "dosageForm", "route", "manufacturer", "category", "class", "controlled", "schedule", "indications", "contraindications", "sideEffects", "interactions", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 29}, {"service": "networks", "missing": [], "extra": ["org", "name", "description", "type", "category", "providers", "facilities", "practitioners", "plans", "coverages", "serviceArea", "tier", "contractNumber", "effectiveDate", "terminationDate", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 27}, {"service": "offers", "missing": [], "extra": ["coverage", "name", "description", "price", "originalPrice", "discount", "discountType", "validFrom", "validTo", "conditions", "eligibility", "maxUses", "currentUses", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 4, "newCount": 26}, {"service": "orgs", "missing": [], "extra": ["legalName", "dba", "industry", "description", "website", "email", "phone", "address", "logo", "ein", "taxId", "licenses", "certifications", "parent", "subsidiaries", "partners", "employees", "owners", "id", "role", "percentage"], "oldCount": 6, "newCount": 27}, {"service": "passkeys", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 17}, {"service": "pings", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 17}, {"service": "plan-docs", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 17}, {"service": "plans", "missing": [], "extra": ["name", "description", "type", "category", "coverage", "coverages", "group", "effectiveDate", "terminationDate", "renewalDate", "premium", "employerContribution", "employeeContribution", "deductible", "outOfPocketMax", "copay", "coinsurance", "network", "networks", "providers", "formulary", "benefits", "exclusions", "limitations", "documents", "brochure", "summaryOfBenefits", "taxes", "active", "public", "template", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 6, "newCount": 45}, {"service": "ppls", "missing": [], "extra": ["firstName", "lastName", "middleName", "email", "phone", "address", "dateOfBirth", "gender", "ssn", "login", "household", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 22}, {"service": "practitioners", "missing": [], "extra": ["lastName", "name", "name_prefix", "gender", "credential", "auto_created", "credentials", "phone", "soleProp", "phones", "email", "npi_date", "npi_update", "npi_status", "npi", "license", "licenses", "cities", "license_states", "taxonomy1", "taxonomy2", "taxonomy3", "providers", "createdAt", "updatedAt", "deleted"], "oldCount": 4, "newCount": 30}, {"service": "price-estimates", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "prices", "missing": [], "extra": ["name", "description", "amount", "currency", "type", "category", "effectiveDate", "expirationDate", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 4, "newCount": 21}, {"service": "procedures", "missing": [], "extra": ["level", "subLevel", "category", "pcc", "parent", "code", "codes", "name", "names", "<PERSON><PERSON><PERSON>", "layDescription", "descriptions", "description", "synonyms", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 24}, {"service": "providers", "missing": [], "extra": ["description", "phone", "phones", "email", "emails", "website", "address", "addresses", "openHours", "timezone", "video", "npi", "taxId", "taxonomy", "taxonomy1", "taxonomy2", "taxonomy3", "specialties", "networks", "practitioners", "locations", "services", "features", "public", "verified", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 4, "newCount": 38}, {"service": "rates", "missing": [], "extra": ["plan", "market", "network", "<PERSON><PERSON><PERSON><PERSON>", "min", "max"], "oldCount": 3, "newCount": 9}, {"service": "refs", "missing": [], "extra": ["description", "type", "category", "reference", "referenceType", "value", "metadata", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 18}, {"service": "reqs", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "sales-taxes", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 16}, {"service": "se-plans", "missing": [], "extra": ["name", "description", "type", "status", "data", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 3, "newCount": 17}, {"service": "shops", "missing": [], "extra": ["org", "name", "description", "plans", "coverages", "market", "region", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 6, "newCount": 22}, {"service": "specs", "missing": [], "extra": ["name", "description", "type", "category", "properties", "version", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 4, "newCount": 19}, {"service": "teams", "missing": [], "extra": ["org", "description", "type", "department", "members", "managers", "leads", "permissions", "roles", "projects", "budget", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 22}, {"service": "threads", "missing": [], "extra": ["subject", "description", "participants", "messages", "status", "priority", "category", "tags", "lastMessage", "lastActivity", "archived", "active", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 2, "newCount": 22}, {"service": "uploads", "missing": [], "extra": ["filename", "originalName", "mimetype", "size", "path", "url", "storage", "bucket", "key", "uploadedBy", "uploadedAt", "tags", "metadata", "processed", "virus_scan", "status", "result", "scannedAt"], "oldCount": 2, "newCount": 20}, {"service": "visits", "missing": [], "extra": ["practitioner", "date", "scheduledDate", "type", "status", "reason", "diagnosis", "notes", "enteredBy", "duration", "location", "room", "vitals", "procedures", "medications", "followUp", "referrals", "attachments", "billing", "insurance", "copay", "coinsurance", "deductible", "total", "paid", "balance", "createdAt", "updatedAt", "created<PERSON>y", "updatedBy", "env", "host", "ref", "changeLog"], "oldCount": 7, "newCount": 41}, {"service": "wallets", "missing": [], "extra": ["name", "type", "currency", "balance", "available", "pending", "reserved", "creditLimit", "interestRate", "fees", "transactions", "linkedAccounts", "paymentMethods", "autoReload", "enabled", "threshold", "amount", "source"], "oldCount": 3, "newCount": 21}]